{"recommendations": ["esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "expo.vscode-expo-tools", "ms-vscode.vscode-react-native", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-vscode.vscode-eslint", "gruntfuggly.todo-tree", "streetsidesoftware.code-spell-checker"]}