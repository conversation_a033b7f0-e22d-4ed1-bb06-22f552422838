{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "emmet.includeLanguages": {"typescript": "typescriptreact", "javascript": "javascriptreact"}, "files.associations": {"*.tsx": "typescriptreact", "*.ts": "typescript"}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.expo": true, "**/ios": true, "**/android": true}, "files.exclude": {"**/.expo": true, "**/.expo-shared": true, "**/node_modules": true}}