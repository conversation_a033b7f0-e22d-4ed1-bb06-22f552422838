{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "React", "_reactNative", "_reactNativeSvg", "_resolveAssetUri", "_css", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_extends", "assign", "bind", "arguments", "length", "apply", "getUriFromSource", "source", "resolvedAssetSource", "Platform", "OS", "resolveAssetUri", "Image", "resolveAssetSource", "uri", "loadLocalRawResourceDefault", "fetchText", "isUriAnAndroidResourceIdentifier", "indexOf", "loadAndroidRawResource", "RNSVGRenderableModule", "getRawResource", "console", "error", "loadLocalRawResourceAndroid", "loadLocalRawResource", "exports", "LocalSvg", "props", "asset", "rest", "xml", "setXml", "useState", "useEffect", "then", "createElement", "SvgCss", "WithLocalSvg", "Component", "state", "componentDidMount", "load", "componentDidUpdate", "prevProps", "setState", "render", "SvgWithCss", "override", "_default"], "sourceRoot": "../../../src", "sources": ["css/LocalSvg.tsx"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AAA+B,IAAAC,KAAA,GAAAH,MAAA;AAE/B,IAAAI,YAAA,GAAAF,OAAA;AACA,IAAAG,eAAA,GAAAH,OAAA;AACA,IAAAI,gBAAA,GAAAJ,OAAA;AACA,IAAAK,IAAA,GAAAL,OAAA;AAA2C,SAAAM,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAW,SAAA,WAAAA,QAAA,GAAAR,MAAA,CAAAS,MAAA,GAAAT,MAAA,CAAAS,MAAA,CAAAC,IAAA,eAAAb,CAAA,aAAAR,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAE,CAAA,IAAAC,CAAA,OAAAY,cAAA,CAAAC,IAAA,CAAAb,CAAA,EAAAD,CAAA,MAAAM,CAAA,CAAAN,CAAA,IAAAC,CAAA,CAAAD,CAAA,aAAAM,CAAA,KAAAW,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAEpC,SAASG,gBAAgBA,CAACC,MAA2B,EAAE;EAC5D,MAAMC,mBAAmB,GACvBC,qBAAQ,CAACC,EAAE,KAAK,KAAK,GACjB,IAAAC,gCAAe,EAACJ,MAAM,CAAC,GACvBK,kBAAK,CAACC,kBAAkB,CAACN,MAAM,CAAC;EACtC,OAAOC,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEM,GAAG;AACjC;AAEO,SAASC,2BAA2BA,CAACR,MAA2B,EAAE;EACvE,MAAMO,GAAG,GAAGR,gBAAgB,CAACC,MAAM,CAAC;EACpC,OAAO,IAAAS,yBAAS,EAACF,GAAG,CAAC;AACvB;AAEO,SAASG,gCAAgCA,CAACH,GAAY,EAAE;EAC7D,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC1D;AAEO,eAAeC,sBAAsBA,CAACL,GAAW,EAAE;EACxD,IAAI;IACF;IACA,MAAMM,qBAA0B;IAC9B;IACA;IACA9C,OAAO,CAAC,qCAAqC,CAAC,CAACY,OAAO;IACxD,OAAO,MAAMkC,qBAAqB,CAACC,cAAc,CAACP,GAAG,CAAC;EACxD,CAAC,CAAC,OAAOjC,CAAC,EAAE;IACVyC,OAAO,CAACC,KAAK,CACX,mFAAmF,EACnF1C,CACF,CAAC;IACD,OAAO,IAAI;EACb;AACF;AAEO,SAAS2C,2BAA2BA,CAACjB,MAA2B,EAAE;EACvE,MAAMO,GAAG,GAAGR,gBAAgB,CAACC,MAAM,CAAC;EACpC,IAAIO,GAAG,IAAIG,gCAAgC,CAACH,GAAG,CAAC,EAAE;IAChD,OAAOK,sBAAsB,CAACL,GAAG,CAAC;EACpC,CAAC,MAAM;IACL,OAAO,IAAAE,yBAAS,EAACF,GAAG,CAAC;EACvB;AACF;AAEO,MAAMW,oBAAoB,GAAAC,OAAA,CAAAD,oBAAA,GAC/BhB,qBAAQ,CAACC,EAAE,KAAK,SAAS,GACrBK,2BAA2B,GAC3BS,2BAA2B;AAQ1B,SAASG,QAAQA,CAACC,KAAiB,EAAE;EAC1C,MAAM;IAAEC,KAAK;IAAE,GAAGC;EAAK,CAAC,GAAGF,KAAK;EAChC,MAAM,CAACG,GAAG,EAAEC,MAAM,CAAC,GAAG,IAAAC,eAAQ,EAAgB,IAAI,CAAC;EACnD,IAAAC,gBAAS,EAAC,MAAM;IACdT,oBAAoB,CAACI,KAAK,CAAC,CAACM,IAAI,CAACH,MAAM,CAAC;EAC1C,CAAC,EAAE,CAACH,KAAK,CAAC,CAAC;EACX,oBAAOtD,KAAA,CAAA6D,aAAA,CAACzD,IAAA,CAAA0D,MAAM,EAAArC,QAAA;IAAC+B,GAAG,EAAEA;EAAI,GAAKD,IAAI,CAAG,CAAC;AACvC;AAEO,MAAMQ,YAAY,SAASC,gBAAS,CAAyB;EAClEC,KAAK,GAAG;IAAET,GAAG,EAAE;EAAK,CAAC;EACrBU,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACC,IAAI,CAAC,IAAI,CAACd,KAAK,CAACC,KAAK,CAAC;EAC7B;EAEAc,kBAAkBA,CAACC,SAAyC,EAAE;IAC5D,MAAM;MAAEf;IAAM,CAAC,GAAG,IAAI,CAACD,KAAK;IAC5B,IAAIC,KAAK,KAAKe,SAAS,CAACf,KAAK,EAAE;MAC7B,IAAI,CAACa,IAAI,CAACb,KAAK,CAAC;IAClB;EACF;EAEA,MAAMa,IAAIA,CAACb,KAA0B,EAAE;IACrC,IAAI;MACF,IAAI,CAACgB,QAAQ,CAAC;QAAEd,GAAG,EAAEF,KAAK,GAAG,MAAMJ,oBAAoB,CAACI,KAAK,CAAC,GAAG;MAAK,CAAC,CAAC;IAC1E,CAAC,CAAC,OAAOhD,CAAC,EAAE;MACVyC,OAAO,CAACC,KAAK,CAAC1C,CAAC,CAAC;IAClB;EACF;EAEAiE,MAAMA,CAAA,EAAG;IACP,MAAM;MACJlB,KAAK;MACLY,KAAK,EAAE;QAAET;MAAI;IACf,CAAC,GAAG,IAAI;IACR,oBAAOxD,KAAA,CAAA6D,aAAA,CAACzD,IAAA,CAAAoE,UAAU;MAAChB,GAAG,EAAEA,GAAI;MAACiB,QAAQ,EAAEpB;IAAM,CAAE,CAAC;EAClD;AACF;AAACF,OAAA,CAAAY,YAAA,GAAAA,YAAA;AAAA,IAAAW,QAAA,GAAAvB,OAAA,CAAAxC,OAAA,GAEcyC,QAAQ", "ignoreList": []}