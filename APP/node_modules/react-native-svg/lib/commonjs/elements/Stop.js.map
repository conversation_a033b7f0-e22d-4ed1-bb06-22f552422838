{"version": 3, "names": ["_react", "require", "Stop", "Component", "displayName", "setNativeProps", "parent", "props", "forceUpdate", "render", "exports", "default"], "sourceRoot": "../../../src", "sources": ["elements/Stop.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAWe,MAAMC,IAAI,SAASC,gBAAS,CAAY;EACrD,OAAOC,WAAW,GAAG,MAAM;EAE3BC,cAAc,GAAGA,CAAA,KAAM;IACrB,MAAM;MAAEC;IAAO,CAAC,GAAG,IAAI,CAACC,KAAK;IAC7B,IAAID,MAAM,EAAE;MACVA,MAAM,CAACE,WAAW,CAAC,CAAC;IACtB;EACF,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI;EACb;AACF;AAACC,OAAA,CAAAC,OAAA,GAAAT,IAAA", "ignoreList": []}