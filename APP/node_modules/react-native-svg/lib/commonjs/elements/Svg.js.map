{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_extractResponder", "_interopRequireDefault", "_extractViewBox", "_Shape", "_G", "_AndroidSvgViewNativeComponent", "_IOSSvgViewNativeComponent", "_extractOpacity", "_extractTransform", "e", "__esModule", "default", "_getRequireWildcardCache", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_extends", "assign", "bind", "arguments", "length", "apply", "styles", "StyleSheet", "create", "svg", "backgroundColor", "borderWidth", "defaultStyle", "Svg", "<PERSON><PERSON><PERSON>", "displayName", "defaultProps", "preserveAspectRatio", "measureInWindow", "callback", "root", "measure", "measureLayout", "relativeToNativeNode", "onSuccess", "onFail", "setNativeProps", "props", "toDataURL", "options", "handle", "findNodeHandle", "RNSVGSvgViewModule", "render", "style", "opacity", "viewBox", "children", "onLayout", "extracted", "stylesAndProps", "Array", "isArray", "width", "height", "focusable", "transform", "font", "fill", "fillOpacity", "fillRule", "stroke", "strokeWidth", "strokeOpacity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "strokeLinecap", "strokeLinejoin", "strokeMiterlimit", "undefined", "Boolean", "rootStyles", "push", "override", "overrideStyles", "o", "extractOpacity", "NaN", "isNaN", "w", "parseInt", "h", "doNotParseWidth", "doNotParseHeight", "flex", "bb<PERSON><PERSON><PERSON>", "bbHeight", "extractResponder", "gStyle", "flatten", "extractTransformSvgView", "RNSVGSvg", "Platform", "OS", "RNSVGSvgAndroid", "RNSVGSvgIOS", "createElement", "ref", "refMethod", "extractViewBox", "exports"], "sourceRoot": "../../../src", "sources": ["elements/Svg.tsx"], "mappings": ";;;;;;AACA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AAUA,IAAAC,YAAA,GAAAD,OAAA;AAOA,IAAAE,iBAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,eAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,MAAA,GAAAF,sBAAA,CAAAH,OAAA;AAEA,IAAAM,EAAA,GAAAH,sBAAA,CAAAH,OAAA;AACA,IAAAO,8BAAA,GAAAJ,sBAAA,CAAAH,OAAA;AACA,IAAAQ,0BAAA,GAAAL,sBAAA,CAAAH,OAAA;AAEA,IAAAS,eAAA,GAAAN,sBAAA,CAAAH,OAAA;AACA,IAAAU,iBAAA,GAAAV,OAAA;AAA0E,SAAAG,uBAAAQ,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,yBAAAH,CAAA,6BAAAI,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAD,wBAAA,YAAAA,CAAAH,CAAA,WAAAA,CAAA,GAAAM,CAAA,GAAAD,CAAA,KAAAL,CAAA;AAAA,SAAAZ,wBAAAY,CAAA,EAAAK,CAAA,SAAAA,CAAA,IAAAL,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAE,OAAA,EAAAF,CAAA,QAAAM,CAAA,GAAAH,wBAAA,CAAAE,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAP,CAAA,UAAAM,CAAA,CAAAE,GAAA,CAAAR,CAAA,OAAAS,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAf,CAAA,oBAAAe,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAe,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAd,CAAA,EAAAe,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAf,CAAA,CAAAe,CAAA,YAAAN,CAAA,CAAAP,OAAA,GAAAF,CAAA,EAAAM,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAnB,CAAA,EAAAS,CAAA,GAAAA,CAAA;AAAA,SAAAW,SAAA,WAAAA,QAAA,GAAAR,MAAA,CAAAS,MAAA,GAAAT,MAAA,CAAAS,MAAA,CAAAC,IAAA,eAAAb,CAAA,aAAAT,CAAA,MAAAA,CAAA,GAAAuB,SAAA,CAAAC,MAAA,EAAAxB,CAAA,UAAAM,CAAA,GAAAiB,SAAA,CAAAvB,CAAA,YAAAK,CAAA,IAAAC,CAAA,OAAAU,cAAA,CAAAC,IAAA,CAAAX,CAAA,EAAAD,CAAA,MAAAI,CAAA,CAAAJ,CAAA,IAAAC,CAAA,CAAAD,CAAA,aAAAI,CAAA,KAAAW,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAG1E,MAAMG,MAAM,GAAGC,uBAAU,CAACC,MAAM,CAAC;EAC/BC,GAAG,EAAE;IACHC,eAAe,EAAE,aAAa;IAC9BC,WAAW,EAAE;EACf;AACF,CAAC,CAAC;AACF,MAAMC,YAAY,GAAGN,MAAM,CAACG,GAAG;AAWhB,MAAMI,GAAG,SAASC,cAAK,CAAW;EAC/C,OAAOC,WAAW,GAAG,KAAK;EAE1B,OAAOC,YAAY,GAAG;IACpBC,mBAAmB,EAAE;EACvB,CAAC;EAEDC,eAAe,GAAIC,QAA0C,IAAK;IAChE,MAAM;MAAEC;IAAK,CAAC,GAAG,IAAI;IACrBA,IAAI,IAAIA,IAAI,CAACF,eAAe,CAACC,QAAQ,CAAC;EACxC,CAAC;EAEDE,OAAO,GAAIF,QAAkC,IAAK;IAChD,MAAM;MAAEC;IAAK,CAAC,GAAG,IAAI;IACrBA,IAAI,IAAIA,IAAI,CAACC,OAAO,CAACF,QAAQ,CAAC;EAChC,CAAC;EAEDG,aAAa,GAAGA,CACdC,oBAA4B,EAC5BC,SAAyC,EACzCC,MAAkB,KACf;IACH,MAAM;MAAEL;IAAK,CAAC,GAAG,IAAI;IACrBA,IAAI,IAAIA,IAAI,CAACE,aAAa,CAACC,oBAAoB,EAAEC,SAAS,EAAEC,MAAM,CAAC;EACrE,CAAC;EAEDC,cAAc,GACZC,KAGC,IACE;IACH,MAAM;MAAEP;IAAK,CAAC,GAAG,IAAI;IACrBA,IAAI,IAAIA,IAAI,CAACM,cAAc,CAACC,KAAK,CAAC;EACpC,CAAC;EAEDC,SAAS,GAAGA,CAACT,QAAkC,EAAEU,OAAgB,KAAK;IACpE,IAAI,CAACV,QAAQ,EAAE;MACb;IACF;IACA,MAAMW,MAAM,GAAG,IAAAC,2BAAc,EAAC,IAAI,CAACX,IAAiB,CAAC;IACrD,MAAMY,kBAAwB;IAC5B;IACA/D,OAAO,CAAC,+BAA+B,CAAC,CAACa,OAAO;IAClDkD,kBAAkB,CAACJ,SAAS,CAACE,MAAM,EAAED,OAAO,EAAEV,QAAQ,CAAC;EACzD,CAAC;EAEDc,MAAMA,CAAA,EAAG;IACP,MAAM;MACJC,KAAK;MACLC,OAAO;MACPC,OAAO;MACPC,QAAQ;MACRC,QAAQ;MACRrB,mBAAmB;MACnB,GAAGsB;IACL,CAAC,GAAG,IAAI,CAACZ,KAAK;IACd,MAAMa,cAAc,GAAG;MACrB,IAAIC,KAAK,CAACC,OAAO,CAACR,KAAK,CAAC,GAAG1C,MAAM,CAACS,MAAM,CAAC,CAAC,CAAC,EAAE,GAAGiC,KAAK,CAAC,GAAGA,KAAK,CAAC;MAC/D,GAAGK;IACL,CAAC;IACD,IAAI;MACFI,KAAK;MACLC,MAAM;MACNC,SAAS;MACTC,SAAS;MAET;MACAC,IAAI;MACJC,IAAI;MACJC,WAAW;MACXC,QAAQ;MACRC,MAAM;MACNC,WAAW;MACXC,aAAa;MACbC,eAAe;MACfC,gBAAgB;MAChBC,aAAa;MACbC,cAAc;MACdC;IACF,CAAC,GAAGlB,cAAc;IAClB,IAAIG,KAAK,KAAKgB,SAAS,IAAIf,MAAM,KAAKe,SAAS,EAAE;MAC/ChB,KAAK,GAAGC,MAAM,GAAG,MAAM;IACzB;IAEA,MAAMjB,KAAqB,GAAGY,SAA2B;IACzDZ,KAAK,CAACkB,SAAS,GAAGe,OAAO,CAACf,SAAS,CAAC,IAAIA,SAAS,KAAK,OAAO;IAC7D,MAAMgB,UAAkC,GAAG,CAACjD,YAAY,CAAC;IAEzD,IAAIsB,KAAK,EAAE;MACT2B,UAAU,CAACC,IAAI,CAAC5B,KAAK,CAAC;IACxB;IAEA,IAAI6B,QAAQ,GAAG,KAAK;IACpB,MAAMC,cAAyB,GAAG,CAAC,CAAC;IACpC,MAAMC,CAAC,GAAG9B,OAAO,IAAI,IAAI,GAAG,IAAA+B,uBAAc,EAAC/B,OAAO,CAAC,GAAGgC,GAAG;IACzD,IAAI,CAACC,KAAK,CAACH,CAAC,CAAC,EAAE;MACbF,QAAQ,GAAG,IAAI;MACfC,cAAc,CAAC7B,OAAO,GAAG8B,CAAC;IAC5B;IAEA,IAAItB,KAAK,IAAIC,MAAM,EAAE;MACnBmB,QAAQ,GAAG,IAAI;MACf,MAAMM,CAAC,GAAGC,QAAQ,CAAC3B,KAAK,EAAE,EAAE,CAAC;MAC7B,MAAM4B,CAAC,GAAGD,QAAQ,CAAC1B,MAAM,EAAE,EAAE,CAAC;MAC9B,MAAM4B,eAAe,GAAGJ,KAAK,CAACC,CAAC,CAAC,IAAI1B,KAAK,CAACA,KAAK,CAACvC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG;MACnE,MAAMqE,gBAAgB,GAAGL,KAAK,CAACG,CAAC,CAAC,IAAI3B,MAAM,CAACA,MAAM,CAACxC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG;MACtE4D,cAAc,CAACrB,KAAK,GAAG6B,eAAe,GAAG7B,KAAK,GAAG0B,CAAC;MAClDL,cAAc,CAACpB,MAAM,GAAG6B,gBAAgB,GAAG7B,MAAM,GAAG2B,CAAC;MACrDP,cAAc,CAACU,IAAI,GAAG,CAAC;IACzB;IAEA,IAAIX,QAAQ,EAAE;MACZF,UAAU,CAACC,IAAI,CAACE,cAAc,CAAC;IACjC;IAEArC,KAAK,CAACO,KAAK,GAAG2B,UAAU,CAACzD,MAAM,GAAG,CAAC,GAAGyD,UAAU,GAAGjD,YAAY;IAE/D,IAAI+B,KAAK,IAAI,IAAI,EAAE;MACjBhB,KAAK,CAACgD,OAAO,GAAGhC,KAAK;IACvB;IACA,IAAIC,MAAM,IAAI,IAAI,EAAE;MAClBjB,KAAK,CAACiD,QAAQ,GAAGhC,MAAM;IACzB;IAEA,IAAAiC,yBAAgB,EAAClD,KAAK,EAAEA,KAAK,EAAE,IAA8B,CAAC;IAE9D,IAAIW,QAAQ,IAAI,IAAI,EAAE;MACpBX,KAAK,CAACW,QAAQ,GAAGA,QAAQ;IAC3B;IAEA,MAAMwC,MAAM,GAAGtF,MAAM,CAACS,MAAM,CAAC,CAAC,CAAC,EAAEM,uBAAU,CAACwE,OAAO,CAAC7C,KAAK,CAAC,CAAC;IAC3D,IAAIY,SAAS,EAAE;MACb,IAAIgC,MAAM,CAAChC,SAAS,EAAE;QACpBnB,KAAK,CAACmB,SAAS,GAAGgC,MAAM,CAAChC,SAAS;QAClCgC,MAAM,CAAChC,SAAS,GAAGa,SAAS;MAC9B;MACA;MACAhC,KAAK,CAACmB,SAAS,GAAG,IAAAkC,yCAAuB,EAACrD,KAAY,CAAC;IACzD;IAEA,MAAMsD,QAAQ,GAAGC,qBAAQ,CAACC,EAAE,KAAK,SAAS,GAAGC,sCAAe,GAAGC,kCAAW;IAE1E,oBACEtH,KAAA,CAAAuH,aAAA,CAACL,QAAQ,EAAAjF,QAAA,KACH2B,KAAK;MACT4D,GAAG,EAAGA,GAAG,IAAK,IAAI,CAACC,SAAS,CAACD,GAAmC;IAAE,GAC9D,IAAAE,uBAAc,EAAC;MAAErD,OAAO;MAAEnB;IAAoB,CAAC,CAAC,gBACpDlD,KAAA,CAAAuH,aAAA,CAAC/G,EAAA,CAAAO,OAAC;MAEEuD,QAAQ;MACRH,KAAK,EAAE4C,MAAM;MACb/B,IAAI;MACJC,IAAI;MACJC,WAAW;MACXC,QAAQ;MACRC,MAAM;MACNC,WAAW;MACXC,aAAa;MACbC,eAAe;MACfC,gBAAgB;MAChBC,aAAa;MACbC,cAAc;MACdC;IAAgB,CAEnB,CACO,CAAC;EAEf;AACF;AAACgC,OAAA,CAAA5G,OAAA,GAAA+B,GAAA", "ignoreList": []}