{"version": 3, "names": ["_reactNative", "require", "PRESS_RETENTION_OFFSET", "top", "left", "right", "bottom", "Mixin", "Touchable", "touchableHandleStartShouldSetResponder", "touchableHandleResponderTerminationRequest", "touchableHandleResponderGrant", "touchableHandleResponderMove", "touchableHandleResponderRelease", "touchableHandleResponderTerminate", "touchableGetInitialState", "SvgTouchableMixin", "e", "onStartShouldSetResponder", "props", "call", "onResponderTerminationRequest", "onResponderGrant", "onResponderMove", "onResponderRelease", "onResponderTerminate", "touchableHandlePress", "onPress", "touchableHandleActivePressIn", "onPressIn", "touchableHandleActivePressOut", "onPressOut", "touchableHandleLongPress", "onLongPress", "touchableGetPressRectOffset", "pressRetentionOffset", "touchableGetHitSlop", "hitSlop", "touchableGetHighlightDelayMS", "delayPressIn", "touchableGetLongPressDelayMS", "delayLongPress", "touchableGetPressOutDelayMS", "delayPressOut", "touchKeys", "Object", "keys", "touchVals", "map", "key", "numTouchKeys", "length", "_default", "target", "i", "val", "bind", "state", "exports", "default"], "sourceRoot": "../../../src", "sources": ["lib/SvgTouchableMixin.ts"], "mappings": ";;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AACA,MAAMC,sBAAsB,GAAG;EAAEC,GAAG,EAAE,EAAE;EAAEC,IAAI,EAAE,EAAE;EAAEC,KAAK,EAAE,EAAE;EAAEC,MAAM,EAAE;AAAG,CAAC;AAC3E;AACA,MAAM;EAAEC;AAAM,CAAC,GAAGC,sBAAS;AAC3B,MAAM;EACJC,sCAAsC;EACtCC,0CAA0C;EAC1CC,6BAA6B;EAC7BC,4BAA4B;EAC5BC,+BAA+B;EAC/BC,iCAAiC;EACjCC;AACF,CAAC,GAAGR,KAAK;AAET,MAAMS,iBAAiB,GAAG;EACxB,GAAGT,KAAK;EAERE,sCAAsCA,CAACQ,CAAwB,EAAE;IAC/D,MAAM;MAAEC;IAA0B,CAAC,GAAG,IAAI,CAACC,KAAK;IAChD,IAAID,yBAAyB,EAAE;MAC7B,OAAOA,yBAAyB,CAACD,CAAC,CAAC;IACrC,CAAC,MAAM;MACL,OAAOR,sCAAsC,CAACW,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC;IAC7D;EACF,CAAC;EAEDP,0CAA0CA,CAACO,CAAwB,EAAE;IACnE,MAAM;MAAEI;IAA8B,CAAC,GAAG,IAAI,CAACF,KAAK;IACpD,IAAIE,6BAA6B,EAAE;MACjC,OAAOA,6BAA6B,CAACJ,CAAC,CAAC;IACzC,CAAC,MAAM;MACL,OAAOP,0CAA0C,CAACU,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC;IACjE;EACF,CAAC;EAEDN,6BAA6BA,CAACM,CAAwB,EAAE;IACtD,MAAM;MAAEK;IAAiB,CAAC,GAAG,IAAI,CAACH,KAAK;IACvC,IAAIG,gBAAgB,EAAE;MACpB,OAAOA,gBAAgB,CAACL,CAAC,CAAC;IAC5B,CAAC,MAAM;MACL,OAAON,6BAA6B,CAACS,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC;IACpD;EACF,CAAC;EAEDL,4BAA4BA,CAACK,CAAwB,EAAE;IACrD,MAAM;MAAEM;IAAgB,CAAC,GAAG,IAAI,CAACJ,KAAK;IACtC,IAAII,eAAe,EAAE;MACnB,OAAOA,eAAe,CAACN,CAAC,CAAC;IAC3B,CAAC,MAAM;MACL,OAAOL,4BAA4B,CAACQ,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC;IACnD;EACF,CAAC;EAEDJ,+BAA+BA,CAACI,CAAwB,EAAE;IACxD,MAAM;MAAEO;IAAmB,CAAC,GAAG,IAAI,CAACL,KAAK;IACzC,IAAIK,kBAAkB,EAAE;MACtB,OAAOA,kBAAkB,CAACP,CAAC,CAAC;IAC9B,CAAC,MAAM;MACL,OAAOJ,+BAA+B,CAACO,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC;IACtD;EACF,CAAC;EAEDH,iCAAiCA,CAACG,CAAwB,EAAE;IAC1D,MAAM;MAAEQ;IAAqB,CAAC,GAAG,IAAI,CAACN,KAAK;IAC3C,IAAIM,oBAAoB,EAAE;MACxB,OAAOA,oBAAoB,CAACR,CAAC,CAAC;IAChC,CAAC,MAAM;MACL,OAAOH,iCAAiC,CAACM,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC;IACxD;EACF,CAAC;EAEDS,oBAAoBA,CAACT,CAAwB,EAAE;IAC7C,MAAM;MAAEU;IAAQ,CAAC,GAAG,IAAI,CAACR,KAAK;IAC9BQ,OAAO,IAAIA,OAAO,CAACV,CAAC,CAAC;EACvB,CAAC;EAEDW,4BAA4BA,CAACX,CAAwB,EAAE;IACrD,MAAM;MAAEY;IAAU,CAAC,GAAG,IAAI,CAACV,KAAK;IAChCU,SAAS,IAAIA,SAAS,CAACZ,CAAC,CAAC;EAC3B,CAAC;EAEDa,6BAA6BA,CAACb,CAAwB,EAAE;IACtD,MAAM;MAAEc;IAAW,CAAC,GAAG,IAAI,CAACZ,KAAK;IACjCY,UAAU,IAAIA,UAAU,CAACd,CAAC,CAAC;EAC7B,CAAC;EAEDe,wBAAwBA,CAACf,CAAwB,EAAE;IACjD,MAAM;MAAEgB;IAAY,CAAC,GAAG,IAAI,CAACd,KAAK;IAClCc,WAAW,IAAIA,WAAW,CAAChB,CAAC,CAAC;EAC/B,CAAC;EAEDiB,2BAA2BA,CAAA,EAAG;IAC5B,MAAM;MAAEC;IAAqB,CAAC,GAAG,IAAI,CAAChB,KAAK;IAC3C,OAAOgB,oBAAoB,IAAIjC,sBAAsB;EACvD,CAAC;EAEDkC,mBAAmBA,CAAA,EAAG;IACpB,MAAM;MAAEC;IAAQ,CAAC,GAAG,IAAI,CAAClB,KAAK;IAC9B,OAAOkB,OAAO;EAChB,CAAC;EAEDC,4BAA4BA,CAAA,EAAG;IAC7B,MAAM;MAAEC;IAAa,CAAC,GAAG,IAAI,CAACpB,KAAK;IACnC,OAAOoB,YAAY,IAAI,CAAC;EAC1B,CAAC;EAEDC,4BAA4BA,CAAA,EAAG;IAC7B,MAAM;MAAEC;IAAe,CAAC,GAAG,IAAI,CAACtB,KAAK;IACrC,OAAOsB,cAAc,KAAK,CAAC,GAAG,CAAC,GAAGA,cAAc,IAAI,GAAG;EACzD,CAAC;EAEDC,2BAA2BA,CAAA,EAAG;IAC5B,MAAM;MAAEC;IAAc,CAAC,GAAG,IAAI,CAACxB,KAAK;IACpC,OAAOwB,aAAa,IAAI,CAAC;EAC3B;AACF,CAAC;AAED,MAAMC,SAAS,GAAGC,MAAM,CAACC,IAAI,CAAC9B,iBAAiB,CAAC;AAChD,MAAM+B,SAAS,GAAGH,SAAS,CAACI,GAAG,CAAEC,GAAG,IAAKjC,iBAAiB,CAACiC,GAAG,CAAC,CAAC;AAChE,MAAMC,YAAY,GAAGN,SAAS,CAACO,MAAM;AAAC,IAAAC,QAAA,GAEtBC,MAAgD,IAAK;EACnE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,EAAEI,CAAC,EAAE,EAAE;IACrC,MAAML,GAAG,GAAGL,SAAS,CAACU,CAAC,CAAC;IACxB,MAAMC,GAAG,GAAGR,SAAS,CAACO,CAAC,CAAC;IACxB,IAAI,OAAOC,GAAG,KAAK,UAAU,EAAE;MAC7BF,MAAM,CAACJ,GAAG,CAAC,GAAGM,GAAG,CAACC,IAAI,CAACH,MAAM,CAAC;IAChC,CAAC,MAAM;MACLA,MAAM,CAACJ,GAAG,CAAC,GAAGM,GAAG;IACnB;EACF;EACAF,MAAM,CAACI,KAAK,GAAG1C,wBAAwB,CAAC,CAAC;AAC3C,CAAC;AAAA2C,OAAA,CAAAC,OAAA,GAAAP,QAAA", "ignoreList": []}