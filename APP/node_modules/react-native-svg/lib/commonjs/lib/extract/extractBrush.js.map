{"version": 3, "names": ["_reactNative", "require", "_convertPercentageColor", "urlIdPattern", "currentColorBrush", "type", "contextFillBrush", "contextStrokeBrush", "extractBrush", "color", "brush", "match", "brushRef", "colorToProcess", "convertPercentageColor", "processedColor", "processColor", "payload", "console", "warn", "String"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractBrush.ts"], "mappings": ";;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAD,OAAA;AAEA,MAAME,YAAY,GAAG,gBAAgB;AAErC,MAAMC,iBAAiB,GAAG;EAAEC,IAAI,EAAE;AAAE,CAAC;AACrC,MAAMC,gBAAgB,GAAG;EAAED,IAAI,EAAE;AAAE,CAAC;AACpC,MAAME,kBAAkB,GAAG;EAAEF,IAAI,EAAE;AAAE,CAAC;AAEvB,SAASG,YAAYA,CAACC,KAAkB,EAAE;EACvD,IAAI,CAACA,KAAK,IAAIA,KAAK,KAAK,MAAM,EAAE;IAC9B,OAAO,IAAI;EACb;EAEA,IAAIA,KAAK,KAAK,cAAc,EAAE;IAC5B,OAAOL,iBAAiB;EAC1B;EAEA,IAAIK,KAAK,KAAK,cAAc,EAAE;IAC5B,OAAOH,gBAAgB;EACzB;EAEA,IAAIG,KAAK,KAAK,gBAAgB,EAAE;IAC9B,OAAOF,kBAAkB;EAC3B;EAEA,MAAMG,KAAK,GAAG,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,KAAK,CAACR,YAAY,CAAC;EACpE,IAAIO,KAAK,EAAE;IACT,OAAO;MAAEL,IAAI,EAAE,CAAC;MAAEO,QAAQ,EAAEF,KAAK,CAAC,CAAC;IAAE,CAAC;EACxC;;EAEA;EACA,MAAMG,cAAc,GAAG,IAAAC,8CAAsB,EAACL,KAAK,CAAC;EACpD,MAAMM,cAAc,GAAG,IAAAC,yBAAY,EAACH,cAAc,CAAC;EACnD,IAAI,OAAOE,cAAc,KAAK,QAAQ,EAAE;IACtC,OAAO;MAAEV,IAAI,EAAE,CAAC;MAAEY,OAAO,EAAEF;IAAe,CAAC;EAC7C;EAEA,IAAI,OAAOA,cAAc,KAAK,QAAQ,IAAIA,cAAc,KAAK,IAAI,EAAE;IACjE;IACA;IACA;IACA,OAAO;MAAEV,IAAI,EAAE,CAAC;MAAEY,OAAO,EAAEF;IAAe,CAAC;EAC7C;EAEAG,OAAO,CAACC,IAAI,CAAC,IAAIC,MAAM,CAACX,KAAK,CAAC,iCAAiC,CAAC;EAChE,OAAO,IAAI;AACb", "ignoreList": []}