{"version": 3, "names": ["spaceReg", "commaReg", "extractLengthList", "lengthList", "Array", "isArray", "trim", "replace", "split"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractLengthList.ts"], "mappings": ";;;;;;AAEA,MAAMA,QAAQ,GAAG,KAAK;AACtB,MAAMC,QAAQ,GAAG,IAAI;AAEN,SAASC,iBAAiBA,CACvCC,UAA+C,EACxB;EACvB,IAAIC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,EAAE;IAC7B,OAAOA,UAAU;EACnB,CAAC,MAAM,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;IACzC,OAAO,CAACA,UAAU,CAAC;EACrB,CAAC,MAAM,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;IACzC,OAAOA,UAAU,CAACG,IAAI,CAAC,CAAC,CAACC,OAAO,CAACN,QAAQ,EAAE,GAAG,CAAC,CAACO,KAAK,CAACR,QAAQ,CAAC;EACjE,CAAC,MAAM;IACL,OAAO,EAAE;EACX;AACF", "ignoreList": []}