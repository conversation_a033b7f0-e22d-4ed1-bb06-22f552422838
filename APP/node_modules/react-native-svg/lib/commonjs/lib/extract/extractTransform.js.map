{"version": 3, "names": ["_Matrix2D", "require", "_transform", "_transformToRn", "appendTransformProps", "props", "x", "y", "originX", "originY", "scaleX", "scaleY", "rotation", "skewX", "skewY", "appendTransform", "universal2axis", "universal", "axisX", "axisY", "defaultValue", "coords", "split", "length", "Array", "isArray", "isNaN", "transformsArrayToProps", "transformObjectsArray", "for<PERSON>ach", "transformObject", "keys", "Object", "console", "error", "key", "value", "props2transform", "translate", "translateX", "translateY", "origin", "scale", "skew", "warn", "tr", "or", "sc", "sk", "transformToMatrix", "transform", "reset", "columnMatrix", "append", "transformProps", "t", "parse", "e", "toArray", "extractTransform", "identity", "extractTransformSvgView", "parseTransformSvgToRnStyle"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractTransform.ts"], "mappings": ";;;;;;;;;;AACA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AAaA,SAASG,oBAAoBA,CAACC,KAAuB,EAAE;EACrD,MAAM;IAAEC,CAAC;IAAEC,CAAC;IAAEC,OAAO;IAAEC,OAAO;IAAEC,MAAM;IAAEC,MAAM;IAAEC,QAAQ;IAAEC,KAAK;IAAEC;EAAM,CAAC,GACtET,KAAK;EACP,IAAAU,yBAAe,EACbT,CAAC,GAAGE,OAAO,EACXD,CAAC,GAAGE,OAAO,EACXC,MAAM,EACNC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,KAAK,EACLN,OAAO,EACPC,OACF,CAAC;AACH;AAEA,SAASO,cAAcA,CACrBC,SAAgD,EAChDC,KAAwB,EACxBC,KAAwB,EACxBC,YAAqB,EACH;EAClB,IAAId,CAAC;EACL,IAAIC,CAAC;EACL,IAAI,OAAOU,SAAS,KAAK,QAAQ,EAAE;IACjCX,CAAC,GAAGC,CAAC,GAAGU,SAAS;EACnB,CAAC,MAAM,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IACxC,MAAMI,MAAM,GAAGJ,SAAS,CAACK,KAAK,CAAC,SAAS,CAAC;IACzC,IAAID,MAAM,CAACE,MAAM,KAAK,CAAC,EAAE;MACvBjB,CAAC,GAAG,CAACe,MAAM,CAAC,CAAC,CAAC;MACdd,CAAC,GAAG,CAACc,MAAM,CAAC,CAAC,CAAC;IAChB,CAAC,MAAM,IAAIA,MAAM,CAACE,MAAM,KAAK,CAAC,EAAE;MAC9BjB,CAAC,GAAGC,CAAC,GAAG,CAACc,MAAM,CAAC,CAAC,CAAC;IACpB;EACF,CAAC,MAAM,IAAIG,KAAK,CAACC,OAAO,CAACR,SAAS,CAAC,EAAE;IACnC,IAAIA,SAAS,CAACM,MAAM,KAAK,CAAC,EAAE;MAC1BjB,CAAC,GAAG,CAACW,SAAS,CAAC,CAAC,CAAC;MACjBV,CAAC,GAAG,CAACU,SAAS,CAAC,CAAC,CAAC;IACnB,CAAC,MAAM,IAAIA,SAAS,CAACM,MAAM,KAAK,CAAC,EAAE;MACjCjB,CAAC,GAAGC,CAAC,GAAG,CAACU,SAAS,CAAC,CAAC,CAAC;IACvB;EACF;EAEAC,KAAK,GAAG,CAACA,KAAK;EACd,IAAI,CAACQ,KAAK,CAACR,KAAK,CAAC,EAAE;IACjBZ,CAAC,GAAGY,KAAK;EACX;EAEAC,KAAK,GAAG,CAACA,KAAK;EACd,IAAI,CAACO,KAAK,CAACP,KAAK,CAAC,EAAE;IACjBZ,CAAC,GAAGY,KAAK;EACX;EAEA,OAAO,CAACb,CAAC,IAAIc,YAAY,IAAI,CAAC,EAAEb,CAAC,IAAIa,YAAY,IAAI,CAAC,CAAC;AACzD;AAEO,SAASO,sBAAsBA,CACpCC,qBAA2C,EAC3C;EACA,MAAMvB,KAAqB,GAAG,CAAC,CAAC;EAChCuB,qBAAqB,aAArBA,qBAAqB,eAArBA,qBAAqB,CAAEC,OAAO,CAAEC,eAAe,IAAK;IAClD,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACD,eAAe,CAAC;IACzC,IAAIC,IAAI,CAACR,MAAM,KAAK,CAAC,EAAE;MACrBU,OAAO,CAACC,KAAK,CACX,6DACF,CAAC;IACH;IACA,MAAMC,GAAG,GAAGJ,IAAI,CAAC,CAAC,CAAyB;IAC3C,MAAMK,KAAK,GAAGN,eAAe,CAACK,GAAG,CAAiC;IAClE;IACA9B,KAAK,CAAC8B,GAAG,CAAC,GAAGC,KAAK;EACpB,CAAC,CAAC;EACF,OAAO/B,KAAK;AACd;AAEO,SAASgC,eAAeA,CAC7BhC,KAAiC,EACR;EACzB,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EACA,MAAM;IACJO,QAAQ;IACR0B,SAAS;IACTC,UAAU;IACVC,UAAU;IACVC,MAAM;IACNjC,OAAO;IACPC,OAAO;IACPiC,KAAK;IACLhC,MAAM;IACNC,MAAM;IACNgC,IAAI;IACJ9B,KAAK;IACLC,KAAK;IACLR,CAAC;IACDC;EACF,CAAC,GAAGF,KAAK;EACT,IACEO,QAAQ,IAAI,IAAI,IAChB0B,SAAS,IAAI,IAAI,IACjBC,UAAU,IAAI,IAAI,IAClBC,UAAU,IAAI,IAAI,IAClBC,MAAM,IAAI,IAAI,IACdjC,OAAO,IAAI,IAAI,IACfC,OAAO,IAAI,IAAI,IACfiC,KAAK,IAAI,IAAI,IACbhC,MAAM,IAAI,IAAI,IACdC,MAAM,IAAI,IAAI,IACdgC,IAAI,IAAI,IAAI,IACZ9B,KAAK,IAAI,IAAI,IACbC,KAAK,IAAI,IAAI,IACbR,CAAC,IAAI,IAAI,IACTC,CAAC,IAAI,IAAI,EACT;IACA,OAAO,IAAI;EACb;EAEA,IAAIiB,KAAK,CAACC,OAAO,CAACnB,CAAC,CAAC,IAAIkB,KAAK,CAACC,OAAO,CAAClB,CAAC,CAAC,EAAE;IACxC0B,OAAO,CAACW,IAAI,CACV,oEACF,CAAC;EACH;EACA,MAAMC,EAAE,GAAG7B,cAAc,CACvBsB,SAAS,EACTC,UAAU,KAAKf,KAAK,CAACC,OAAO,CAACnB,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,EAC3CkC,UAAU,KAAKhB,KAAK,CAACC,OAAO,CAAClB,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAC5C,CAAC;EACD,MAAMuC,EAAE,GAAG9B,cAAc,CAACyB,MAAM,EAAEjC,OAAO,EAAEC,OAAO,CAAC;EACnD,MAAMsC,EAAE,GAAG/B,cAAc,CAAC0B,KAAK,EAAEhC,MAAM,EAAEC,MAAM,EAAE,CAAC,CAAC;EACnD,MAAMqC,EAAE,GAAGhC,cAAc,CAAC2B,IAAI,EAAE9B,KAAK,EAAEC,KAAK,CAAC;EAE7C,OAAO;IACLF,QAAQ,EAAEA,QAAQ,IAAI,IAAI,GAAG,CAAC,GAAG,CAACA,QAAQ,IAAI,CAAC;IAC/CJ,OAAO,EAAEsC,EAAE,CAAC,CAAC,CAAC;IACdrC,OAAO,EAAEqC,EAAE,CAAC,CAAC,CAAC;IACdpC,MAAM,EAAEqC,EAAE,CAAC,CAAC,CAAC;IACbpC,MAAM,EAAEoC,EAAE,CAAC,CAAC,CAAC;IACblC,KAAK,EAAEmC,EAAE,CAAC,CAAC,CAAC;IACZlC,KAAK,EAAEkC,EAAE,CAAC,CAAC,CAAC;IACZ1C,CAAC,EAAEuC,EAAE,CAAC,CAAC,CAAC;IACRtC,CAAC,EAAEsC,EAAE,CAAC,CAAC;EACT,CAAC;AACH;AAEO,SAASI,iBAAiBA,CAC/B5C,KAA8B,EAC9B6C,SAAsC,EACH;EACnC,IAAI,CAAC7C,KAAK,IAAI,CAAC6C,SAAS,EAAE;IACxB,OAAO,IAAI;EACb;EACA,IAAAC,eAAK,EAAC,CAAC;EACP9C,KAAK,IAAID,oBAAoB,CAACC,KAAK,CAAC;EAEpC,IAAI6C,SAAS,EAAE;IACb,IAAI1B,KAAK,CAACC,OAAO,CAACyB,SAAS,CAAC,EAAE;MAC5B,IAAI,OAAOA,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;QACpC,MAAME,YAAY,GAAGF,SAAuC;QAC5D,IAAAG,gBAAM,EACJD,YAAY,CAAC,CAAC,CAAC,EACfA,YAAY,CAAC,CAAC,CAAC,EACfA,YAAY,CAAC,CAAC,CAAC,EACfA,YAAY,CAAC,CAAC,CAAC,EACfA,YAAY,CAAC,CAAC,CAAC,EACfA,YAAY,CAAC,CAAC,CAChB,CAAC;MACH,CAAC,MAAM;QACL,MAAME,cAAc,GAAGjB,eAAe;QACpC;QACAV,sBAAsB,CAACuB,SAAiC,CAC1D,CAAC;QACDI,cAAc,IAAIlD,oBAAoB,CAACkD,cAAc,CAAC;MACxD;IACF,CAAC,MAAM,IAAI,OAAOJ,SAAS,KAAK,QAAQ,EAAE;MACxC,IAAI;QACF,MAAMK,CAAC,GAAG,IAAAC,gBAAK,EAACN,SAAS,CAAC;QAC1B,IAAAG,gBAAM,EAACE,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5C,CAAC,CAAC,OAAOE,CAAC,EAAE;QACVxB,OAAO,CAACC,KAAK,CAACuB,CAAC,CAAC;MAClB;IACF,CAAC,MAAM;MACL;MACA,MAAMH,cAAc,GAAGjB,eAAe,CAACa,SAAS,CAAC;MACjDI,cAAc,IAAIlD,oBAAoB,CAACkD,cAAc,CAAC;IACxD;EACF;EAEA,OAAO,IAAAI,iBAAO,EAAC,CAAC;AAClB;AAEe,SAASC,gBAAgBA,CACtCtD,KAAmD,EAChB;EACnC,IAAImB,KAAK,CAACC,OAAO,CAACpB,KAAK,CAAC,IAAI,OAAOA,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;IACxD,OAAOA,KAAK;EACd;EACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAI;MACF,MAAMkD,CAAC,GAAG,IAAAC,gBAAK,EAACnD,KAAK,CAAC;MACtB,OAAO,CAACkD,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC,OAAOE,CAAC,EAAE;MACVxB,OAAO,CAACC,KAAK,CAACuB,CAAC,CAAC;MAChB,OAAOG,kBAAQ;IACjB;EACF;EACA;EACA;EACA,MAAMN,cAAc,GAAGjD,KAAuB;EAC9C,OAAO4C,iBAAiB,CACtBZ,eAAe,CAACiB,cAAc,CAAC,EAC/BA,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEJ,SAClB,CAAC;AACH;AAEO,SAASW,uBAAuBA,CACrCxD,KAAsB,EACQ;EAC9B,IAAI,OAAOA,KAAK,CAAC6C,SAAS,KAAK,QAAQ,EAAE;IACvC,OAAO,IAAAY,oBAA0B,EAACzD,KAAK,CAAC6C,SAAS,CAAC;EACpD;EACA,OAAO7C,KAAK,CAAC6C,SAAS;AACxB", "ignoreList": []}