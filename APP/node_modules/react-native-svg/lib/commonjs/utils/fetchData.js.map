{"version": 3, "names": ["_reactNative", "require", "_buffer", "fetchText", "uri", "startsWith", "Platform", "OS", "dataUriToXml", "decodeBase64Image", "fetchUriData", "decoded", "decodeURIComponent", "splitContent", "split", "dataType", "content", "slice", "join", "<PERSON><PERSON><PERSON>", "from", "toString", "error", "Error", "response", "fetch", "ok", "status", "text"], "sourceRoot": "../../../src", "sources": ["utils/fetchData.ts"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AAEO,eAAeE,SAASA,CAACC,GAAY,EAA0B;EACpE,IAAI,CAACA,GAAG,EAAE;IACR,OAAO,IAAI;EACb;EACA,IAAIA,GAAG,CAACC,UAAU,CAAC,yBAAyB,CAAC,IAAIC,qBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;IAC1E,OAAOC,YAAY,CAACJ,GAAG,CAAC;EAC1B,CAAC,MAAM,IAAIA,GAAG,CAACC,UAAU,CAAC,2BAA2B,CAAC,EAAE;IACtD,OAAOI,iBAAiB,CAACL,GAAG,CAAC;EAC/B,CAAC,MAAM;IACL,OAAOM,YAAY,CAACN,GAAG,CAAC;EAC1B;AACF;AAEA,MAAMK,iBAAiB,GAAIL,GAAW,IAAK;EACzC,MAAMO,OAAO,GAAGC,kBAAkB,CAACR,GAAG,CAAC;EACvC,MAAMS,YAAY,GAAGF,OAAO,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC;EACrD,MAAMC,QAAQ,GAAGF,YAAY,CAAC,CAAC,CAAmB;EAClD,MAAMG,OAAO,GAAGH,YAAY,CAACI,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAE/C,OAAOC,cAAM,CAACC,IAAI,CAACJ,OAAO,EAAED,QAAQ,CAAC,CAACM,QAAQ,CAAC,OAAO,CAAC;AACzD,CAAC;AAED,SAASb,YAAYA,CAACJ,GAAW,EAAiB;EAChD,IAAI;IACF;IACA,OAAOQ,kBAAkB,CAACR,GAAG,CAAC,CAACU,KAAK,CAAC,GAAG,CAAC,CAACG,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAC9D,CAAC,CAAC,OAAOI,KAAK,EAAE;IACd,MAAM,IAAIC,KAAK,CAAC,YAAYnB,GAAG,uBAAuBkB,KAAK,EAAE,CAAC;EAChE;AACF;AAEA,eAAeZ,YAAYA,CAACN,GAAW,EAAE;EACvC,MAAMoB,QAAQ,GAAG,MAAMC,KAAK,CAACrB,GAAG,CAAC;EACjC,IAAIoB,QAAQ,CAACE,EAAE,IAAKF,QAAQ,CAACG,MAAM,KAAK,CAAC,IAAIvB,GAAG,CAACC,UAAU,CAAC,SAAS,CAAE,EAAE;IACvE,OAAO,MAAMmB,QAAQ,CAACI,IAAI,CAAC,CAAC;EAC9B;EACA,MAAM,IAAIL,KAAK,CAAC,YAAYnB,GAAG,uBAAuBoB,QAAQ,CAACG,MAAM,EAAE,CAAC;AAC1E", "ignoreList": []}