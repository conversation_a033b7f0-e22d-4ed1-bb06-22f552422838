import { camelCase, parse, SvgAst, SvgFromUri, SvgFromXml, SvgUri, SvgXml } from './xml';
import { fetchText } from './utils/fetchData';
export { inlineStyles, loadLocalRawResource, LocalSvg, SvgCss, SvgCssUri, SvgWithCss, SvgWithCssUri, WithLocalSvg } from './deprecated';
export { camelCase, fetchText, parse, SvgAst, SvgFromUri, SvgFromXml, SvgUri, SvgXml };
export * from './lib/extract/types';
export * from './elements';
export { default } from './elements';
//# sourceMappingURL=ReactNativeSVG.web.js.map