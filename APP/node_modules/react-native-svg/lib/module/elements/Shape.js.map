{"version": 3, "names": ["Component", "SvgTouchableMixin", "extractBrush", "findNodeHandle", "BrushProperties", "multiplyMatrices", "l", "r", "a", "al", "b", "bl", "c", "cl", "d", "dl", "e", "el", "f", "fl", "ar", "br", "cr", "dr", "er", "fr", "invert", "n", "deg2rad", "Math", "PI", "SVGMatrix", "constructor", "matrix", "multiply", "secondMatrix", "inverse", "translate", "x", "y", "scale", "scaleFactor", "scaleNonUniform", "scaleFactorX", "scaleFactorY", "rotate", "angle", "cos", "sin", "rotateFromVector", "atan2", "flipX", "flipY", "skewX", "tan", "skewY", "matrixTransform", "point", "SVGPoint", "ownerSVGElement", "createSVGPoint", "createSVGMatrix", "<PERSON><PERSON><PERSON>", "root", "props", "refMethod", "instance", "getNativeScrollRef", "setNativeProps", "_this$root", "key", "includes", "getBBox", "options", "fill", "stroke", "markers", "clipped", "handle", "RNSVGRenderableModule", "require", "default", "getCTM", "getScreenCTM", "isPointInFill", "isPointInStroke", "getTotalLength", "getPointAtLength", "length", "prototype"], "sourceRoot": "../../../src", "sources": ["elements/Shape.tsx"], "mappings": "AAAA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,iBAAiB,MAAM,0BAA0B;AACxD,OAAOC,YAAY,MAAM,6BAA6B;AAEtD,SAASC,cAAc,QAAQ,cAAc;AAM7C,SAASC,eAAe,QAAQ,uBAAuB;AAwDvD,OAAO,SAASC,gBAAgBA,CAACC,CAAS,EAAEC,CAAS,EAAU;EAC7D,MAAM;IAAEC,CAAC,EAAEC,EAAE;IAAEC,CAAC,EAAEC,EAAE;IAAEC,CAAC,EAAEC,EAAE;IAAEC,CAAC,EAAEC,EAAE;IAAEC,CAAC,EAAEC,EAAE;IAAEC,CAAC,EAAEC;EAAG,CAAC,GAAGb,CAAC;EACtD,MAAM;IAAEE,CAAC,EAAEY,EAAE;IAAEV,CAAC,EAAEW,EAAE;IAAET,CAAC,EAAEU,EAAE;IAAER,CAAC,EAAES,EAAE;IAAEP,CAAC,EAAEQ,EAAE;IAAEN,CAAC,EAAEO;EAAG,CAAC,GAAGlB,CAAC;EAEtD,MAAMC,CAAC,GAAGC,EAAE,GAAGW,EAAE,GAAGP,EAAE,GAAGQ,EAAE;EAC3B,MAAMT,CAAC,GAAGH,EAAE,GAAGa,EAAE,GAAGT,EAAE,GAAGU,EAAE;EAC3B,MAAMP,CAAC,GAAGP,EAAE,GAAGe,EAAE,GAAGX,EAAE,GAAGY,EAAE,GAAGR,EAAE;EAChC,MAAMP,CAAC,GAAGC,EAAE,GAAGS,EAAE,GAAGL,EAAE,GAAGM,EAAE;EAC3B,MAAMP,CAAC,GAAGH,EAAE,GAAGW,EAAE,GAAGP,EAAE,GAAGQ,EAAE;EAC3B,MAAML,CAAC,GAAGP,EAAE,GAAGa,EAAE,GAAGT,EAAE,GAAGU,EAAE,GAAGN,EAAE;EAEhC,OAAO;IAAEX,CAAC;IAAEI,CAAC;IAAEI,CAAC;IAAEN,CAAC;IAAEI,CAAC;IAAEI;EAAE,CAAC;AAC7B;AAEA,OAAO,SAASQ,MAAMA,CAAC;EAAElB,CAAC;EAAEE,CAAC;EAAEE,CAAC;EAAEE,CAAC;EAAEE,CAAC;EAAEE;AAAU,CAAC,EAAU;EAC3D,MAAMS,CAAC,GAAGnB,CAAC,GAAGM,CAAC,GAAGJ,CAAC,GAAGE,CAAC;EACvB,OAAO;IACLJ,CAAC,EAAEM,CAAC,GAAGa,CAAC;IACRjB,CAAC,EAAE,CAACA,CAAC,GAAGiB,CAAC;IACTf,CAAC,EAAE,CAACA,CAAC,GAAGe,CAAC;IACTb,CAAC,EAAEN,CAAC,GAAGmB,CAAC;IACRX,CAAC,EAAE,CAACJ,CAAC,GAAGM,CAAC,GAAGJ,CAAC,GAAGE,CAAC,IAAIW,CAAC;IACtBT,CAAC,EAAE,EAAEV,CAAC,GAAGU,CAAC,GAAGR,CAAC,GAAGM,CAAC,CAAC,GAAGW;EACxB,CAAC;AACH;AAEA,MAAMC,OAAO,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;AAE7B,OAAO,MAAMC,SAAS,CAAsB;EAC1CC,WAAWA,CAACC,MAAe,EAAE;IAC3B,IAAIA,MAAM,EAAE;MACV,MAAM;QAAEzB,CAAC;QAAEE,CAAC;QAAEE,CAAC;QAAEE,CAAC;QAAEE,CAAC;QAAEE;MAAE,CAAC,GAAGe,MAAM;MACnC,IAAI,CAACzB,CAAC,GAAGA,CAAC;MACV,IAAI,CAACE,CAAC,GAAGA,CAAC;MACV,IAAI,CAACE,CAAC,GAAGA,CAAC;MACV,IAAI,CAACE,CAAC,GAAGA,CAAC;MACV,IAAI,CAACE,CAAC,GAAGA,CAAC;MACV,IAAI,CAACE,CAAC,GAAGA,CAAC;IACZ,CAAC,MAAM;MACL,IAAI,CAACV,CAAC,GAAG,CAAC;MACV,IAAI,CAACE,CAAC,GAAG,CAAC;MACV,IAAI,CAACE,CAAC,GAAG,CAAC;MACV,IAAI,CAACE,CAAC,GAAG,CAAC;MACV,IAAI,CAACE,CAAC,GAAG,CAAC;MACV,IAAI,CAACE,CAAC,GAAG,CAAC;IACZ;EACF;EAEAgB,QAAQA,CAACC,YAAoB,EAAa;IACxC,OAAO,IAAIJ,SAAS,CAAC1B,gBAAgB,CAAC,IAAI,EAAE8B,YAAY,CAAC,CAAC;EAC5D;EAEAC,OAAOA,CAAA,EAAc;IACnB,OAAO,IAAIL,SAAS,CAACL,MAAM,CAAC,IAAI,CAAC,CAAC;EACpC;EAEAW,SAASA,CAACC,CAAS,EAAEC,CAAS,EAAa;IACzC,OAAO,IAAIR,SAAS,CAClB1B,gBAAgB,CAAC,IAAI,EAAE;MAAEG,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAEsB,CAAC;MAAEpB,CAAC,EAAEqB;IAAE,CAAC,CAC/D,CAAC;EACH;EAEAC,KAAKA,CAACC,WAAmB,EAAa;IACpC,OAAO,IAAIV,SAAS,CAClB1B,gBAAgB,CAAC,IAAI,EAAE;MACrBG,CAAC,EAAEiC,WAAW;MACd/B,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE2B,WAAW;MACdzB,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE;IACL,CAAC,CACH,CAAC;EACH;EAEAwB,eAAeA,CAACC,YAAoB,EAAEC,YAAoB,EAAa;IACrE,OAAO,IAAIb,SAAS,CAClB1B,gBAAgB,CAAC,IAAI,EAAE;MACrBG,CAAC,EAAEmC,YAAY;MACfjC,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE8B,YAAY;MACf5B,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE;IACL,CAAC,CACH,CAAC;EACH;EAEA2B,MAAMA,CAACC,KAAa,EAAa;IAC/B,MAAMC,GAAG,GAAGlB,IAAI,CAACkB,GAAG,CAACnB,OAAO,GAAGkB,KAAK,CAAC;IACrC,MAAME,GAAG,GAAGnB,IAAI,CAACmB,GAAG,CAACpB,OAAO,GAAGkB,KAAK,CAAC;IACrC,OAAO,IAAIf,SAAS,CAClB1B,gBAAgB,CAAC,IAAI,EAAE;MAAEG,CAAC,EAAEuC,GAAG;MAAErC,CAAC,EAAEsC,GAAG;MAAEpC,CAAC,EAAE,CAACoC,GAAG;MAAElC,CAAC,EAAEiC,GAAG;MAAE/B,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC,CACxE,CAAC;EACH;EAEA+B,gBAAgBA,CAACX,CAAS,EAAEC,CAAS,EAAa;IAChD,MAAMO,KAAK,GAAGjB,IAAI,CAACqB,KAAK,CAACX,CAAC,EAAED,CAAC,CAAC;IAC9B,MAAMS,GAAG,GAAGlB,IAAI,CAACkB,GAAG,CAACnB,OAAO,GAAGkB,KAAK,CAAC;IACrC,MAAME,GAAG,GAAGnB,IAAI,CAACmB,GAAG,CAACpB,OAAO,GAAGkB,KAAK,CAAC;IACrC,OAAO,IAAIf,SAAS,CAClB1B,gBAAgB,CAAC,IAAI,EAAE;MAAEG,CAAC,EAAEuC,GAAG;MAAErC,CAAC,EAAEsC,GAAG;MAAEpC,CAAC,EAAE,CAACoC,GAAG;MAAElC,CAAC,EAAEiC,GAAG;MAAE/B,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC,CACxE,CAAC;EACH;EAEAiC,KAAKA,CAAA,EAAc;IACjB,OAAO,IAAIpB,SAAS,CAClB1B,gBAAgB,CAAC,IAAI,EAAE;MAAEG,CAAC,EAAE,CAAC,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC,CAChE,CAAC;EACH;EAEAkC,KAAKA,CAAA,EAAc;IACjB,OAAO,IAAIrB,SAAS,CAClB1B,gBAAgB,CAAC,IAAI,EAAE;MAAEG,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC,CAChE,CAAC;EACH;EAEAmC,KAAKA,CAACP,KAAa,EAAa;IAC9B,OAAO,IAAIf,SAAS,CAClB1B,gBAAgB,CAAC,IAAI,EAAE;MACrBG,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE,CAAC;MACJE,CAAC,EAAEiB,IAAI,CAACyB,GAAG,CAAC1B,OAAO,GAAGkB,KAAK,CAAC;MAC5BhC,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE;IACL,CAAC,CACH,CAAC;EACH;EAEAqC,KAAKA,CAACT,KAAa,EAAa;IAC9B,OAAO,IAAIf,SAAS,CAClB1B,gBAAgB,CAAC,IAAI,EAAE;MACrBG,CAAC,EAAE,CAAC;MACJE,CAAC,EAAEmB,IAAI,CAACyB,GAAG,CAAC1B,OAAO,GAAGkB,KAAK,CAAC;MAC5BlC,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE,CAAC;MACJE,CAAC,EAAE;IACL,CAAC,CACH,CAAC;EACH;AACF;AAEA,OAAO,SAASsC,eAAeA,CAACvB,MAAc,EAAEwB,KAAY,EAAS;EACnE,MAAM;IAAEjD,CAAC;IAAEE,CAAC;IAAEE,CAAC;IAAEE,CAAC;IAAEE,CAAC;IAAEE;EAAE,CAAC,GAAGe,MAAM;EACnC,MAAM;IAAEK,CAAC;IAAEC;EAAE,CAAC,GAAGkB,KAAK;EACtB,OAAO;IACLnB,CAAC,EAAE9B,CAAC,GAAG8B,CAAC,GAAG1B,CAAC,GAAG2B,CAAC,GAAGvB,CAAC;IACpBuB,CAAC,EAAE7B,CAAC,GAAG4B,CAAC,GAAGxB,CAAC,GAAGyB,CAAC,GAAGrB;EACrB,CAAC;AACH;AAEA,OAAO,MAAMwC,QAAQ,CAAqB;EACxC1B,WAAWA,CAACyB,KAAa,EAAE;IACzB,IAAIA,KAAK,EAAE;MACT,MAAM;QAAEnB,CAAC;QAAEC;MAAE,CAAC,GAAGkB,KAAK;MACtB,IAAI,CAACnB,CAAC,GAAGA,CAAC;MACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACZ,CAAC,MAAM;MACL,IAAI,CAACD,CAAC,GAAG,CAAC;MACV,IAAI,CAACC,CAAC,GAAG,CAAC;IACZ;EACF;EAEAiB,eAAeA,CAACvB,MAAc,EAAY;IACxC,OAAO,IAAIyB,QAAQ,CAACF,eAAe,CAACvB,MAAM,EAAE,IAAI,CAAC,CAAC;EACpD;AACF;AAEA,OAAO,MAAM0B,eAAe,GAAG;EAC7BC,cAAcA,CAAA,EAAa;IACzB,OAAO,IAAIF,QAAQ,CAAC,CAAC;EACvB,CAAC;EACDG,eAAeA,CAAA,EAAc;IAC3B,OAAO,IAAI9B,SAAS,CAAC,CAAC;EACxB;AACF,CAAC;AAED,eAAe,MAAM+B,KAAK,SAAY9D,SAAS,CAAI;EAEjD+D,IAAI,GAAsC,IAAI;EAC9C/B,WAAWA,CAACgC,KAAsB,EAAE;IAClC,KAAK,CAACA,KAAK,CAAC;IACZ/D,iBAAiB,CAAC,IAAI,CAAC;EACzB;EAEAgE,SAAS,GACPC,QAA2C,IACxC;IACH,IAAI,CAACH,IAAI,GAAGG,QAAQ;EACtB,CAAC;;EAED;EACAC,kBAAkBA,CAAA,EAAsC;IACtD,OAAO,IAAI,CAACJ,IAAI;EAClB;EAEAK,cAAc,GACZJ,KAGkB,IACf;IAAA,IAAAK,UAAA;IACH,KAAK,MAAMC,GAAG,IAAIN,KAAK,EAAE;MACvB,IAAI5D,eAAe,CAACmE,QAAQ,CAACD,GAAG,CAAC,EAAE;QACjC;QACAN,KAAK,CAACM,GAAG,CAAC,GAAGpE,YAAY,CAAC8D,KAAK,CAACM,GAAG,CAAC,CAAC;MACvC;IACF;IACA,CAAAD,UAAA,OAAI,CAACN,IAAI,cAAAM,UAAA,eAATA,UAAA,CAAWD,cAAc,CAACJ,KAAK,CAAC;EAClC,CAAC;;EAED;AACF;AACA;AACA;AACA;EACEQ,OAAO,GAAIC,OAA+B,IAA0B;IAClE,MAAM;MACJC,IAAI,GAAG,IAAI;MACXC,MAAM,GAAG,IAAI;MACbC,OAAO,GAAG,IAAI;MACdC,OAAO,GAAG;IACZ,CAAC,GAAGJ,OAAO,IAAI,CAAC,CAAC;IACjB,MAAMK,MAAM,GAAG3E,cAAc,CAAC,IAAI,CAAC4D,IAAI,CAAC;IACxC,MAAMgB,qBAAqB,GACzBC,OAAO,CAAC,qCAAqC,CAAC,CAACC,OAAO;IACxD,OAAOF,qBAAqB,CAACP,OAAO,CAACM,MAAM,EAAE;MAC3CJ,IAAI;MACJC,MAAM;MACNC,OAAO;MACPC;IACF,CAAC,CAAC;EACJ,CAAC;EAEDK,MAAM,GAAGA,CAAA,KAAiB;IACxB,MAAMJ,MAAM,GAAG3E,cAAc,CAAC,IAAI,CAAC4D,IAAI,CAAC;IACxC,MAAMgB,qBAA2B,GAC/BC,OAAO,CAAC,qCAAqC,CAAC,CAACC,OAAO;IACxD,OAAO,IAAIlD,SAAS,CAACgD,qBAAqB,CAACG,MAAM,CAACJ,MAAM,CAAC,CAAC;EAC5D,CAAC;EAEDK,YAAY,GAAGA,CAAA,KAAiB;IAC9B,MAAML,MAAM,GAAG3E,cAAc,CAAC,IAAI,CAAC4D,IAAI,CAAC;IACxC,MAAMgB,qBAA2B,GAC/BC,OAAO,CAAC,qCAAqC,CAAC,CAACC,OAAO;IACxD,OAAO,IAAIlD,SAAS,CAACgD,qBAAqB,CAACI,YAAY,CAACL,MAAM,CAAC,CAAC;EAClE,CAAC;EAEDM,aAAa,GAAIX,OAAqB,IAA0B;IAC9D,MAAMK,MAAM,GAAG3E,cAAc,CAAC,IAAI,CAAC4D,IAAI,CAAC;IACxC,MAAMgB,qBAA2B,GAC/BC,OAAO,CAAC,qCAAqC,CAAC,CAACC,OAAO;IACxD,OAAOF,qBAAqB,CAACK,aAAa,CAACN,MAAM,EAAEL,OAAO,CAAC;EAC7D,CAAC;EAEDY,eAAe,GAAIZ,OAAqB,IAA0B;IAChE,MAAMK,MAAM,GAAG3E,cAAc,CAAC,IAAI,CAAC4D,IAAI,CAAC;IACxC,MAAMgB,qBAA2B,GAC/BC,OAAO,CAAC,qCAAqC,CAAC,CAACC,OAAO;IACxD,OAAOF,qBAAqB,CAACM,eAAe,CAACP,MAAM,EAAEL,OAAO,CAAC;EAC/D,CAAC;EAEDa,cAAc,GAAGA,CAAA,KAA0B;IACzC,MAAMR,MAAM,GAAG3E,cAAc,CAAC,IAAI,CAAC4D,IAAI,CAAC;IACxC,MAAMgB,qBAA2B,GAC/BC,OAAO,CAAC,qCAAqC,CAAC,CAACC,OAAO;IACxD,OAAOF,qBAAqB,CAACO,cAAc,CAACR,MAAM,CAAC;EACrD,CAAC;EAEDS,gBAAgB,GAAIC,MAAc,IAAe;IAC/C,MAAMV,MAAM,GAAG3E,cAAc,CAAC,IAAI,CAAC4D,IAAI,CAAC;IACxC,MAAMgB,qBAA2B,GAC/BC,OAAO,CAAC,qCAAqC,CAAC,CAACC,OAAO;IACxD,OAAO,IAAIvB,QAAQ,CACjBqB,qBAAqB,CAACQ,gBAAgB,CAACT,MAAM,EAAE;MAAEU;IAAO,CAAC,CAC3D,CAAC;EACH,CAAC;AACH;AACA1B,KAAK,CAAC2B,SAAS,CAAC9B,eAAe,GAAGA,eAAe", "ignoreList": []}