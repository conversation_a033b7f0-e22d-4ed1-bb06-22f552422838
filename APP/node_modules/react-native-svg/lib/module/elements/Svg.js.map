{"version": 3, "names": ["React", "findNodeHandle", "Platform", "StyleSheet", "extractResponder", "extractViewBox", "<PERSON><PERSON><PERSON>", "G", "RNSVGSvgAndroid", "RNSVGSvgIOS", "extractOpacity", "extractTransformSvgView", "styles", "create", "svg", "backgroundColor", "borderWidth", "defaultStyle", "Svg", "displayName", "defaultProps", "preserveAspectRatio", "measureInWindow", "callback", "root", "measure", "measureLayout", "relativeToNativeNode", "onSuccess", "onFail", "setNativeProps", "props", "toDataURL", "options", "handle", "RNSVGSvgViewModule", "require", "default", "render", "style", "opacity", "viewBox", "children", "onLayout", "extracted", "stylesAndProps", "Array", "isArray", "Object", "assign", "width", "height", "focusable", "transform", "font", "fill", "fillOpacity", "fillRule", "stroke", "strokeWidth", "strokeOpacity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "strokeLinecap", "strokeLinejoin", "strokeMiterlimit", "undefined", "Boolean", "rootStyles", "push", "override", "overrideStyles", "o", "NaN", "isNaN", "w", "parseInt", "h", "doNotParseWidth", "length", "doNotParseHeight", "flex", "bb<PERSON><PERSON><PERSON>", "bbHeight", "gStyle", "flatten", "RNSVGSvg", "OS", "createElement", "_extends", "ref", "refMethod"], "sourceRoot": "../../../src", "sources": ["elements/Svg.tsx"], "mappings": ";AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAU9B,SAASC,cAAc,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,cAAc;AAOnE,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,KAAK,MAAM,SAAS;AAE3B,OAAOC,CAAC,MAAM,KAAK;AACnB,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,WAAW,MAAM,qCAAqC;AAE7D,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,SAASC,uBAAuB,QAAQ,iCAAiC;AAGzE,MAAMC,MAAM,GAAGT,UAAU,CAACU,MAAM,CAAC;EAC/BC,GAAG,EAAE;IACHC,eAAe,EAAE,aAAa;IAC9BC,WAAW,EAAE;EACf;AACF,CAAC,CAAC;AACF,MAAMC,YAAY,GAAGL,MAAM,CAACE,GAAG;AAW/B,eAAe,MAAMI,GAAG,SAASZ,KAAK,CAAW;EAC/C,OAAOa,WAAW,GAAG,KAAK;EAE1B,OAAOC,YAAY,GAAG;IACpBC,mBAAmB,EAAE;EACvB,CAAC;EAEDC,eAAe,GAAIC,QAA0C,IAAK;IAChE,MAAM;MAAEC;IAAK,CAAC,GAAG,IAAI;IACrBA,IAAI,IAAIA,IAAI,CAACF,eAAe,CAACC,QAAQ,CAAC;EACxC,CAAC;EAEDE,OAAO,GAAIF,QAAkC,IAAK;IAChD,MAAM;MAAEC;IAAK,CAAC,GAAG,IAAI;IACrBA,IAAI,IAAIA,IAAI,CAACC,OAAO,CAACF,QAAQ,CAAC;EAChC,CAAC;EAEDG,aAAa,GAAGA,CACdC,oBAA4B,EAC5BC,SAAyC,EACzCC,MAAkB,KACf;IACH,MAAM;MAAEL;IAAK,CAAC,GAAG,IAAI;IACrBA,IAAI,IAAIA,IAAI,CAACE,aAAa,CAACC,oBAAoB,EAAEC,SAAS,EAAEC,MAAM,CAAC;EACrE,CAAC;EAEDC,cAAc,GACZC,KAGC,IACE;IACH,MAAM;MAAEP;IAAK,CAAC,GAAG,IAAI;IACrBA,IAAI,IAAIA,IAAI,CAACM,cAAc,CAACC,KAAK,CAAC;EACpC,CAAC;EAEDC,SAAS,GAAGA,CAACT,QAAkC,EAAEU,OAAgB,KAAK;IACpE,IAAI,CAACV,QAAQ,EAAE;MACb;IACF;IACA,MAAMW,MAAM,GAAGjC,cAAc,CAAC,IAAI,CAACuB,IAAiB,CAAC;IACrD,MAAMW,kBAAwB;IAC5B;IACAC,OAAO,CAAC,+BAA+B,CAAC,CAACC,OAAO;IAClDF,kBAAkB,CAACH,SAAS,CAACE,MAAM,EAAED,OAAO,EAAEV,QAAQ,CAAC;EACzD,CAAC;EAEDe,MAAMA,CAAA,EAAG;IACP,MAAM;MACJC,KAAK;MACLC,OAAO;MACPC,OAAO;MACPC,QAAQ;MACRC,QAAQ;MACRtB,mBAAmB;MACnB,GAAGuB;IACL,CAAC,GAAG,IAAI,CAACb,KAAK;IACd,MAAMc,cAAc,GAAG;MACrB,IAAIC,KAAK,CAACC,OAAO,CAACR,KAAK,CAAC,GAAGS,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAGV,KAAK,CAAC,GAAGA,KAAK,CAAC;MAC/D,GAAGK;IACL,CAAC;IACD,IAAI;MACFM,KAAK;MACLC,MAAM;MACNC,SAAS;MACTC,SAAS;MAET;MACAC,IAAI;MACJC,IAAI;MACJC,WAAW;MACXC,QAAQ;MACRC,MAAM;MACNC,WAAW;MACXC,aAAa;MACbC,eAAe;MACfC,gBAAgB;MAChBC,aAAa;MACbC,cAAc;MACdC;IACF,CAAC,GAAGpB,cAAc;IAClB,IAAIK,KAAK,KAAKgB,SAAS,IAAIf,MAAM,KAAKe,SAAS,EAAE;MAC/ChB,KAAK,GAAGC,MAAM,GAAG,MAAM;IACzB;IAEA,MAAMpB,KAAqB,GAAGa,SAA2B;IACzDb,KAAK,CAACqB,SAAS,GAAGe,OAAO,CAACf,SAAS,CAAC,IAAIA,SAAS,KAAK,OAAO;IAC7D,MAAMgB,UAAkC,GAAG,CAACnD,YAAY,CAAC;IAEzD,IAAIsB,KAAK,EAAE;MACT6B,UAAU,CAACC,IAAI,CAAC9B,KAAK,CAAC;IACxB;IAEA,IAAI+B,QAAQ,GAAG,KAAK;IACpB,MAAMC,cAAyB,GAAG,CAAC,CAAC;IACpC,MAAMC,CAAC,GAAGhC,OAAO,IAAI,IAAI,GAAG9B,cAAc,CAAC8B,OAAO,CAAC,GAAGiC,GAAG;IACzD,IAAI,CAACC,KAAK,CAACF,CAAC,CAAC,EAAE;MACbF,QAAQ,GAAG,IAAI;MACfC,cAAc,CAAC/B,OAAO,GAAGgC,CAAC;IAC5B;IAEA,IAAItB,KAAK,IAAIC,MAAM,EAAE;MACnBmB,QAAQ,GAAG,IAAI;MACf,MAAMK,CAAC,GAAGC,QAAQ,CAAC1B,KAAK,EAAE,EAAE,CAAC;MAC7B,MAAM2B,CAAC,GAAGD,QAAQ,CAACzB,MAAM,EAAE,EAAE,CAAC;MAC9B,MAAM2B,eAAe,GAAGJ,KAAK,CAACC,CAAC,CAAC,IAAIzB,KAAK,CAACA,KAAK,CAAC6B,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG;MACnE,MAAMC,gBAAgB,GAAGN,KAAK,CAACG,CAAC,CAAC,IAAI1B,MAAM,CAACA,MAAM,CAAC4B,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG;MACtER,cAAc,CAACrB,KAAK,GAAG4B,eAAe,GAAG5B,KAAK,GAAGyB,CAAC;MAClDJ,cAAc,CAACpB,MAAM,GAAG6B,gBAAgB,GAAG7B,MAAM,GAAG0B,CAAC;MACrDN,cAAc,CAACU,IAAI,GAAG,CAAC;IACzB;IAEA,IAAIX,QAAQ,EAAE;MACZF,UAAU,CAACC,IAAI,CAACE,cAAc,CAAC;IACjC;IAEAxC,KAAK,CAACQ,KAAK,GAAG6B,UAAU,CAACW,MAAM,GAAG,CAAC,GAAGX,UAAU,GAAGnD,YAAY;IAE/D,IAAIiC,KAAK,IAAI,IAAI,EAAE;MACjBnB,KAAK,CAACmD,OAAO,GAAGhC,KAAK;IACvB;IACA,IAAIC,MAAM,IAAI,IAAI,EAAE;MAClBpB,KAAK,CAACoD,QAAQ,GAAGhC,MAAM;IACzB;IAEA/C,gBAAgB,CAAC2B,KAAK,EAAEA,KAAK,EAAE,IAA8B,CAAC;IAE9D,IAAIY,QAAQ,IAAI,IAAI,EAAE;MACpBZ,KAAK,CAACY,QAAQ,GAAGA,QAAQ;IAC3B;IAEA,MAAMyC,MAAM,GAAGpC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE9C,UAAU,CAACkF,OAAO,CAAC9C,KAAK,CAAC,CAAC;IAC3D,IAAIc,SAAS,EAAE;MACb,IAAI+B,MAAM,CAAC/B,SAAS,EAAE;QACpBtB,KAAK,CAACsB,SAAS,GAAG+B,MAAM,CAAC/B,SAAS;QAClC+B,MAAM,CAAC/B,SAAS,GAAGa,SAAS;MAC9B;MACA;MACAnC,KAAK,CAACsB,SAAS,GAAG1C,uBAAuB,CAACoB,KAAY,CAAC;IACzD;IAEA,MAAMuD,QAAQ,GAAGpF,QAAQ,CAACqF,EAAE,KAAK,SAAS,GAAG/E,eAAe,GAAGC,WAAW;IAE1E,oBACET,KAAA,CAAAwF,aAAA,CAACF,QAAQ,EAAAG,QAAA,KACH1D,KAAK;MACT2D,GAAG,EAAGA,GAAG,IAAK,IAAI,CAACC,SAAS,CAACD,GAAmC;IAAE,GAC9DrF,cAAc,CAAC;MAAEoC,OAAO;MAAEpB;IAAoB,CAAC,CAAC,gBACpDrB,KAAA,CAAAwF,aAAA,CAACjF,CAAC;MAEEmC,QAAQ;MACRH,KAAK,EAAE6C,MAAM;MACb9B,IAAI;MACJC,IAAI;MACJC,WAAW;MACXC,QAAQ;MACRC,MAAM;MACNC,WAAW;MACXC,aAAa;MACbC,eAAe;MACfC,gBAAgB;MAChBC,aAAa;MACbC,cAAc;MACdC;IAAgB,CAEnB,CACO,CAAC;EAEf;AACF", "ignoreList": []}