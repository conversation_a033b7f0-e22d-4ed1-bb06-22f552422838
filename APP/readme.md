# IsotopeAI Mobile App - Comprehensive Phase-wise Implementation Plan

## Overview

This document provides a comprehensive **14-phase implementation plan** for building the IsotopeAI mobile application using Expo (React Native). The app replicates the functionality of the existing web application with identical Supabase integration and beautiful Material 3 Expressive design.

**Important**: Copy all environment variables and Supabase configuration from `/WEB/` directory.

## 📋 Implementation Phases Overview

This implementation is organized into **14 sequential phases** with **158 detailed subtasks**, each representing approximately 20 minutes of professional development work:

### Phase Summary:
1. **Phase 1: Project Setup & Foundation** (8 tasks)
2. **Phase 2: Backend Integration & Authentication** (10 tasks)
3. **Phase 3: Material 3 Expressive Design System** (10 tasks)
4. **Phase 4: Core Timer System Implementation** (12 tasks)
5. **Phase 5: Subject Management System** (10 tasks)
6. **Phase 6: Task Management (Kanban + Table Views)** (12 tasks)
7. **Phase 7: Study Analytics Dashboard** (12 tasks)
8. **Phase 8: Groups & Collaboration Features** (12 tasks)
9. **Phase 9: Mock Test Analysis System** (12 tasks)
10. **Phase 10: Advanced Animations & Interactions** (12 tasks)
11. **Phase 11: Background Tasks & Notifications** (12 tasks)
12. **Phase 12: Testing & Quality Assurance** (12 tasks)
13. **Phase 13: Performance Optimization** (12 tasks)
14. **Phase 14: Deployment & Distribution** (12 tasks)

## Architecture Overview

The application uses a modern, expressive approach:
- **Backend**: Supabase (Authentication, Database, Real-time subscriptions, Storage)
- **Frontend**: React Native with TypeScript, Zustand for state management
- **UI**: Material 3 Expressive Design System with enhanced personalization
- **Animations**: React Native Reanimated 3 with dynamic motion and Lottie micro-interactions
- **Theming**: Theming: Dynamic Material 3 Expressive color system, deep personalization.

The mobile app maintains identical data structures and API patterns for seamless synchronization while delivering a more engaging and emotionally resonant user experience.

## Core Features Implementation

### 1. Study Timer System
**Database Schema**: `study_sessions` table in Supabase
```typescript
interface StudySession {
  id: string;
  user_id: string;
  subject: string;
  task_name: string;
  task_type: string; // Lecture, Exercise, Reading, etc.
  start_time: string; // ISO timestamp
  end_time: string;
  duration: number; // seconds
  mode: 'pomodoro' | 'stopwatch';
  phase: 'work' | 'shortBreak' | 'longBreak' | 'pause';
  completed: boolean;
  date: string; // YYYY-MM-DD
  notes: string;
  productivity_rating: number; // 1-5 scale
}
```

**Key Features**:
- Pomodoro (25min work, 5min break, 15min long break after 4 sessions)
- Stopwatch mode with customizable notification intervals
- Background timer persistence using AsyncStorage
- Local notifications for session transitions
- Session pause/resume with duration tracking
- Subject selection with color-coded organization
- Task type categorization (Study, Lecture, Exercise, Reading, etc.)
- Post-session feedback and productivity rating

### 2. Subject Management System
**Database Schema**: `userSubjects` table in Supabase
```typescript
interface Subject {
  id: string;
  user_id: string;
  name: string;
  color: string; // Hex color code
  created_at: string;
  updated_at: string;
}
```

**Features**:
- Color-coded subject organization
- Predefined color palette + custom color picker
- Subject CRUD operations with real-time sync
- Integration with timer and analytics

### 3. Task Management (Kanban + Table Views)
**Database Schema**: `todos` table in Supabase
```typescript
interface TodoItem {
  id: string;
  user_id: string;
  group_id?: string;
  title: string;
  description?: string;
  status: 'todo' | 'inProgress' | 'done';
  priority: 'low' | 'medium' | 'high';
  column_id: string; // For Kanban organization
  due_date?: string;
  assigned_to?: string;
  assigned_to_photo_url?: string;
  created_at: number;
  updated_at: number;
}
```

**Features**:
- Drag-and-drop Kanban board
- Real-time collaboration via Supabase subscriptions
- Priority levels with color coding
- Due date management
- Assignment to group members
- Table view with sorting/filtering

### 4. Study Analytics Dashboard
**Data Sources**:
- `study_sessions` table for timer data
- `users` table for daily targets and preferences
- Real-time calculations for streaks and achievements

**Features**:
- Daily/weekly/monthly study time breakdowns
- Subject-wise time distribution with color-coded charts
- Productivity rating trends
- Study streak tracking
- Goal setting and progress monitoring
- Pomodoro vs Stopwatch usage analytics

### 5. Groups & Collaboration
**Database Schema**: `groups` table in Supabase
```typescript
interface Group {
  id: string;
  name: string;
  description?: string;
  created_by: string;
  members: string[]; // Array of user IDs
  is_public: boolean;
  invite_code?: string;
  created_at: string;
  updated_at: string;
}
```

**Features**:
- Create/join study groups with invite codes
- Real-time member activity tracking
- Shared task boards
- Group study sessions (timer sync)
- Member role management

### 6. Mock Test Analysis
**Database Schema**: `mock_tests` table in Supabase
- CSV/JSON file upload via Supabase Storage
- Performance analytics with radar charts
- Topic-wise strength/weakness analysis
- Score progression tracking
- Comparison with peer performance

## Technical Implementation Requirements

### Authentication System
- **Primary**: Supabase Auth (email/password, Google OAuth)
- **Session Management**: JWT tokens with auto-refresh
- **Profile Management**: User preferences, subjects, daily targets

### Data Persistence Strategy
- **Real-time Data**: Supabase subscriptions for live updates
- **Local Storage**: AsyncStorage for timer state, user preferences
- **Background Sync**: Queue failed operations for retry when online
- **Offline Support**: Cache critical data for offline timer functionality

### Performance Requirements
- **Timer Accuracy**: Background execution with WorkManager (Android) / Background App Refresh (iOS)
- **Real-time Updates**: <500ms latency for collaborative features
- **App Launch**: <2s cold start time
- **Smooth Animations**: 60fps for all transitions and gestures

## Technology Stack

### Core Framework
```json
{
  "expo": "~51.0.0",
  "react-native": "0.74.x",
  "typescript": "^5.3.0"
}
```

### Navigation & State Management
```json
{
  "@react-navigation/native": "^6.1.0",
  "@react-navigation/stack": "^6.3.0",
  "@react-navigation/bottom-tabs": "^6.5.0",
  "zustand": "^4.4.0",
  "@tanstack/react-query": "^5.0.0"
}
```

### Backend Integration
```json
{
  "@supabase/supabase-js": "^2.47.0",
  "@react-native-async-storage/async-storage": "^1.19.0"
}
```

### Material 3 Expressive UI & Styling
```json
{
  "react-native-paper": "^5.12.0",
  "react-native-vector-icons": "^10.0.0",
  "react-native-svg": "^13.14.0",
  "react-native-linear-gradient": "^2.8.0",
  "@react-native-material/core": "^1.3.7",
  "react-native-material-you": "^1.2.0",
  "react-native-dynamic-color": "^1.0.0",
  "react-native-material-ripple": "^0.9.1",
  "react-native-blur": "^4.3.2"
}
```

### Charts & Animations
```json
{
  "victory-native": "^36.8.0",
  "react-native-reanimated": "^3.5.0",
  "react-native-gesture-handler": "^2.13.0",
  "lottie-react-native": "^6.4.0",
  "react-native-shared-element": "^0.8.4",
  "react-native-super-grid": "^4.9.6"
}
```

## Material 3 Expressive Design Implementation

### Expressive Color System & Theming
```typescript
// constants/expressiveTheme.ts
import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';

// Expressive color palettes with emotional impact
export const expressiveLightTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    // Primary - Vibrant Purple for Focus & Productivity
    primary: '#6750A4',
    onPrimary: '#FFFFFF',
    primaryContainer: '#EADDFF',
    onPrimaryContainer: '#21005D',

    // Secondary - Warm Teal for Balance
    secondary: '#006A6B',
    onSecondary: '#FFFFFF',
    secondaryContainer: '#6FF7F8',
    onSecondaryContainer: '#002020',

    // Tertiary - Energetic Orange for Motivation
    tertiary: '#8B5000',
    onTertiary: '#FFFFFF',
    tertiaryContainer: '#FFDCC0',
    onTertiaryContainer: '#2C1600',

    // Success - Fresh Green for Achievements
    success: '#006E26',
    onSuccess: '#FFFFFF',
    successContainer: '#6EFF82',
    onSuccessContainer: '#002106',

    // Warning - Attention Yellow
    warning: '#8B5000',
    onWarning: '#FFFFFF',
    warningContainer: '#FFDCC0',
    onWarningContainer: '#2C1600',

    // Error - Expressive Red
    error: '#BA1A1A',
    onError: '#FFFFFF',
    errorContainer: '#FFDAD6',
    onErrorContainer: '#410002',

    // Surface variations for depth
    surface: '#FEF7FF',
    onSurface: '#1D1B20',
    surfaceVariant: '#E7E0EC',
    onSurfaceVariant: '#49454F',
    surfaceTint: '#6750A4',

    // Enhanced outline system
    outline: '#79747E',
    outlineVariant: '#CAC4D0',

    // Expressive additions
    surfaceBright: '#FEF7FF',
    surfaceDim: '#DED8E1',
    surfaceContainer: '#F3EDF7',
    surfaceContainerHigh: '#ECE6F0',
    surfaceContainerHighest: '#E6E0E9',
    surfaceContainerLow: '#F9F2FC',
    surfaceContainerLowest: '#FFFFFF',
  }
};

export const expressiveDarkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    // Primary - Luminous Purple for Dark Mode
    primary: '#D0BCFF',
    onPrimary: '#381E72',
    primaryContainer: '#4F378B',
    onPrimaryContainer: '#EADDFF',

    // Secondary - Glowing Teal
    secondary: '#4DD8DA',
    onSecondary: '#003738',
    secondaryContainer: '#004F50',
    onSecondaryContainer: '#6FF7F8',

    // Tertiary - Warm Orange Glow
    tertiary: '#FFB77C',
    onTertiary: '#4A2800',
    tertiaryContainer: '#693C00',
    onTertiaryContainer: '#FFDCC0',

    // Success - Vibrant Green
    success: '#52DD6E',
    onSuccess: '#003910',
    successContainer: '#00531B',
    onSuccessContainer: '#6EFF82',

    // Enhanced dark surfaces
    surface: '#141218',
    onSurface: '#E6E0E9',
    surfaceVariant: '#49454F',
    onSurfaceVariant: '#CAC4D0',
    surfaceTint: '#D0BCFF',

    // Dark mode expressive additions
    surfaceBright: '#3B383E',
    surfaceDim: '#141218',
    surfaceContainer: '#211F26',
    surfaceContainerHigh: '#2B2930',
    surfaceContainerHighest: '#36343B',
    surfaceContainerLow: '#1D1B20',
    surfaceContainerLowest: '#0F0D13',
  }
};

// Expressive typography with emotional impact
export const expressiveTypography = {
  displayLarge: {
    fontFamily: 'System',
    fontWeight: '400',
    fontSize: 57,
    lineHeight: 64,
    letterSpacing: -0.25,
  },
  displayMedium: {
    fontFamily: 'System',
    fontWeight: '400',
    fontSize: 45,
    lineHeight: 52,
    letterSpacing: 0,
  },
  displaySmall: {
    fontFamily: 'System',
    fontWeight: '400',
    fontSize: 36,
    lineHeight: 44,
    letterSpacing: 0,
  },
  headlineLarge: {
    fontFamily: 'System',
    fontWeight: '600', // More expressive weight
    fontSize: 32,
    lineHeight: 40,
    letterSpacing: 0,
  },
  headlineMedium: {
    fontFamily: 'System',
    fontWeight: '600',
    fontSize: 28,
    lineHeight: 36,
    letterSpacing: 0,
  },
  headlineSmall: {
    fontFamily: 'System',
    fontWeight: '600',
    fontSize: 24,
    lineHeight: 32,
    letterSpacing: 0,
  },
  titleLarge: {
    fontFamily: 'System',
    fontWeight: '700', // Bold for emphasis
    fontSize: 22,
    lineHeight: 28,
    letterSpacing: 0,
  },
  titleMedium: {
    fontFamily: 'System',
    fontWeight: '600',
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: 0.15,
  },
  titleSmall: {
    fontFamily: 'System',
    fontWeight: '600',
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.1,
  },
  bodyLarge: {
    fontFamily: 'System',
    fontWeight: '400',
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: 0.5,
  },
  bodyMedium: {
    fontFamily: 'System',
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.25,
  },
  bodySmall: {
    fontFamily: 'System',
    fontWeight: '400',
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.4,
  },
  labelLarge: {
    fontFamily: 'System',
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.1,
  },
  labelMedium: {
    fontFamily: 'System',
    fontWeight: '500',
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.5,
  },
  labelSmall: {
    fontFamily: 'System',
    fontWeight: '500',
    fontSize: 11,
    lineHeight: 16,
    letterSpacing: 0.5,
  },
};
```

### Notifications & Background Tasks
```json
{
  "expo-notifications": "~0.25.0",
  "expo-background-fetch": "~12.0.0",
  "expo-task-manager": "~11.6.0",
  "@react-native-community/push-notification-ios": "^1.11.0"
}
```

### File Handling & Storage
```json
{
  "expo-document-picker": "~11.7.0",
  "expo-file-system": "~16.0.0",
  "react-native-fs": "^2.20.0"
}
```

## Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── timer/           # Timer-related components
│   ├── analytics/       # Chart and dashboard components
│   ├── tasks/           # Kanban and task management
│   ├── groups/          # Group collaboration components
│   └── common/          # Shared UI elements
├── screens/             # Screen components
│   ├── TimerScreen.tsx
│   ├── AnalyticsScreen.tsx
│   ├── TasksScreen.tsx
│   ├── GroupsScreen.tsx
│   └── ProfileScreen.tsx
├── services/            # API and backend integration
│   ├── supabase/        # Supabase client and utilities
│   └── notifications/   # Push notification service
├── stores/              # Zustand state management
│   ├── authStore.ts
│   ├── timerStore.ts
│   ├── tasksStore.ts
│   └── analyticsStore.ts
├── hooks/               # Custom React hooks
│   ├── useTimer.ts
│   ├── useSupabase.ts
│   └── useNotifications.ts
├── utils/               # Utility functions
│   ├── dateUtils.ts
│   ├── timerUtils.ts
│   └── validationUtils.ts
├── types/               # TypeScript type definitions
│   ├── database.ts
│   ├── timer.ts
│   └── user.ts
└── constants/           # App constants and configuration
    ├── colors.ts
    ├── subjects.ts
    └── config.ts
```

### Material 3 Expressive Component Examples

#### Expressive Timer Card with Dynamic Elevation
```typescript
// components/timer/ExpressiveTimerCard.tsx
import { Card, Text, useTheme } from 'react-native-paper';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolateColor,
  interpolate,
  Easing
} from 'react-native-reanimated';
import { BlurView } from '@react-native-community/blur';

const ExpressiveTimerCard = ({ isRunning, timeRemaining, mode, phase }) => {
  const theme = useTheme();
  const scale = useSharedValue(1);
  const progress = useSharedValue(0);
  const elevation = useSharedValue(2);
  const glowIntensity = useSharedValue(0);

  // Dynamic animations based on timer state
  const animatedStyle = useAnimatedStyle(() => {
    const phaseColors = {
      work: [theme.colors.surface, theme.colors.primaryContainer],
      shortBreak: [theme.colors.surface, theme.colors.secondaryContainer],
      longBreak: [theme.colors.surface, theme.colors.tertiaryContainer],
    };

    return {
      transform: [{
        scale: withSpring(scale.value, {
          damping: 15,
          stiffness: 150,
        })
      }],
      backgroundColor: interpolateColor(
        progress.value,
        [0, 1],
        phaseColors[phase] || phaseColors.work
      ),
      elevation: withTiming(elevation.value, {
        duration: 300,
        easing: Easing.bezier(0.4, 0, 0.2, 1),
      }),
      shadowOpacity: interpolate(glowIntensity.value, [0, 1], [0.1, 0.3]),
      shadowRadius: interpolate(glowIntensity.value, [0, 1], [4, 12]),
    };
  });

  // Pulsing effect for running timer
  const pulseStyle = useAnimatedStyle(() => ({
    opacity: isRunning ?
      withTiming(0.8, { duration: 1000, easing: Easing.inOut(Easing.ease) }) : 1,
  }));

  return (
    <Animated.View style={animatedStyle}>
      <Card mode="elevated" style={{ margin: 16, overflow: 'hidden' }}>
        {isRunning && (
          <BlurView
            style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 }}
            blurType="light"
            blurAmount={1}
          />
        )}
        <Animated.View style={pulseStyle}>
          <Card.Content style={{ padding: 32 }}>
            <Text
              variant="displayLarge"
              style={{
                textAlign: 'center',
                fontWeight: '300',
                color: theme.colors.onSurface,
              }}
            >
              {formatTime(timeRemaining)}
            </Text>
            <Text
              variant="titleMedium"
              style={{
                textAlign: 'center',
                marginTop: 8,
                color: theme.colors.onSurfaceVariant,
                textTransform: 'uppercase',
                letterSpacing: 1.5,
              }}
            >
              {phase === 'work' ? 'Focus Time' :
               phase === 'shortBreak' ? 'Short Break' : 'Long Break'}
            </Text>
          </Card.Content>
        </Animated.View>
      </Card>
    </Animated.View>
  );
};
```

#### Expressive Morphing FAB with State Transitions
```typescript
// components/common/ExpressiveMorphingFAB.tsx
import { FAB, useTheme } from 'react-native-paper';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  interpolateColor,
  runOnJS,
  Easing
} from 'react-native-reanimated';
import { Pressable } from 'react-native';
import MaterialRipple from 'react-native-material-ripple';

const ExpressiveMorphingFAB = ({
  icon,
  onPress,
  extended = false,
  state = 'idle', // idle, active, success, error
  label
}) => {
  const theme = useTheme();
  const width = useSharedValue(extended ? 120 : 56);
  const scale = useSharedValue(1);
  const rotation = useSharedValue(0);
  const colorProgress = useSharedValue(0);

  // State-based color mapping
  const getStateColors = () => {
    switch (state) {
      case 'active':
        return [theme.colors.primary, theme.colors.secondary];
      case 'success':
        return [theme.colors.primary, theme.colors.success];
      case 'error':
        return [theme.colors.primary, theme.colors.error];
      default:
        return [theme.colors.primary, theme.colors.primary];
    }
  };

  const animatedStyle = useAnimatedStyle(() => {
    const [startColor, endColor] = getStateColors();

    return {
      width: withTiming(width.value, {
        duration: 400,
        easing: Easing.bezier(0.4, 0, 0.2, 1),
      }),
      transform: [
        {
          scale: withSpring(scale.value, {
            damping: 12,
            stiffness: 200,
          })
        },
        {
          rotate: `${withTiming(rotation.value, {
            duration: 300,
            easing: Easing.bezier(0.4, 0, 0.2, 1),
          })}deg`
        }
      ],
      backgroundColor: interpolateColor(
        colorProgress.value,
        [0, 1],
        [startColor, endColor]
      ),
    };
  });

  const handlePress = () => {
    // Haptic feedback and visual response
    scale.value = 0.95;
    rotation.value = state === 'active' ? 180 : 0;
    colorProgress.value = withTiming(1, { duration: 200 });

    setTimeout(() => {
      scale.value = 1;
      colorProgress.value = withTiming(0, { duration: 300 });
    }, 150);

    runOnJS(onPress)();
  };

  return (
    <Animated.View style={[animatedStyle, {
      position: 'absolute',
      bottom: 16,
      right: 16,
      borderRadius: 28,
      overflow: 'hidden',
    }]}>
      <MaterialRipple
        onPress={handlePress}
        rippleColor={theme.colors.onPrimary}
        rippleOpacity={0.2}
        rippleDuration={400}
        style={{
          width: '100%',
          height: 56,
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: 'row',
        }}
      >
        <FAB.Icon icon={icon} size={24} color={theme.colors.onPrimary} />
        {extended && label && (
          <Text
            style={{
              color: theme.colors.onPrimary,
              marginLeft: 8,
              fontWeight: '600',
            }}
          >
            {label}
          </Text>
        )}
      </MaterialRipple>
    </Animated.View>
  );
};
```

## Environment Configuration

Copy these environment variables from `/WEB/.env`:

```env
# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=https://pcfrgvhigvklersufktf.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# App Configuration
EXPO_PUBLIC_APP_ENV=development
EXPO_PUBLIC_API_URL=https://api.isotopeai.com

# Material 3 Dynamic Colors (Android 12+)
EXPO_PUBLIC_ENABLE_DYNAMIC_COLORS=true
EXPO_PUBLIC_FALLBACK_SEED_COLOR=#6750A4

# Expressive Motion Settings
EXPO_PUBLIC_ENABLE_EXPRESSIVE_MOTION=true
EXPO_PUBLIC_REDUCE_MOTION_PREFERENCE=false
```

## Material 3 Expressive Motion System

### Motion Principles
```typescript
// constants/expressiveMotion.ts
export const ExpressiveEasing = {
  // Standard Material 3 Expressive easing curves
  standard: Easing.bezier(0.2, 0, 0, 1),
  decelerate: Easing.bezier(0, 0, 0.2, 1),
  accelerate: Easing.bezier(0.4, 0, 1, 1),

  // Expressive curves for emotional impact
  bounce: Easing.bezier(0.68, -0.55, 0.265, 1.55),
  elastic: Easing.bezier(0.175, 0.885, 0.32, 1.275),
  dramatic: Easing.bezier(0.25, 0.46, 0.45, 0.94),
};

export const ExpressiveDurations = {
  // Micro-interactions
  instant: 50,
  quick: 100,
  snappy: 200,

  // Standard transitions
  short: 300,
  medium: 400,
  long: 500,

  // Expressive transitions
  dramatic: 600,
  cinematic: 800,
  epic: 1000,
};

export const ExpressiveSpringConfigs = {
  gentle: { damping: 20, stiffness: 120 },
  bouncy: { damping: 8, stiffness: 200 },
  snappy: { damping: 15, stiffness: 300 },
  dramatic: { damping: 12, stiffness: 150 },
  playful: { damping: 6, stiffness: 180 },
};
```

### Expressive Component Behaviors
```typescript
// hooks/useExpressiveInteraction.ts
import { useSharedValue, withSpring, withTiming } from 'react-native-reanimated';
import { ExpressiveSpringConfigs, ExpressiveDurations } from '../constants/expressiveMotion';

export const useExpressiveInteraction = (config = {}) => {
  const scale = useSharedValue(1);
  const elevation = useSharedValue(2);
  const brightness = useSharedValue(1);
  const saturation = useSharedValue(1);

  const pressIn = () => {
    scale.value = withSpring(0.96, ExpressiveSpringConfigs.snappy);
    elevation.value = withTiming(8, { duration: ExpressiveDurations.quick });
    brightness.value = withTiming(1.1, { duration: ExpressiveDurations.quick });
    saturation.value = withTiming(1.2, { duration: ExpressiveDurations.quick });
  };

  const pressOut = () => {
    scale.value = withSpring(1, ExpressiveSpringConfigs.bouncy);
    elevation.value = withTiming(2, { duration: ExpressiveDurations.medium });
    brightness.value = withTiming(1, { duration: ExpressiveDurations.medium });
    saturation.value = withTiming(1, { duration: ExpressiveDurations.medium });
  };

  const success = () => {
    scale.value = withSpring(1.05, ExpressiveSpringConfigs.playful);
    setTimeout(() => {
      scale.value = withSpring(1, ExpressiveSpringConfigs.gentle);
    }, 200);
  };

  const error = () => {
    scale.value = withSpring(0.98, ExpressiveSpringConfigs.dramatic);
    setTimeout(() => {
      scale.value = withSpring(1, ExpressiveSpringConfigs.bouncy);
    }, 150);
  };

  return {
    scale,
    elevation,
    brightness,
    saturation,
    pressIn,
    pressOut,
    success,
    error,
  };
};
```

## Key Implementation Patterns

### 1. Timer State Management
```typescript
// stores/timerStore.ts
interface TimerState {
  status: 'idle' | 'running' | 'paused';
  mode: 'pomodoro' | 'stopwatch';
  currentPhase: 'work' | 'shortBreak' | 'longBreak';
  displayTime: number;
  selectedSubject: Subject | null;
  sessionStartTime: number | null;
  completedSessions: number;
}
```

### 2. Supabase Integration Pattern
```typescript
// services/supabase/client.ts
import { createClient } from '@supabase/supabase-js';
import { Database } from '../../types/database';

export const supabase = createClient<Database>(
  process.env.EXPO_PUBLIC_SUPABASE_URL!,
  process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!
);
```

### 3. Real-time Subscriptions
```typescript
// hooks/useSupabaseSubscription.ts
export const useTasksSubscription = (userId: string) => {
  useEffect(() => {
    const subscription = supabase
      .channel('todos')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'todos' },
        (payload) => {
          // Handle real-time updates
        }
      )
      .subscribe();

    return () => subscription.unsubscribe();
  }, [userId]);
};
```

## Development Workflow

## Expressive Animation Patterns

### 1. Dynamic Shared Element Transitions
```typescript
// components/animations/ExpressiveSharedElementTransition.tsx
import { SharedElement } from 'react-native-shared-element';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolateColor
} from 'react-native-reanimated';

const ExpressiveSubjectCard = ({ subject, onPress, isSelected }) => {
  const scale = useSharedValue(1);
  const elevation = useSharedValue(2);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{
      scale: withSpring(scale.value, {
        damping: 15,
        stiffness: 200,
      })
    }],
    elevation: withSpring(elevation.value),
    shadowOpacity: isSelected ? 0.3 : 0.1,
    shadowRadius: isSelected ? 8 : 4,
  }));

  const handlePressIn = () => {
    scale.value = 0.98;
    elevation.value = 8;
  };

  const handlePressOut = () => {
    scale.value = 1;
    elevation.value = isSelected ? 6 : 2;
  };

  return (
    <TouchableOpacity
      onPress={() => onPress(subject)}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={0.9}
    >
      <SharedElement id={`subject.${subject.id}.card`}>
        <Animated.View style={animatedStyle}>
          <Card style={{
            backgroundColor: subject.color,
            borderRadius: 16,
            overflow: 'hidden',
          }}>
            <LinearGradient
              colors={[subject.color, `${subject.color}CC`]}
              style={{ padding: 16 }}
            >
              <Text style={{
                color: 'white',
                fontWeight: '600',
                fontSize: 16,
              }}>
                {subject.name}
              </Text>
            </LinearGradient>
          </Card>
        </Animated.View>
      </SharedElement>
    </TouchableOpacity>
  );
};
```

### 2. Expressive Micro-interactions with Lottie
```typescript
// components/animations/ExpressiveSuccessAnimation.tsx
import LottieView from 'lottie-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSequence,
  withTiming,
  withSpring,
  runOnJS
} from 'react-native-reanimated';
import { useTheme } from 'react-native-paper';

const ExpressiveSuccessAnimation = ({
  visible,
  onComplete,
  type = 'success', // success, achievement, milestone
  intensity = 'normal' // subtle, normal, celebration
}) => {
  const theme = useTheme();
  const scale = useSharedValue(0);
  const opacity = useSharedValue(0);
  const rotation = useSharedValue(0);

  const getAnimationSource = () => {
    const animations = {
      success: {
        subtle: require('../../assets/animations/checkmark-subtle.json'),
        normal: require('../../assets/animations/success-normal.json'),
        celebration: require('../../assets/animations/success-celebration.json'),
      },
      achievement: {
        subtle: require('../../assets/animations/star-subtle.json'),
        normal: require('../../assets/animations/achievement-normal.json'),
        celebration: require('../../assets/animations/achievement-celebration.json'),
      },
      milestone: {
        subtle: require('../../assets/animations/trophy-subtle.json'),
        normal: require('../../assets/animations/milestone-normal.json'),
        celebration: require('../../assets/animations/milestone-celebration.json'),
      },
    };
    return animations[type][intensity];
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { rotate: `${rotation.value}deg` }
    ],
    opacity: opacity.value,
  }));

  React.useEffect(() => {
    if (visible) {
      // Entrance animation
      scale.value = withSequence(
        withSpring(1.2, { damping: 8, stiffness: 200 }),
        withSpring(1, { damping: 12, stiffness: 150 })
      );
      opacity.value = withTiming(1, { duration: 300 });

      if (intensity === 'celebration') {
        rotation.value = withSequence(
          withTiming(-5, { duration: 100 }),
          withTiming(5, { duration: 100 }),
          withTiming(0, { duration: 100 })
        );
      }
    } else {
      // Exit animation
      scale.value = withTiming(0, { duration: 200 });
      opacity.value = withTiming(0, { duration: 200 });
    }
  }, [visible]);

  const handleAnimationFinish = () => {
    if (onComplete) {
      runOnJS(onComplete)();
    }
  };

  if (!visible) return null;

  return (
    <Animated.View style={[
      animatedStyle,
      {
        position: 'absolute',
        alignSelf: 'center',
        zIndex: 1000,
      }
    ]}>
      <LottieView
        source={getAnimationSource()}
        autoPlay
        loop={intensity === 'celebration'}
        onAnimationFinish={handleAnimationFinish}
        style={{
          width: intensity === 'celebration' ? 300 : 200,
          height: intensity === 'celebration' ? 300 : 200,
        }}
        colorFilters={[
          {
            keypath: "**",
            color: theme.colors.primary,
          },
        ]}
      />
    </Animated.View>
  );
};
```

### 3. Gesture-based Interactions
```typescript
// components/timer/SwipeableTimer.tsx
import { PanGestureHandler } from 'react-native-gesture-handler';
import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  runOnJS,
  withSpring
} from 'react-native-reanimated';

const SwipeableTimer = ({ onSwipeUp, onSwipeDown }) => {
  const translateY = useSharedValue(0);

  const gestureHandler = useAnimatedGestureHandler({
    onStart: (_, context) => {
      context.startY = translateY.value;
    },
    onActive: (event, context) => {
      translateY.value = context.startY + event.translationY;
    },
    onEnd: (event) => {
      if (event.translationY < -50) {
        runOnJS(onSwipeUp)();
      } else if (event.translationY > 50) {
        runOnJS(onSwipeDown)();
      }
      translateY.value = withSpring(0);
    },
  });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  return (
    <PanGestureHandler onGestureEvent={gestureHandler}>
      <Animated.View style={animatedStyle}>
        {/* Timer content */}
      </Animated.View>
    </PanGestureHandler>
  );
};
```

## 🚀 Detailed Phase Implementation Guide

### Phase 1: Project Setup & Foundation (8 Tasks)

#### 1.1 Initialize Expo Project
```bash
npx create-expo-app IsotopeAI --template
cd IsotopeAI
```

#### 1.2 Configure Development Environment
- Setup VS Code extensions for React Native
- Configure ESLint and Prettier
- Install development tools

#### 1.3 Install Core Dependencies
```bash
# Core framework
npm install react-native@0.74.x typescript@^5.3.0

# Navigation and State Management
npm install @react-navigation/native @react-navigation/stack @react-navigation/bottom-tabs
npm install zustand@^4.4.0 @tanstack/react-query@^5.0.0
```

#### 1.4 Setup Project Structure
```
src/
├── components/           # Reusable UI components
│   ├── timer/           # Timer-related components
│   ├── analytics/       # Chart and dashboard components
│   ├── tasks/           # Kanban and task management
│   ├── groups/          # Group collaboration components
│   └── common/          # Shared UI elements
├── screens/             # Screen components
├── services/            # API and backend integration
├── stores/              # Zustand state management
├── hooks/               # Custom React hooks
├── utils/               # Utility functions
├── types/               # TypeScript type definitions
└── constants/           # App constants and configuration
```

### Phase 2: Backend Integration & Authentication (10 Tasks)

#### 2.1 Install Supabase Dependencies
```bash
npm install @supabase/supabase-js@^2.47.0
npm install @react-native-async-storage/async-storage@^1.19.0
```

#### 2.2 Copy Environment Configuration
Copy from `/WEB/.env`:
```env
EXPO_PUBLIC_SUPABASE_URL=https://pcfrgvhigvklersufktf.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
EXPO_PUBLIC_APP_ENV=development
```

#### 2.3 Setup Supabase Client
```typescript
// services/supabase/client.ts
import { createClient } from '@supabase/supabase-js';
import { Database } from '../../types/database';

export const supabase = createClient<Database>(
  process.env.EXPO_PUBLIC_SUPABASE_URL!,
  process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!
);
```

#### 2.4 Implement Authentication Store
```typescript
// stores/authStore.ts
interface AuthState {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
}
```

### Phase 3: Material 3 Expressive Design System (10 Tasks)

#### 3.1 Install Material 3 Dependencies
```bash
# Material 3 UI Components
npm install react-native-paper@^5.12.0
npm install @react-native-material/core@^1.3.7
npm install react-native-material-you@^1.2.0
npm install react-native-vector-icons@^10.0.0

# Styling and Effects
npm install react-native-svg@^13.14.0
npm install react-native-linear-gradient@^2.8.0
npm install react-native-dynamic-color@^1.0.0
npm install react-native-material-ripple@^0.9.1
npm install react-native-blur@^4.3.2
```

#### 3.2 Create Expressive Theme Configuration
```typescript
// constants/expressiveTheme.ts
export const expressiveLightTheme = {
  colors: {
    primary: '#6750A4',        // Vibrant Purple for Focus
    secondary: '#006A6B',      // Warm Teal for Balance
    tertiary: '#8B5000',       // Energetic Orange for Motivation
    success: '#006E26',        // Fresh Green for Achievements
    // ... comprehensive color system
  }
};
```

### Phase 4: Core Timer System Implementation (12 Tasks)

#### 4.1 Create Timer Database Schema
```sql
CREATE TABLE study_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  subject TEXT NOT NULL,
  task_name TEXT,
  task_type TEXT,
  start_time TIMESTAMPTZ,
  end_time TIMESTAMPTZ,
  duration INTEGER, -- seconds
  mode TEXT CHECK (mode IN ('pomodoro', 'stopwatch')),
  phase TEXT CHECK (phase IN ('work', 'shortBreak', 'longBreak', 'pause')),
  completed BOOLEAN DEFAULT false,
  date DATE,
  notes TEXT,
  productivity_rating INTEGER CHECK (productivity_rating >= 1 AND productivity_rating <= 5)
);
```

#### 4.2 Build Timer State Management
```typescript
// stores/timerStore.ts
interface TimerState {
  status: 'idle' | 'running' | 'paused';
  mode: 'pomodoro' | 'stopwatch';
  currentPhase: 'work' | 'shortBreak' | 'longBreak';
  displayTime: number;
  selectedSubject: Subject | null;
  sessionStartTime: number | null;
  completedSessions: number;
}
```

### Phase 5: Subject Management System (10 Tasks)

#### 5.1 Create Subject Database Schema
```sql
CREATE TABLE userSubjects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  name TEXT NOT NULL,
  color TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### 5.2 Add Daily Time Display Feature
**Special Implementation**: Display daily time in seconds in subject manager as requested by user.

```typescript
// components/subjects/SubjectCard.tsx
const SubjectCard = ({ subject }) => {
  const dailyTimeInSeconds = useDailyTimeForSubject(subject.id);

  return (
    <Card>
      <Text>{subject.name}</Text>
      <Text>Daily Time: {dailyTimeInSeconds}s</Text>
    </Card>
  );
};
```

### Development Workflow Commands

```bash
# Start development server
npx expo start

# Run on specific platform
npx expo start --ios
npx expo start --android

# Install dependencies for each phase
npm install # (specific packages per phase)

# Build for production
npx expo build:ios
npx expo build:android
```

### Phase 6: Task Management (Kanban + Table Views) (12 Tasks)

#### 6.1 Install Drag-and-Drop Dependencies
```bash
npm install react-native-super-grid@^4.9.6
npm install react-native-gesture-handler@^2.13.0
```

#### 6.2 Create Task Database Schema
```sql
CREATE TABLE todos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  group_id UUID REFERENCES groups(id),
  title TEXT NOT NULL,
  description TEXT,
  status TEXT CHECK (status IN ('todo', 'inProgress', 'done')),
  priority TEXT CHECK (priority IN ('low', 'medium', 'high')),
  column_id TEXT,
  due_date TIMESTAMPTZ,
  assigned_to UUID REFERENCES auth.users(id),
  assigned_to_photo_url TEXT,
  created_at BIGINT,
  updated_at BIGINT
);
```

### Phase 7: Study Analytics Dashboard (12 Tasks)

#### 7.1 Install Charts Dependencies
```bash
npm install victory-native@^36.8.0
```

#### 7.2 Replicate Productivity Page from WEB
**Critical Task**: Replicate the exact functionality from @WEB/ productivity page with identical features and data visualization.

```typescript
// screens/AnalyticsScreen.tsx
const AnalyticsScreen = () => {
  // Implement identical functionality to WEB productivity page
  return (
    <ScrollView>
      <TimeDistributionChart />
      <ProductivityTrends />
      <StudyStreakTracker />
      <GoalProgressIndicators />
      <PomodoroVsStopwatchAnalytics />
    </ScrollView>
  );
};
```

### Phase 8: Groups & Collaboration Features (12 Tasks)

#### 8.1 Create Groups Database Schema
```sql
CREATE TABLE groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  created_by UUID REFERENCES auth.users(id),
  members UUID[] DEFAULT '{}',
  is_public BOOLEAN DEFAULT false,
  invite_code TEXT UNIQUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Phase 9: Mock Test Analysis System (12 Tasks)

#### 9.1 Install File Handling Dependencies
```bash
npx expo install expo-document-picker@~11.7.0
npx expo install expo-file-system@~16.0.0
npm install react-native-fs@^2.20.0
```

### Phase 10: Advanced Animations & Interactions (12 Tasks)

#### 10.1 Install Animation Dependencies
```bash
npm install react-native-reanimated@^3.5.0
npm install react-native-gesture-handler@^2.13.0
npm install lottie-react-native@^6.4.0
npm install react-native-shared-element@^0.8.4
```

#### 10.2 Create Expressive Motion System
```typescript
// constants/expressiveMotion.ts
export const ExpressiveEasing = {
  standard: Easing.bezier(0.2, 0, 0, 1),
  bounce: Easing.bezier(0.68, -0.55, 0.265, 1.55),
  elastic: Easing.bezier(0.175, 0.885, 0.32, 1.275),
};

export const ExpressiveDurations = {
  quick: 100,
  snappy: 200,
  medium: 400,
  dramatic: 600,
  cinematic: 800,
};
```

### Phase 11: Background Tasks & Notifications (12 Tasks)

#### 11.1 Install Background Task Dependencies
```bash
npx expo install expo-notifications@~0.25.0
npx expo install expo-background-fetch@~12.0.0
npx expo install expo-task-manager@~11.6.0
npm install @react-native-community/push-notification-ios@^1.11.0
```

### Phase 12: Testing & Quality Assurance (12 Tasks)

#### 12.1 Setup Testing Framework
```bash
npm install --save-dev jest @testing-library/react-native detox
```

#### Testing Strategy:
- **Unit Tests**: Timer logic, data transformations, validation functions
- **Integration Tests**: Supabase CRUD operations, real-time subscriptions, authentication flows
- **E2E Tests**: Complete user workflows, timer functionality across app states, offline/online synchronization

### Phase 13: Performance Optimization (12 Tasks)

#### Performance Requirements:
- **Timer Accuracy**: Background execution with WorkManager (Android) / Background App Refresh (iOS)
- **Real-time Updates**: <500ms latency for collaborative features
- **App Launch**: <2s cold start time
- **Smooth Animations**: 60fps for all transitions and gestures

### Phase 14: Deployment & Distribution (12 Tasks)

#### 14.1 Setup EAS Build Configuration
```bash
npm install -g @expo/cli
npx expo install expo-dev-client
eas build:configure
```

#### Deployment Strategy:
- **Development**: Expo Go for rapid testing, EAS Build for internal testing builds
- **Production**: EAS Build for app store builds, Over-the-air updates for non-native changes
- **Distribution**: App Store Connect / Google Play Console deployment

## 🎯 Key Implementation Notes

### Critical Features:
✅ **Complete Firebase Removal** - All backend functionality moved to Supabase
✅ **Material 3 Expressive Design** - Enhanced emotional impact and personalization
✅ **Beautiful Animations** - React Native Reanimated 3 with Lottie micro-interactions
✅ **Productivity Page Replication** - Exact functionality from @WEB/
✅ **Daily Time Display** - Seconds display in subject manager as requested

### Performance Targets:
- 60fps animations with proper worklet usage
- <2s cold start time
- <500ms real-time latency
- Smooth background timer execution

---

**Last Updated**: June 16, 2025
**Version**: 2.0.0 - Comprehensive Phase-wise Implementation Plan
**Target Platforms**: iOS 13+, Android 8+ (API 26+)
**Total Implementation**: 14 Phases, 158 Detailed Tasks

