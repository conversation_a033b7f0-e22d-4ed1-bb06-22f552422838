# [IsotopeAI](https://isotopeai.in)

**IsotopeAI** is an AI-powered platform designed to solve your doubts in **Physics**, **Chemistry**, and **Mathematics**. Whether you're a high school student, a competitive exam aspirant (JEE, NEET, BITSAT), or just a curious learner, IsotopeAI is your go-to solution for clarifying concepts, solving problems, and learning efficiently.

## ✨ Features

- **AI-Powered Assistance**: Get instant, accurate, and step-by-step explanations for your questions in PCMB.
- **Subject-Specific Expertise**: Focused on Physics, Chemistry, and Maths to provide precise solutions tailored to each subject.
- **User-Friendly Interface**: A clean and intuitive UI designed for seamless learning.
- **24/7 Availability**: Solve your doubts anytime, anywhere.
- **Personalized Responses**: Receive answers tailored to your level of understanding.
- **Image Uploads**: Upload images of your questions.
- **Progressive Web App**: Install on your device for offline access.

## 🚀 How It Works

1. **Ask Your Question**: Type or upload your query in the input field. You can also upload an image of your question.
2. **AI Processing**: The AI model analyzes your question and generates an accurate response.
3. **Detailed Explanation**: View the step-by-step solution or concept breakdown.
4. **Follow-Up Questions**: Clarify further doubts based on the response.

## 📚 Ideal For

- **JEE/NEET Aspirants**: Get help with advanced PCMB concepts
- **High School Students**: Understand fundamental principles
- **University Students**: Master complex topics
- **Competitive Exam Preparation**: Practice with expert guidance
- **Self-Learners**: Explore topics at your own pace

## 🛠️ Built With

- **React.js**: Frontend framework
- **Tailwind CSS**: Styling
- **Gemini API**: AI model
- **Cloudinary**: Cloud-based image storage and management
- **Firebase**: Backend and authentication
- **TypeScript**: Type safety
- **Netlify**: Hosting and serverless functions

## 🌟 Why Choose IsotopeAI?

- **PCMB Focused**: Specialized in Physics, Chemistry, and Mathematics
- **AI-Powered**: Leverages advanced AI for accurate solutions
- **Step-by-Step Solutions**: Detailed explanations for better understanding
- **Free to Use**: Access quality education without barriers
- **Mobile-Friendly**: Learn on any device, anywhere
- **Offline Access**: Install as a PWA for offline use

## Recent Updates

### Inactive User Cleanup
- The application now automatically deletes inactive user accounts and their data after 3 months of inactivity
- Users will receive notification warnings at 2.5 months and 2.75 months of inactivity
- User activity is tracked across the application to ensure accurate inactivity detection
- This helps maintain database performance and reduces storage costs by cleaning up unused accounts

---

## Setup Procedure
- Get API key from: [Google AI Studio](https://aistudio.google.com/app/apikey)
- Get Brevo API key from: [Brevo](https://app.brevo.com/) (for welcome emails)
- Add these keys to your `.env` file
- `npm install`
- `npm run dev`

## Brevo Email Campaign Setup
1. Create a Brevo account at [app.brevo.com](https://app.brevo.com) if you don't have one already
2. Create a new email campaign:
   - Navigate to 'Campaigns' > 'Email' > 'Create an email campaign'
   - Design your welcome email using Brevo's template editor or HTML editor
   - Use personalization variables like `{FIRSTNAME}` and `{REGISTRATION_DATE}` in your template
3. Save the campaign as a template
4. Get the campaign ID (visible in the URL when editing the campaign)
5. Update the campaign ID in `src/utils/emailUtils.ts`
6. Ensure your Brevo API key is set in the `.env` file as `VITE_BREVO_API_KEY`

### Personalizing Your Welcome Emails
The integration automatically adds the following personalization attributes to your Brevo contacts:
- `FIRSTNAME`: The user's display name or first part of their email if no name is available
- `REGISTRATION_DATE`: The date when the user registered in YYYY-MM-DD format

You can use these variables in your Brevo email templates with:
```
Hello {FIRSTNAME},
Thank you for joining us on {REGISTRATION_DATE}!
```

### Managing Contact Lists
To add users to specific contact lists in Brevo:
1. Create lists in Brevo: go to 'Contacts' > 'Lists' > 'Create a list'
2. Get the list ID(s) (visible in the URL when viewing a list)
3. Modify the `sendWelcomeEmail` function in `src/utils/emailUtils.ts` to add users to your lists:
   ```typescript
   await createOrUpdateContact(email, displayName, [2, 3]); // Replace 2, 3 with your list IDs
   ```

## Keywords
JEE preparation, NEET preparation, Physics tutor, Chemistry help, Mathematics solver, PCMB doubts, AI tutor, online doubt solving, competitive exam preparation, educational technology, free doubt solving, PCMB learning platform