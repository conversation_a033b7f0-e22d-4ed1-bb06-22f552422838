rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // --- NEW: Rules for Public Q&A (Initial Question/Answer) ---
    match /public_qna/{slug} {
      // Allow anyone to read documents that are marked as 'approved'
      allow read: if resource.data.status == 'approved';
      // Disallow public writes entirely. Writes must use Admin SDK.
      allow write: if false;
    }

    // --- NEW: Rules for Private User Chats (Full Conversation History) ---
    match /user_chats/{messageId} {
      // Allow read/write only if the requesting user's ID matches the userId field.
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
      // Allow authenticated users to create new messages for themselves.
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
    }

    // --- Existing Rules ---
    // Existing chat comments rules (Assuming 'chats' is different from 'user_chats')
    match /chats/{chatId}/comments/{commentId} {
      allow read: if true; // Consider if this should be restricted
      // Basic validation for comment creation
      allow write: if request.auth != null && request.resource.data.keys().hasAll(['content', 'author', 'timestamp']); // Added auth check
    }

    // User profiles
    match /users/{userId} {
      allow read: if true; // Consider if profile data should be public
      allow write: if request.auth != null && request.auth.uid == userId;
    }

    // --- IMPORTANT: Wildcard rule ---
    // This rule applies to any collection NOT specifically matched above.
    // Your original rule allowed open read/write. This is generally unsafe.
    // Consider restricting this further if possible.
    // If you need open access for specific other collections, add explicit rules for them above.
    match /{document=**} {
      allow read: if true; // Keep existing read access if needed
      allow write: if request.auth != null; // Allow write only if logged in (basic protection)
      // Or keep 'allow write: if true;' if you absolutely need open writes for other collections
    }
  }
}