# Firebase Functions

This directory contains Firebase Cloud Functions that provide server-side functionality for the application.

## Inactive User Cleanup

We've implemented automated inactive user cleanup to maintain database performance and reduce storage costs by removing unused accounts and their data.

### Features

1. **Automatic User Deletion**: Users who have been inactive for 3 months will have their accounts and associated data deleted.
2. **Warning Notifications**: Users will receive warning notifications at 2.5 months and 2.75 months of inactivity.
3. **Activity Tracking**: User activity is tracked throughout the app via event listeners to ensure accurate inactivity detection.

### Important Requirements

**⚠️ Blaze Plan Required**: These functions use scheduled triggers which require the Firebase Blaze (pay-as-you-go) plan.

To deploy these functions, upgrade your project to the Blaze plan:
1. Visit the [Firebase Console](https://console.firebase.google.com)
2. Select your project
3. Navigate to "Usage and Billing" > "Details & Settings"
4. Click "Modify plan" and select "Blaze"

### Cost Considerations

The Blaze plan is pay-as-you-go, but these functions are designed to be cost-efficient:
- Scheduled functions run only once per week at low-traffic hours
- Each execution processes users in batches to minimize function runtime
- The Cloud Functions free tier includes:
  - 2 million invocations per month
  - 400,000 GB-seconds of compute time
  - 200,000 CPU-seconds of compute time

For most applications, these functions will operate within the free tier limits.

### Deployment

Once you've upgraded to the Blaze plan, deploy the functions with:

```bash
npm run deploy
```

Or for just the specific functions:

```bash
firebase deploy --only functions:notifyInactiveUsers,functions:cleanupInactiveUsers
``` 