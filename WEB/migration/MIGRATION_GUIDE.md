# IsotopeAI Firebase to Supabase Migration Guide

## Overview

This guide outlines the complete migration process from Firebase to Supabase for the IsotopeAI application. The migration is designed to be seamless for users while providing better performance, cost efficiency, and developer experience.

## Migration Architecture

### Phase 1: Database Schema Setup ✅
- [x] Analyzed existing Firebase collections and data structures
- [x] Designed comprehensive Supabase schema with proper relationships
- [x] Created RLS (Row Level Security) policies for data protection
- [x] Set up indexes for optimal performance

### Phase 2: Export System (legacy.isotopeai.in)
- [x] Created legacy export interface
- [x] Built Firebase data extraction utilities
- [x] Implemented CSV export functionality for all collections
- [x] Added user authentication and data validation

### Phase 3: Import System (main site)
- [x] Created migration page component
- [x] Built CSV parsing and validation utilities
- [x] Implemented data transformation and import logic
- [x] Added progress tracking and error handling

### Phase 4: Code Migration (In Progress)
- [ ] Replace Firebase SDK calls with Supabase client calls
- [ ] Update authentication system
- [ ] Migrate real-time subscriptions
- [ ] Update all CRUD operations

## Data Collections Migrated

### 1. AI Chats (`aiChats` → `ai_chats`)
- **Purpose**: AI conversation history and shared chats
- **Key Fields**: id, user_id, title, messages, is_public, view_count, slug, status
- **Special Handling**: Comments are extracted to separate `chat_comments` table

### 2. Groups (`groups` → `groups`)
- **Purpose**: Chat groups and their metadata
- **Key Fields**: id, name, members, created_by, is_public, invite_code
- **Special Handling**: Messages are extracted to separate `messages` table

### 3. Todos (`todos` → `todos`)
- **Purpose**: Task management and kanban boards
- **Key Fields**: id, title, description, priority, created_by, group_id, column_id
- **Special Handling**: Maintains kanban board structure

### 4. User Subjects (`userSubjects` → `user_subjects`)
- **Purpose**: Custom study subjects with colors
- **Key Fields**: id, user_id, name, color
- **Special Handling**: Migrates from nested document structure

### 5. Exams (`exams` → `exams`)
- **Purpose**: Exam countdown and scheduling
- **Key Fields**: id, user_id, name, date, subject_marks
- **Special Handling**: Enhanced with mock test integration

### 6. User Data (`users` → multiple tables)
- **Purpose**: User profiles, study sessions, mock tests
- **Migration Strategy**: 
  - Profile data → `users` table
  - Study sessions → `study_sessions` table
  - Mock tests → `mock_tests` table

## Security Implementation

### Row Level Security (RLS) Policies

1. **User Data Protection**
   - Users can only access their own data
   - Public content is accessible to all users
   - Group members can access shared group data

2. **Authentication Requirements**
   - All write operations require authentication
   - Read operations follow visibility rules
   - Admin operations are restricted

3. **Data Isolation**
   - Multi-tenant architecture ensures data separation
   - Foreign key constraints maintain referential integrity
   - Audit trails for sensitive operations

## Performance Optimizations

### Database Indexes
- User-specific queries: `user_id` indexes on all tables
- Time-based queries: `created_at`, `updated_at` indexes
- Search functionality: Full-text search indexes
- Relationship queries: Foreign key indexes

### Query Optimization
- Efficient pagination with cursor-based navigation
- Selective field loading to reduce bandwidth
- Batch operations for bulk data processing
- Connection pooling for high concurrency

## Migration Process for Users

### Step 1: Export Data (legacy.isotopeai.in)
1. Visit `https://legacy.isotopeai.in`
2. Sign in with Google account
3. Export each data collection as CSV files:
   - AI Chats (`isotope-ai-chats.csv`)
   - Groups & Messages (`isotope-groups-messages.csv`)
   - Todos & Tasks (`isotope-todos.csv`)
   - Subjects (`isotope-subjects.csv`)
   - Exams (`isotope-exams.csv`)
   - User Profile & Analytics (`isotope-user-data.csv`)

### Step 2: Import Data (main site)
1. Visit `https://isotopeai.in/migration`
2. Sign in with the same Google account
3. Upload each CSV file to import data
4. Monitor progress and verify successful import
5. Test functionality to ensure data integrity

### Step 3: Verification
1. Check that all conversations are accessible
2. Verify group memberships and messages
3. Confirm todos and subjects are properly imported
4. Test study analytics and mock test data

## Technical Implementation Details

### Data Transformation Rules

1. **Timestamp Conversion**
   - Firebase Timestamps → ISO 8601 strings
   - Millisecond timestamps → PostgreSQL timestamps
   - Structured dates → Standard date formats

2. **JSON Field Handling**
   - Firebase arrays → PostgreSQL arrays
   - Nested objects → JSONB fields
   - Complex structures → Normalized tables

3. **ID Management**
   - Preserve original Firebase document IDs
   - Generate UUIDs for new records
   - Maintain referential integrity

### Error Handling

1. **Import Validation**
   - CSV format validation
   - Data type checking
   - Required field verification
   - Duplicate detection

2. **Rollback Mechanisms**
   - Transaction-based imports
   - Backup creation before migration
   - Selective rollback capabilities
   - Error logging and reporting

## Post-Migration Tasks

### 1. Code Updates Required
- [ ] Update Firebase imports to Supabase
- [ ] Replace Firestore queries with Supabase queries
- [ ] Update authentication flow
- [ ] Migrate real-time listeners
- [ ] Update environment variables

### 2. Testing Checklist
- [ ] User authentication and authorization
- [ ] AI chat functionality
- [ ] Group messaging
- [ ] Todo management
- [ ] Study analytics
- [ ] Mock test analysis
- [ ] Real-time updates
- [ ] Mobile responsiveness

### 3. Performance Monitoring
- [ ] Query performance analysis
- [ ] Database connection monitoring
- [ ] Error rate tracking
- [ ] User experience metrics

## Rollback Plan

In case of issues during migration:

1. **Immediate Rollback**
   - Switch DNS back to Firebase version
   - Restore Firebase rules and indexes
   - Notify users of temporary service restoration

2. **Data Recovery**
   - Restore from Firebase backups
   - Re-export user data if needed
   - Provide manual data recovery tools

3. **Communication Plan**
   - User notification system
   - Status page updates
   - Support channel activation

## Support and Troubleshooting

### Common Issues
1. **CSV Format Errors**: Ensure proper encoding and escaping
2. **Authentication Failures**: Verify Google account consistency
3. **Data Validation Errors**: Check required fields and data types
4. **Import Timeouts**: Break large datasets into smaller chunks

### Support Channels
- Migration help page: `/migration-help`
- Email support: <EMAIL>
- Discord community: [Link to Discord]
- Documentation: [Link to docs]

## Timeline and Milestones

### Phase 1: Preparation (Completed)
- ✅ Schema design and implementation
- ✅ Export system development
- ✅ Import system development

### Phase 2: User Migration (Current)
- 🔄 User communication and guidance
- 🔄 Gradual user migration
- 🔄 Support and troubleshooting

### Phase 3: Code Migration (Next)
- ⏳ Firebase to Supabase code updates
- ⏳ Real-time functionality migration
- ⏳ Performance optimization

### Phase 4: Completion
- ⏳ Firebase service deprecation
- ⏳ Legacy system cleanup
- ⏳ Performance monitoring and optimization

## Success Metrics

1. **Migration Completion Rate**: Target 95% user migration
2. **Data Integrity**: 100% data accuracy post-migration
3. **Performance Improvement**: 50% faster query response times
4. **Cost Reduction**: 60% reduction in database costs
5. **User Satisfaction**: Maintain 90%+ user satisfaction scores

## Contact Information

For technical questions or migration support:
- **Development Team**: <EMAIL>
- **Migration Lead**: Arnav Singh (<EMAIL>)
- **Emergency Contact**: [Emergency contact details]

---

*This migration guide is a living document and will be updated as the migration progresses.*
