#!/bin/bash

# Deploy script for IsotopeAI Legacy Export System
# This script helps deploy the export system to Netlify

echo "🚀 IsotopeAI Legacy Export System Deployment"
echo "============================================="

# Check if we're in the right directory
if [ ! -f "migration/legacy-export/index.html" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    echo "   Expected to find: migration/legacy-export/index.html"
    exit 1
fi

echo "📁 Checking export system files..."

# Check required files
required_files=(
    "migration/legacy-export/index.html"
    "migration/legacy-export/export.js"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file - MISSING"
        exit 1
    fi
done

echo ""
echo "🔧 Deployment Options:"
echo "======================"
echo ""
echo "Option 1: Manual Upload to Netlify"
echo "-----------------------------------"
echo "1. Go to https://app.netlify.com/"
echo "2. Drag and drop the 'migration/legacy-export/' folder"
echo "3. Set custom domain to 'legacyisotopeai.netlify.app'"
echo ""
echo "Option 2: Netlify CLI (if installed)"
echo "-----------------------------------"
echo "1. cd migration/legacy-export/"
echo "2. netlify deploy --prod"
echo ""
echo "Option 3: Git-based Deployment"
echo "------------------------------"
echo "1. Create new repo with legacy-export/ contents"
echo "2. Connect to Netlify"
echo "3. Auto-deploy on push"
echo ""

# Check if Firebase config is updated
if grep -q "YOUR_FIREBASE_API_KEY" migration/legacy-export/export.js; then
    echo "⚠️  WARNING: Firebase configuration not updated!"
    echo "   Please update the Firebase config in migration/legacy-export/export.js"
    echo "   Replace 'YOUR_FIREBASE_API_KEY' etc. with actual values"
    echo ""
fi

echo "📋 Pre-deployment Checklist:"
echo "============================"
echo "[ ] Firebase configuration updated in export.js"
echo "[ ] Admin email (<EMAIL>) is correct"
echo "[ ] All required files present"
echo "[ ] Domain configured (legacyisotopeai.netlify.app)"
echo ""

echo "🧪 Testing Instructions:"
echo "========================"
echo "After deployment:"
echo "1. Visit https://legacyisotopeai.netlify.app/"
echo "2. Sign in with Google"
echo "3. Test export functionality"
echo "4. Verify admin features <NAME_EMAIL>"
echo ""

echo "✅ Ready for deployment!"
echo ""
echo "📞 Need help? Check migration/SETUP_GUIDE.md"
