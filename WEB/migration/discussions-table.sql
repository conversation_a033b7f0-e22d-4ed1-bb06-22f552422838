-- Create discussions table for AI chat discussions
-- This replaces the old chat_comments table with a cleaner structure

-- Drop existing chat_comments table if it exists
DROP TABLE IF EXISTS chat_comments CASCADE;

-- Create new discussions table
CREATE TABLE IF NOT EXISTS discussions (
    id TEXT PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    chat_id TEXT NOT NULL,
    author_id TEXT NOT NULL,
    author_name TEXT,
    author_username TEXT,
    author_photo_url TEXT,
    content TEXT NOT NULL,
    parent_id TEXT REFERENCES discussions(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_discussions_chat_id ON discussions(chat_id);
CREATE INDEX IF NOT EXISTS idx_discussions_parent_id ON discussions(parent_id);
CREATE INDEX IF NOT EXISTS idx_discussions_author_id ON discussions(author_id);
CREATE INDEX IF NOT EXISTS idx_discussions_created_at ON discussions(created_at DESC);

-- Enable Row Level Security
ALTER TABLE discussions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for discussions (public read, authenticated write)
CREATE POLICY "Discussions are viewable by everyone" ON discussions FOR SELECT USING (true);
CREATE POLICY "Authenticated users can insert discussions" ON discussions FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Users can update their own discussions" ON discussions FOR UPDATE USING (auth.uid()::text = author_id);
CREATE POLICY "Users can delete their own discussions" ON discussions FOR DELETE USING (auth.uid()::text = author_id);

-- Function to get discussions with replies in hierarchical structure
CREATE OR REPLACE FUNCTION get_discussions_for_chat(p_chat_id TEXT)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    WITH RECURSIVE discussion_tree AS (
        -- Base case: get all root discussions (no parent)
        SELECT 
            id, chat_id, author_id, author_name, author_username, 
            author_photo_url, content, parent_id, created_at, updated_at,
            0 as level,
            ARRAY[id] as path
        FROM discussions 
        WHERE chat_id = p_chat_id AND parent_id IS NULL
        
        UNION ALL
        
        -- Recursive case: get replies
        SELECT 
            d.id, d.chat_id, d.author_id, d.author_name, d.author_username,
            d.author_photo_url, d.content, d.parent_id, d.created_at, d.updated_at,
            dt.level + 1,
            dt.path || d.id
        FROM discussions d
        INNER JOIN discussion_tree dt ON d.parent_id = dt.id
        WHERE d.chat_id = p_chat_id
    )
    SELECT json_agg(
        json_build_object(
            'id', id,
            'chat_id', chat_id,
            'author_id', author_id,
            'author_name', author_name,
            'author_username', author_username,
            'author_photo_url', author_photo_url,
            'content', content,
            'parent_id', parent_id,
            'created_at', created_at,
            'updated_at', updated_at,
            'level', level
        ) ORDER BY created_at ASC
    ) INTO result
    FROM discussion_tree;
    
    RETURN COALESCE(result, '[]'::json);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to add a new discussion comment
CREATE OR REPLACE FUNCTION add_discussion(
    p_chat_id TEXT,
    p_author_id TEXT,
    p_author_name TEXT,
    p_author_username TEXT,
    p_author_photo_url TEXT DEFAULT NULL,
    p_content TEXT,
    p_parent_id TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    new_discussion discussions%ROWTYPE;
BEGIN
    INSERT INTO discussions (
        chat_id, author_id, author_name, author_username, 
        author_photo_url, content, parent_id
    ) VALUES (
        p_chat_id, p_author_id, p_author_name, p_author_username,
        p_author_photo_url, p_content, p_parent_id
    ) RETURNING * INTO new_discussion;
    
    RETURN json_build_object(
        'id', new_discussion.id,
        'chat_id', new_discussion.chat_id,
        'author_id', new_discussion.author_id,
        'author_name', new_discussion.author_name,
        'author_username', new_discussion.author_username,
        'author_photo_url', new_discussion.author_photo_url,
        'content', new_discussion.content,
        'parent_id', new_discussion.parent_id,
        'created_at', new_discussion.created_at,
        'updated_at', new_discussion.updated_at
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update a discussion comment
CREATE OR REPLACE FUNCTION update_discussion(
    p_discussion_id TEXT,
    p_content TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE discussions 
    SET content = p_content, updated_at = NOW()
    WHERE id = p_discussion_id AND author_id = auth.uid()::text;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to delete a discussion comment
CREATE OR REPLACE FUNCTION delete_discussion(
    p_discussion_id TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
    DELETE FROM discussions 
    WHERE id = p_discussion_id AND author_id = auth.uid()::text;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
