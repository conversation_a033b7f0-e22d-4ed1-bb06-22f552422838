<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IsotopeAI Data Export - Legacy System</title>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #666;
            font-size: 16px;
        }
        
        .auth-section {
            margin-bottom: 30px;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 15px;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .export-section {
            display: none;
        }
        
        .export-section.active {
            display: block;
        }
        
        .collection-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .collection-info h3 {
            color: #333;
            margin-bottom: 5px;
        }
        
        .collection-info p {
            color: #666;
            font-size: 14px;
        }
        
        .export-btn {
            background: #28a745;
            padding: 8px 16px;
            font-size: 14px;
            min-width: 100px;
        }
        
        .export-btn:hover {
            background: #218838;
        }
        
        .progress {
            margin-top: 10px;
            height: 4px;
            background: #e9ecef;
            border-radius: 2px;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 100%;
            background: #28a745;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .user-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .export-all-btn {
            background: #dc3545;
            margin-top: 20px;
        }
        
        .export-all-btn:hover {
            background: #c82333;
        }
        
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .instructions h4 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .instructions ol {
            color: #856404;
            margin-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 5px;
        }

        .admin-badge {
            background: #dc3545;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 10px;
        }

        .admin-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }

        .admin-section h4 {
            color: #856404;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">IsotopeAI</div>
            <div class="subtitle">Legacy Data Export System</div>
        </div>
        
        <div class="auth-section" id="authSection">
            <div class="instructions">
                <h4>Migration Instructions</h4>
                <ol>
                    <li>Sign in with your IsotopeAI account</li>
                    <li>PS: If you logged in with email, go to the main website's and link your account with a Google account</li>
                    <li>Export your data collections as CSV files</li>
                    <li>Go to the main IsotopeAI site and import your data</li>
                    <li>Verify your data has been migrated correctly</li>
                </ol>
            </div>
            <button class="btn" id="signInBtn">Sign in with Google</button>
            <div id="authStatus" class="status" style="display: none;"></div>
        </div>
        
        <div class="export-section" id="exportSection">
            <div class="user-info" id="userInfo"></div>
            
            <div class="collection-item">
                <div class="collection-info">
                    <h3>AI Chats</h3>
                    <p>Your AI conversation history and shared chats</p>
                    <div id="aiChatsProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="aiChatsStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('aiChats')">Export</button>
            </div>
            
            <div class="collection-item">
                <div class="collection-info">
                    <h3>Groups & Messages</h3>
                    <p>Your group chats and messages</p>
                    <div id="groupsProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="groupsStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('groups')">Export</button>
            </div>
            
            <div class="collection-item">
                <div class="collection-info">
                    <h3>Todos & Tasks</h3>
                    <p>Your todo lists and task management data</p>
                    <div id="todosProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="todosStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('todos')">Export</button>
            </div>
            
            <div class="collection-item">
                <div class="collection-info">
                    <h3>Subjects</h3>
                    <p>Your custom subjects and study categories</p>
                    <div id="subjectsProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="subjectsStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('subjects')">Export</button>
            </div>
            
            <div class="collection-item">
                <div class="collection-info">
                    <h3>Exams</h3>
                    <p>Your exam countdown and scheduling data</p>
                    <div id="examsProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="examsStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('exams')">Export</button>
            </div>
            
            <div class="collection-item">
                <div class="collection-info">
                    <h3>User Profile & Analytics</h3>
                    <p>Your profile, study sessions, and mock test data</p>
                    <div id="userDataProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="userDataStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('userData')">Export</button>
            </div>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>Study Sessions (Table Format)</h3>
                    <p>Study sessions as separate table records for Supabase</p>
                    <div id="studySessionsTableProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="studySessionsTableStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('studySessionsTable')">Export</button>
            </div>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>Mock Tests (Table Format)</h3>
                    <p>Mock tests as separate table records for Supabase</p>
                    <div id="mockTestsTableProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="mockTestsTableStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('mockTestsTable')">Export</button>
            </div>

            <button class="btn export-all-btn" onclick="exportAllData()">Export All Data</button>
            
            <div class="status info" style="margin-top: 20px;">
                <strong>Note:</strong> After exporting your data, please visit the main IsotopeAI website to import your data into the new system.
            </div>
        </div>
    </div>
    
    <script src="export.js"></script>
</body>
</html>
