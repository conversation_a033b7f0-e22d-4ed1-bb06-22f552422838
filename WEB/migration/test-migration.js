#!/usr/bin/env node

/**
 * Test script to verify the migration system is working correctly
 * Run this script to test various components of the migration
 */

import fs from 'fs';
import path from 'path';

console.log('🧪 IsotopeAI Migration System Test\n');

// Test 1: Check if all required files exist
console.log('📁 Testing file structure...');
const requiredFiles = [
    'migration/supabase-schema.sql',
    'migration/legacy-export/index.html',
    'migration/legacy-export/export.js',
    'migration/MIGRATION_GUIDE.md',
    'migration/IMPLEMENTATION_PLAN.md',
    'src/pages/Migration.tsx',
    'src/utils/supabaseClient.ts'
];

let allFilesExist = true;
requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} - MISSING`);
        allFilesExist = false;
    }
});

if (allFilesExist) {
    console.log('✅ All required files exist\n');
} else {
    console.log('❌ Some files are missing\n');
}

// Test 2: Check Supabase schema
console.log('🗄️  Testing Supabase schema...');
const schemaContent = fs.readFileSync('migration/supabase-schema.sql', 'utf8');
const requiredTables = [
    'questions',
    'answers', 
    'comments',
    'messages',
    'study_sessions',
    'mock_tests',
    'chat_comments'
];

let allTablesInSchema = true;
requiredTables.forEach(table => {
    if (schemaContent.includes(`CREATE TABLE IF NOT EXISTS ${table}`)) {
        console.log(`✅ Table: ${table}`);
    } else {
        console.log(`❌ Table: ${table} - MISSING FROM SCHEMA`);
        allTablesInSchema = false;
    }
});

if (allTablesInSchema) {
    console.log('✅ All required tables in schema\n');
} else {
    console.log('❌ Some tables missing from schema\n');
}

// Test 3: Check React component
console.log('⚛️  Testing React Migration component...');
const migrationComponent = fs.readFileSync('src/pages/Migration.tsx', 'utf8');
const requiredImports = [
    'useAuth',
    'supabase',
    'useState',
    'useRef'
];

let allImportsPresent = true;
requiredImports.forEach(importItem => {
    if (migrationComponent.includes(importItem)) {
        console.log(`✅ Import: ${importItem}`);
    } else {
        console.log(`❌ Import: ${importItem} - MISSING`);
        allImportsPresent = false;
    }
});

if (allImportsPresent) {
    console.log('✅ All required imports present\n');
} else {
    console.log('❌ Some imports missing\n');
}

// Test 4: Check export system
console.log('📤 Testing export system...');
const exportScript = fs.readFileSync('migration/legacy-export/export.js', 'utf8');
const exportFunctions = [
    'exportAIChats',
    'exportGroups',
    'exportTodos',
    'exportSubjects',
    'exportExams',
    'exportUserData'
];

let allExportFunctionsPresent = true;
exportFunctions.forEach(func => {
    if (exportScript.includes(`async function ${func}`)) {
        console.log(`✅ Function: ${func}`);
    } else {
        console.log(`❌ Function: ${func} - MISSING`);
        allExportFunctionsPresent = false;
    }
});

if (allExportFunctionsPresent) {
    console.log('✅ All export functions present\n');
} else {
    console.log('❌ Some export functions missing\n');
}

// Test 5: Check Supabase client utilities
console.log('🔌 Testing Supabase client utilities...');
const supabaseClient = fs.readFileSync('src/utils/supabaseClient.ts', 'utf8');
const clientFunctions = [
    'getAIChatsFromSupabase',
    'saveAIChatToSupabase',
    'getUserGroupsFromSupabase',
    'saveGroupToSupabase',
    'getUserTodosFromSupabase',
    'saveTodoToSupabase'
];

let allClientFunctionsPresent = true;
clientFunctions.forEach(func => {
    if (supabaseClient.includes(`export const ${func}`)) {
        console.log(`✅ Function: ${func}`);
    } else {
        console.log(`❌ Function: ${func} - MISSING`);
        allClientFunctionsPresent = false;
    }
});

if (allClientFunctionsPresent) {
    console.log('✅ All client functions present\n');
} else {
    console.log('❌ Some client functions missing\n');
}

// Test 6: Check if Migration route is added to App.tsx
console.log('🛣️  Testing route configuration...');
try {
    const appContent = fs.readFileSync('src/App.tsx', 'utf8');
    if (appContent.includes('const Migration = lazy') && appContent.includes('/migration')) {
        console.log('✅ Migration route configured in App.tsx');
    } else {
        console.log('❌ Migration route not properly configured in App.tsx');
    }
} catch (error) {
    console.log('❌ Could not read App.tsx');
}

// Summary
console.log('\n📊 Test Summary:');
console.log('================');

const testResults = [
    { name: 'File Structure', passed: allFilesExist },
    { name: 'Supabase Schema', passed: allTablesInSchema },
    { name: 'React Component', passed: allImportsPresent },
    { name: 'Export System', passed: allExportFunctionsPresent },
    { name: 'Supabase Client', passed: allClientFunctionsPresent }
];

const passedTests = testResults.filter(test => test.passed).length;
const totalTests = testResults.length;

testResults.forEach(test => {
    console.log(`${test.passed ? '✅' : '❌'} ${test.name}`);
});

console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);

if (passedTests === totalTests) {
    console.log('\n🎉 All tests passed! Migration system is ready.');
    console.log('\n📋 Next steps:');
    console.log('1. Update Firebase config in migration/legacy-export/export.js');
    console.log('2. Deploy the export system to legacy.isotopeai.in');
    console.log('3. Test the migration flow with sample data');
    console.log('4. Begin code migration phase');
} else {
    console.log('\n⚠️  Some tests failed. Please fix the issues before proceeding.');
}

console.log('\n📚 Documentation:');
console.log('- Migration Guide: migration/MIGRATION_GUIDE.md');
console.log('- Implementation Plan: migration/IMPLEMENTATION_PLAN.md');
console.log('- Schema: migration/supabase-schema.sql');
