# Sitemap redirects
[[redirects]]
  from = "/sitemap.xml"
  to = "/.netlify/functions/sitemap"
  status = 200
  force = true
  content_type = "application/xml"

[[redirects]]
  from = "/sitemap-static.xml"
  to = "/.netlify/functions/generate-sitemap-static"
  status = 200
  force = true
  content_type = "application/xml"

[[redirects]]
  from = "/sitemap-shared-:page.xml"
  to = "/.netlify/functions/generate-sitemap?page=:page"
  status = 200
  force = true
  content_type = "application/xml"

# New redirect for shared chats sitemap
[[redirects]]
  from = "/sitemap-shared.xml"
  to = "/.netlify/functions/sitemap"
  status = 200
  force = true
  content_type = "application/xml"

# Shared chat page redirects - route all URLs to the static HTML files
[[redirects]]
  from = "/shared/*"
  to = "/shared/:splat.html"
  status = 200
  force = true

# API redirect for related chats
[[redirects]]
  from = "/api/related-chats"
  to = "/.netlify/functions/related-chats"
  status = 200
  force = true

[[redirects]]
  from = "/api/cloudinary/delete/*"
  to = "/.netlify/functions/delete-cloudinary-image"
  status = 200
  force = true

# Handle SPA routing - must be the last redirect
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Sitemap headers
[[headers]]
  for = "/sitemap*.xml"
  [headers.values]
    Content-Type = "application/xml; charset=UTF-8"
    Access-Control-Allow-Origin = "*"
    X-Robots-Tag = "all"
    Cache-Control = "public, max-age=3600"
    Pragma = "no-cache"
    X-Content-Type-Options = "nosniff"

# Shared chat page headers for SEO optimization
[[headers]]
  for = "/shared/*"
  [headers.values]
    X-Robots-Tag = "all"
    Cache-Control = "public, max-age=86400"
    X-Content-Type-Options = "nosniff"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self' https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: blob: https:; font-src 'self' data: https:; connect-src 'self' https:; frame-src 'self' https:; object-src 'none'"

[build.environment]
  # Existing variables if any...

  # Note: These are build-time variables. For runtime, set them in the Netlify dashboard.
  CLOUDINARY_CLOUD_NAME = "your_cloud_name"

# Scheduled function to update sitemaps daily
[functions.scheduled-sitemap-update]
  schedule = "@daily"
  # This will run the function once a day at midnight UTC

# Add a build plugin to generate shared chat pages
[build]
  command = "npm run generate-qa-pages && npm run build"
  publish = "dist"