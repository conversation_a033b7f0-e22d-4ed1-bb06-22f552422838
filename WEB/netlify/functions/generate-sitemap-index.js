const { initializeApp } = require('firebase/app');
const { getFirestore, collection, query, where, getDocs, orderBy, limit, startAfter } = require('firebase/firestore');

// Initialize Firebase with environment variables
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID
};

let firebaseApp;
let db;

// Count total number of public chats to determine how many sitemaps we need
async function countPublicChats() {
  try {
    // Initialize Firebase if not already initialized
    if (!firebaseApp) {
      firebaseApp = initializeApp(firebaseConfig);
      db = getFirestore(firebaseApp);
    }

    const chatsRef = collection(db, 'aiChats');
    const q = query(
      chatsRef,
      where('isPublic', '==', true)
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.size;
  } catch (error) {
    console.error("Error counting public chats:", error);
    return 0;
  }
}

// Generate the sitemap index XML
function generateSitemapIndex(baseUrl, sitemapCount) {
  const today = new Date().toISOString().split('T')[0];

  let sitemapIndex = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <!-- Main sitemap for static pages -->
  <sitemap>
    <loc>${baseUrl}/sitemap-static.xml</loc>
    <lastmod>${today}</lastmod>
  </sitemap>
  
  <!-- Shared chat pages sitemap -->
  <sitemap>
    <loc>${baseUrl}/sitemap-shared.xml</loc>
    <lastmod>${today}</lastmod>
  </sitemap>`;

  // Add sitemap entries for shared pages
  for (let i = 1; i <= sitemapCount; i++) {
    sitemapIndex += `
  <sitemap>
    <loc>${baseUrl}/sitemap-shared-${i}.xml</loc>
    <lastmod>${today}</lastmod>
  </sitemap>`;
  }

  sitemapIndex += `
</sitemapindex>`;

  return sitemapIndex;
}

// Netlify serverless function handler
exports.handler = async function(event, context) {
  try {
    // Get base URL from request or environment
    const baseUrl = process.env.URL || 'https://isotopeai.in';

    // Count total public chats
    const totalChats = await countPublicChats();

    // Calculate how many sitemaps we need (1000 URLs per sitemap is a good practice)
    const URLS_PER_SITEMAP = 1000;
    const sitemapCount = Math.ceil(totalChats / URLS_PER_SITEMAP) || 1;

    // Generate the sitemap index XML
    const sitemapIndex = generateSitemapIndex(baseUrl, sitemapCount);

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/xml; charset=UTF-8',
        'X-Robots-Tag': 'all',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        'X-Content-Type-Options': 'nosniff'
      },
      body: sitemapIndex
    };
  } catch (error) {
    console.error("Error generating sitemap index:", error);
    return {
      statusCode: 500,
      body: "Error generating sitemap index: " + error.message
    };
  }
};
