const { initializeApp } = require('firebase/app');
const { getFirestore, collection, query, where, getDocs, orderBy, limit, startAfter } = require('firebase/firestore');

// Initialize Firebase with environment variables
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID
};

let firebaseApp;
let db;

// Generate the XML sitemap with just the chat URLs (no static pages)
function generateSitemap(baseUrl, chatEntries) {
  const today = new Date().toISOString().split('T')[0];

  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

  // Add shared chat pages
  chatEntries.forEach(chat => {
    const lastMod = chat.updatedAt ? new Date(chat.updatedAt).toISOString().split('T')[0] : today;
    sitemap += `
  <url>
    <loc>${baseUrl}/shared/${chat.id}</loc>
    <lastmod>${lastMod}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>`;
  });

  sitemap += `
</urlset>`;

  return sitemap;
}

// Netlify serverless function handler
exports.handler = async function(event, context) {
  // Initialize Firebase if not already initialized
  if (!firebaseApp) {
    try {
      firebaseApp = initializeApp(firebaseConfig);
      db = getFirestore(firebaseApp);
    } catch (error) {
      console.error("Firebase initialization error:", error);
      return {
        statusCode: 500,
        body: "Error initializing Firebase"
      };
    }
  }

  try {
    // Get base URL from request
    const baseUrl = process.env.URL || 'https://isotopeai.in';

    // Get page number from query parameters
    const pageNumber = event.queryStringParameters?.page || 1;
    const URLS_PER_SITEMAP = 1000; // Maximum URLs per sitemap file

    // Query for public chats with pagination
    const chatsRef = collection(db, 'aiChats');
    let q;

    // Create the query based on page number
    q = query(
      chatsRef,
      orderBy('updatedAt', 'desc'),
      limit(URLS_PER_SITEMAP)
    );

    // If we're requesting a page beyond the first, we need to use startAfter
    if (pageNumber > 1) {
      // First, get the last document from the previous page
      const previousPageQuery = query(
        chatsRef,
        orderBy('updatedAt', 'desc'),
        limit((pageNumber - 1) * URLS_PER_SITEMAP)
      );

      const previousPageSnapshot = await getDocs(previousPageQuery);
      const lastVisibleDoc = previousPageSnapshot.docs[previousPageSnapshot.docs.length - 1];

      // Now create a new query starting after the last document from previous page
      if (lastVisibleDoc) {
        q = query(
          chatsRef,
          orderBy('updatedAt', 'desc'),
          startAfter(lastVisibleDoc),
          limit(URLS_PER_SITEMAP)
        );
      } else {
        // If we don't have enough documents to reach this page, return empty sitemap
        const emptySitemap = generateSitemap(baseUrl, []);
        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/xml; charset=UTF-8',
            'X-Robots-Tag': 'all',
            'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
            'X-Content-Type-Options': 'nosniff'
          },
          body: emptySitemap
        };
      }
    }

    const querySnapshot = await getDocs(q);
    const chatEntries = querySnapshot.docs.map(doc => ({
      id: doc.id,
      updatedAt: doc.data().updatedAt,
    }));

    // Generate the sitemap XML
    const sitemap = generateSitemap(baseUrl, chatEntries);

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/xml; charset=UTF-8',
        'X-Robots-Tag': 'all',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        'X-Content-Type-Options': 'nosniff'
      },
      body: sitemap
    };
  } catch (error) {
    console.error("Error generating sitemap:", error);
    return {
      statusCode: 500,
      body: "Error generating sitemap: " + error.message
    };
  }
};