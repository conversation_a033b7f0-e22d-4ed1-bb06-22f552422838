const { initializeApp } = require('firebase/app');
const { getFirestore, collection, query, where, getDocs, orderBy, limit } = require('firebase/firestore');

// Initialize Firebase with environment variables
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID
};

// Firebase initialization
let firebaseApp;
let db;

// Helper function to extract keywords from text
function extractKeywords(text) {
  if (!text) return [];
  
  // Remove special characters and convert to lowercase
  const cleanText = text.toLowerCase().replace(/[^\w\s]/g, '');
  
  // Split into words
  const words = cleanText.split(/\s+/);
  
  // Filter out common stop words
  const stopWords = new Set([
    'a', 'about', 'above', 'after', 'again', 'against', 'all', 'am', 'an', 'and', 'any', 'are', 'as', 'at',
    'be', 'because', 'been', 'before', 'being', 'below', 'between', 'both', 'but', 'by',
    'can', 'did', 'do', 'does', 'doing', 'down', 'during',
    'each', 'few', 'for', 'from', 'further',
    'had', 'has', 'have', 'having', 'he', 'her', 'here', 'hers', 'herself', 'him', 'himself', 'his', 'how',
    'i', 'if', 'in', 'into', 'is', 'it', 'its', 'itself',
    'just',
    'me', 'more', 'most', 'my', 'myself',
    'no', 'nor', 'not', 'now',
    'of', 'off', 'on', 'once', 'only', 'or', 'other', 'our', 'ours', 'ourselves', 'out', 'over', 'own',
    'same', 'she', 'should', 'so', 'some', 'such',
    'than', 'that', 'the', 'their', 'theirs', 'them', 'themselves', 'then', 'there', 'these', 'they',
    'this', 'those', 'through', 'to', 'too',
    'under', 'until', 'up',
    'very',
    'was', 'we', 'were', 'what', 'when', 'where', 'which', 'while', 'who', 'whom', 'why', 'will', 'with',
    'would',
    'you', 'your', 'yours', 'yourself', 'yourselves'
  ]);
  
  const keywords = words.filter(word => 
    word.length > 2 && !stopWords.has(word)
  );
  
  // Return unique keywords
  return [...new Set(keywords)];
}

// Find related questions based on a given slug
async function findRelatedQuestions(slug, maxCount = 5) {
  if (!firebaseApp) {
    firebaseApp = initializeApp(firebaseConfig);
    db = getFirestore(firebaseApp);
  }
  
  try {
    // First, get the current Q&A by slug
    const qnaRef = collection(db, 'public_qna');
    const qnaQuery = query(qnaRef, where('slug', '==', slug), where('status', '==', 'approved'));
    const qnaSnapshot = await getDocs(qnaQuery);
    
    if (qnaSnapshot.empty) {
      return [];
    }
    
    const currentQnA = qnaSnapshot.docs[0].data();
    const currentId = qnaSnapshot.docs[0].id;
    const currentKeywords = [
      ...extractKeywords(currentQnA.questionText),
      ...extractKeywords(currentQnA.answerText)
    ];
    
    if (currentKeywords.length === 0) {
      // If no keywords found, just return recent Q&As
      const recentQuery = query(
        qnaRef,
        where('status', '==', 'approved'),
        where('id', '!=', currentId),
        orderBy('createdAt', 'desc'),
        limit(maxCount)
      );
      
      const recentSnapshot = await getDocs(recentQuery);
      return recentSnapshot.docs.map(doc => ({
        id: doc.id,
        slug: doc.data().slug,
        questionText: doc.data().questionText,
      }));
    }
    
    // Get all approved Q&As
    const allQnAQuery = query(qnaRef, where('status', '==', 'approved'));
    const allQnASnapshot = await getDocs(allQnAQuery);
    
    // Calculate relevance scores
    const scoredQuestions = allQnASnapshot.docs
      .filter(doc => doc.id !== currentId) // Exclude current Q&A
      .map(doc => {
        const data = doc.data();
        const questionKeywords = extractKeywords(data.questionText);
        const answerKeywords = extractKeywords(data.answerText);
        const allKeywords = [...questionKeywords, ...answerKeywords];
        
        // Calculate matches with current keywords
        let matchCount = 0;
        
        for (const keyword of currentKeywords) {
          if (allKeywords.includes(keyword)) {
            matchCount++;
          }
        }
        
        // Calculate relevance score (percentage of matching keywords)
        const score = matchCount / Math.max(1, currentKeywords.length);
        
        return {
          id: doc.id,
          slug: data.slug,
          questionText: data.questionText,
          score: score
        };
      })
      .filter(item => item.score > 0) // Only keep items with some relevance
      .sort((a, b) => b.score - a.score) // Sort by relevance, highest first
      .slice(0, maxCount);
    
    // If we don't have enough relevant questions, supplement with recent ones
    if (scoredQuestions.length < maxCount) {
      const recentQuery = query(
        qnaRef,
        where('status', '==', 'approved'),
        where('id', '!=', currentId),
        orderBy('createdAt', 'desc'),
        limit(maxCount)
      );
      
      const recentSnapshot = await getDocs(recentQuery);
      const recentQuestions = recentSnapshot.docs
        .map(doc => ({
          id: doc.id,
          slug: doc.data().slug,
          questionText: doc.data().questionText,
          score: 0
        }))
        .filter(item => !scoredQuestions.some(q => q.id === item.id));
      
      // Combine the lists, keeping the max count
      const combined = [...scoredQuestions, ...recentQuestions].slice(0, maxCount);
      return combined;
    }
    
    return scoredQuestions;
  } catch (error) {
    console.error('Error finding related questions:', error);
    return [];
  }
}

// Netlify function handler
exports.handler = async function(event, context) {
  // Extract slug from query parameters
  const slug = event.queryStringParameters?.slug;
  
  if (!slug) {
    return {
      statusCode: 400,
      body: JSON.stringify({ error: 'Missing required parameter: slug' })
    };
  }
  
  try {
    const relatedQuestions = await findRelatedQuestions(slug);
    
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=3600' // Cache for 1 hour
      },
      body: JSON.stringify({
        questions: relatedQuestions
      })
    };
  } catch (error) {
    console.error('Error handling related questions request:', error);
    
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
}; 