const { initializeApp } = require('firebase/app');
const { getFirestore, collection, query, where, getDocs, orderBy, limit } = require('firebase/firestore');
// Use dynamic import for node-fetch (ES Module)
let fetch;

// Initialize fetch using dynamic import
async function initFetch() {
  if (!fetch) {
    const nodeFetch = await import('node-fetch');
    fetch = nodeFetch.default;
  }
  return fetch;
}

// Initialize Firebase with environment variables
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID
};

let firebaseApp;
let db;

// Ping search engines with the sitemap URL
async function pingSearchEngines(sitemapUrl) {
  try {
    // Make sure fetch is initialized
    const fetchFn = await initFetch();

    // List of search engine ping endpoints
    const pingUrls = [
      `https://www.google.com/ping?sitemap=${encodeURIComponent(sitemapUrl)}`,
      `https://www.bing.com/ping?sitemap=${encodeURIComponent(sitemapUrl)}`
    ];

    // Send the ping requests
    const pingPromises = pingUrls.map(url =>
      fetchFn(url, { method: 'GET' })
        .then(response => console.log(`Pinged search engine: ${url}, status: ${response.status}`))
        .catch(err => console.error(`Failed to ping search engine: ${url}`, err))
    );

    // Wait for all pings to complete
    await Promise.all(pingPromises);

    console.log('Successfully pinged search engines with sitemap URL:', sitemapUrl);
    return true;
  } catch (error) {
    console.error('Error pinging search engines:', error);
    return false;
  }
}

// Count total number of public chats
async function countPublicChats() {
  try {
    // Initialize Firebase if not already initialized
    if (!firebaseApp) {
      firebaseApp = initializeApp(firebaseConfig);
      db = getFirestore(firebaseApp);
    }

    const chatsRef = collection(db, 'aiChats');
    const q = query(
      chatsRef,
      where('isPublic', '==', true)
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.size;
  } catch (error) {
    console.error("Error counting public chats:", error);
    return 0;
  }
}

// Trigger regeneration of all sitemaps
async function regenerateAllSitemaps(baseUrl) {
  try {
    // Make sure fetch is initialized
    const fetchFn = await initFetch();

    // First, regenerate the static sitemap
    await fetchFn(`${baseUrl}/.netlify/functions/generate-sitemap-static`);
    console.log('Static sitemap regenerated');
    
    // Regenerate the Q&A pages and sitemap
    await fetchFn(`${baseUrl}/.netlify/functions/generate-qa-pages`);
    console.log('Q&A pages and sitemap regenerated');

    // Count total chats to determine how many sitemap files we need
    const totalChats = await countPublicChats();
    const URLS_PER_SITEMAP = 1000;
    const sitemapCount = Math.ceil(totalChats / URLS_PER_SITEMAP) || 1;

    // Regenerate each shared sitemap
    for (let i = 1; i <= sitemapCount; i++) {
      await fetchFn(`${baseUrl}/.netlify/functions/generate-sitemap?page=${i}`);
      console.log(`Shared sitemap ${i} regenerated`);
    }

    // Finally, regenerate the sitemap index
    await fetchFn(`${baseUrl}/.netlify/functions/generate-sitemap-index`);
    console.log('Sitemap index regenerated');

    // Ping search engines with the sitemap index URL
    await pingSearchEngines(`${baseUrl}/sitemap.xml`);

    return true;
  } catch (error) {
    console.error('Error regenerating sitemaps:', error);
    return false;
  }
}

// Netlify scheduled function handler
exports.handler = async function(event, context) {
  try {
    // Get base URL from environment
    const baseUrl = process.env.URL || 'https://isotopeai.in';

    // Regenerate all sitemaps
    const success = await regenerateAllSitemaps(baseUrl);

    if (success) {
      return {
        statusCode: 200,
        body: JSON.stringify({ message: 'Sitemaps successfully regenerated and search engines pinged' })
      };
    } else {
      return {
        statusCode: 500,
        body: JSON.stringify({ message: 'Error regenerating sitemaps' })
      };
    }
  } catch (error) {
    console.error("Error in scheduled sitemap update:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({ message: `Error in scheduled sitemap update: ${error.message}` })
    };
  }
};
