const { initializeApp } = require('firebase/app');
const { getFirestore, collection, query, getDocs, orderBy, limit } = require('firebase/firestore');
const { SitemapStream, streamToPromise } = require('sitemap');
const { Readable } = require('stream');

// Initialize Firebase with environment variables
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID
};

// Firebase initialization
let firebaseApp;
let db;

// Function to fetch all shared chat IDs and slugs
async function getAllSharedChats(maxEntries = 1000) {
  if (!firebaseApp) {
    firebaseApp = initializeApp(firebaseConfig);
    db = getFirestore(firebaseApp);
  }
  
  try {
    const chatsRef = collection(db, 'aiChats');
    // Query all public chats, most recently updated first
    const q = query(
      chatsRef,
      orderBy('updatedAt', 'desc'),
      limit(maxEntries)
    );
    
    const querySnapshot = await getDocs(q);
    console.log(`Found ${querySnapshot.docs.length} shared chats`);
    
    // Get ID and slug for each chat
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        slug: data.slug || doc.id,
        lastmod: data.updatedAt ? new Date(
          typeof data.updatedAt.toDate === 'function' ? 
            data.updatedAt.toDate() : 
            typeof data.updatedAt === 'object' && data.updatedAt.seconds ? 
              new Date(data.updatedAt.seconds * 1000) :
              data.updatedAt
        ).toISOString() : new Date().toISOString()
      };
    });
  } catch (error) {
    console.error('Error getting shared chats for sitemap:', error);
    return [];
  }
}

// Netlify function handler
exports.handler = async function(event, context) {
  try {
    // Get all shared chats
    const sharedChats = await getAllSharedChats();
    
    // Define the base URL for the site
    const hostname = process.env.URL || 'https://isotopeai.com';
    
    // Create an array of URL entries for the sitemap
    const links = [
      { url: '/', changefreq: 'daily', priority: 1.0 },
      { url: '/ai', changefreq: 'daily', priority: 0.9 },
      { url: '/ai-landing', changefreq: 'monthly', priority: 0.8 },
      { url: '/shared', changefreq: 'daily', priority: 0.8 },
      // Add shared chat pages
      ...sharedChats.map(chat => ({
        url: `/shared/${chat.slug || chat.id}`,
        lastmod: chat.lastmod,
        changefreq: 'monthly',
        priority: 0.6
      }))
    ];
    
    // Create the sitemap
    const stream = new SitemapStream({ hostname });
    const sitemap = await streamToPromise(Readable.from(links).pipe(stream));
    
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600' // Cache for 1 hour
      },
      body: sitemap.toString()
    };
  } catch (error) {
    console.error('Error generating sitemap:', error);
    
    return {
      statusCode: 500,
      body: 'Internal server error generating sitemap'
    };
  }
}; 