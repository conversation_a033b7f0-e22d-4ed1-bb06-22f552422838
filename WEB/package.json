{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:dev": "vite build --mode development", "build:functions": "cd netlify/functions && npm install", "postbuild": "npm run build:functions", "lint": "eslint .", "preview": "vite preview", "generate-qa-pages": "node scripts/generate-qa-pages.js", "ping-sitemap": "curl -X GET https://www.google.com/ping?sitemap=https://isotopeai.com/sitemap.xml", "dev:token-server": "cd server && npm start", "dev:with-token": "concurrently \"npm run dev\" \"npm run dev:token-server\""}, "dependencies": {"@babel/helpers": "~7.26.10", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@getbrevo/brevo": "^2.2.0", "@google/generative-ai": "^0.21.0", "@grpc/grpc-js": "~1.8.22", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.9.0", "@mui/icons-material": "^6.4.6", "@mui/material": "^6.4.6", "@mui/x-charts": "^7.27.1", "@nuxt/image": "^1.10.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@react-spring/web": "^9.7.5", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.47.10", "@tanstack/react-query": "^5.56.2", "@types/crypto-js": "^4.2.2", "@types/html2canvas": "^0.5.35", "@types/jsonwebtoken": "^9.0.9", "@types/react-helmet": "^6.1.11", "@types/react-scroll": "^1.8.10", "@types/uuid": "^10.0.0", "accurate-interval": "^1.0.9", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "cloudinary": "^2.6.0", "cloudinary-react": "^1.8.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "esbuild": "^0.25.3", "fabric": "^5.3.0", "framer-motion": "^11.15.0", "html2canvas": "^1.4.1", "input-otp": "^1.2.4", "jsonwebtoken": "^9.0.2", "katex": "^0.16.22", "lodash": "^4.17.21", "lucide-react": "^0.462.0", "nanoid": "~3.3.8", "next-themes": "^0.3.0", "protobufjs": "~7.2.5", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-color": "^2.19.3", "react-colorful": "^5.6.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.0", "react-image-crop": "^11.0.5", "react-markdown": "^9.1.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "react-scroll": "^1.9.2", "recharts": "^2.12.7", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "shadcn": "^2.1.8", "sonner": "^1.5.0", "stream-chat": "^8.60.0", "stream-chat-css": "^1.0.23", "stream-chat-react": "^12.14.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "tough-cookie": "~4.1.3", "uuid": "^11.1.0", "vaul": "^0.9.3", "worker-timers": "^8.0.19", "zod": "^3.23.8", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.8.3", "typescript-eslint": "^8.0.1", "vite": "~5.4.12", "vite-plugin-pwa": "^0.21.1"}, "description": "**IsotopeAI** is an AI-powered platform designed to solve your doubts in **Physics**, **Chemistry**, and **Mathematics**. Whether you're a high school student, a competitive exam aspirant, or just a curious learner, IsotopeAI is your go-to solution for clarifying concepts, solving problems, and learning efficiently.", "main": "eslint.config.js", "engines": {"node": ">=20.0.0"}, "keywords": [], "author": "", "license": "ISC"}