// Simple Timer Worker
// This worker's only job is to send a 'TICK' message periodically
// when requested by the main thread. It does not track time itself.

let intervalId = null;
const intervalDuration = 500; // Send a tick every 500ms

self.onmessage = function(e) {
  const { type } = e.data;

  switch (type) {
    case 'START':
      if (intervalId === null) {
        // console.log('Worker: Starting ticks');
        intervalId = setInterval(() => {
          self.postMessage({ type: 'TICK' });
        }, intervalDuration);
      }
      break;

    case 'STOP':
      if (intervalId !== null) {
        // console.log('Worker: Stopping ticks');
        clearInterval(intervalId);
        intervalId = null;
      }
      break;

    default:
      console.warn('Worker: Received unknown message type:', type);
  }
};

// Ensure interval is cleared if the worker is terminated unexpectedly
self.onclose = function() {
  if (intervalId !== null) {
    clearInterval(intervalId);
  }
};
