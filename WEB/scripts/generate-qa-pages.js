#!/usr/bin/env node
// <PERSON>rip<PERSON> to generate static shared chat pages during build
// This is called from package.json "prebuild" script

// Create a wrapper for the CommonJS module using native Node.js features
import { createRequire } from 'module';
const require = createRequire(import.meta.url);
const { generateSharedChatPages } = require('../netlify/functions/generate-qa-pages');

async function main() {
  console.log('=== SHARED CHAT STATIC PAGE GENERATOR ===');
  console.log('This script generates static HTML pages for all public shared chats.');
  console.log('These pages will be included in the build for SEO optimization.');
  console.log('');
  
  try {
    console.log('Starting shared chat page generation...');
    const result = await generateSharedChatPages();
    
    if (result.success) {
      console.log(`Success! Generated ${result.pagesGenerated} shared chat pages.`);
      console.log('Pages are located in public/shared/ directory.');
      console.log('Sitemap is located at public/sitemap-shared.xml');
    } else {
      console.error('Failed to generate shared chat pages:', result.error);
      process.exit(1);
    }
  } catch (error) {
    console.error('Unexpected error during shared chat page generation:', error);
    process.exit(1);
  }
}

main(); 