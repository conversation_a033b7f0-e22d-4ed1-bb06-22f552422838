// <PERSON><PERSON><PERSON> to manually trigger sitemap generation
// Run with: node --experimental-modules scripts/generate-sitemaps.js

// Use ES module import for node-fetch
import fetch from 'node-fetch';

async function generateSitemaps() {
  try {
    const baseUrl = process.env.URL || 'https://isotopeai.in';

    console.log('Starting sitemap generation process...');

    // Generate static sitemap
    console.log('Generating static sitemap...');
    await fetch(`${baseUrl}/.netlify/functions/generate-sitemap-static`);

    // Count total chats to determine how many sitemap files we need
    console.log('Generating shared pages sitemaps...');
    // We'll generate the first 5 sitemaps as an example
    // In production, this would be determined by the total number of chats
    for (let i = 1; i <= 5; i++) {
      console.log(`Generating shared sitemap ${i}...`);
      await fetch(`${baseUrl}/.netlify/functions/generate-sitemap?page=${i}`);
    }

    // Generate sitemap index
    console.log('Generating sitemap index...');
    await fetch(`${baseUrl}/.netlify/functions/generate-sitemap-index`);

    console.log('Sitemap generation complete!');
    console.log(`Main sitemap index available at: ${baseUrl}/sitemap.xml`);
    console.log(`Static sitemap available at: ${baseUrl}/sitemap-static.xml`);
    console.log(`Shared pages sitemaps available at: ${baseUrl}/sitemap-shared-1.xml, ${baseUrl}/sitemap-shared-2.xml, etc.`);
  } catch (error) {
    console.error('Error generating sitemaps:', error);
  }
}

generateSitemaps();
