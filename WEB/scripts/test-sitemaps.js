// Test script for sitemap generation functions
// Run with: node --experimental-modules scripts/test-sitemaps.js

// Use ES module import for node-fetch
import fetch from 'node-fetch';

// Configure the base URL for your local environment
const BASE_URL = 'http://localhost:8081';

async function testSitemaps() {
  try {
    console.log('=== SITEMAP TESTING SCRIPT ===');
    console.log(`Using base URL: ${BASE_URL}`);
    console.log('Make sure your Netlify Dev environment is running!');
    console.log('');

    // Test sitemap index
    console.log('1. Testing sitemap index...');
    try {
      const indexResponse = await fetch(`${BASE_URL}/.netlify/functions/generate-sitemap-index`);
      const indexStatus = indexResponse.status;
      const indexBody = await indexResponse.text();

      console.log(`   Status: ${indexStatus} ${indexResponse.statusText}`);
      if (indexStatus === 200) {
        console.log('   ✅ Sitemap index generated successfully');
        console.log(`   Size: ${indexBody.length} bytes`);
        console.log(`   First 300 characters: ${indexBody.substring(0, 300)}...`);
      } else {
        console.log('   ❌ Failed to generate sitemap index');
        console.log(`   Error: ${indexBody}`);
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }

    console.log('');

    // Test static sitemap
    console.log('2. Testing static sitemap...');
    try {
      const staticResponse = await fetch(`${BASE_URL}/.netlify/functions/generate-sitemap-static`);
      const staticStatus = staticResponse.status;
      const staticBody = await staticResponse.text();

      console.log(`   Status: ${staticStatus} ${staticResponse.statusText}`);
      if (staticStatus === 200) {
        console.log('   ✅ Static sitemap generated successfully');
        console.log(`   Size: ${staticBody.length} bytes`);
        console.log(`   First 300 characters: ${staticBody.substring(0, 300)}...`);
      } else {
        console.log('   ❌ Failed to generate static sitemap');
        console.log(`   Error: ${staticBody}`);
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }

    console.log('');

    // Test shared sitemap page 1
    console.log('3. Testing shared sitemap (page 1)...');
    try {
      const sharedResponse = await fetch(`${BASE_URL}/.netlify/functions/generate-sitemap?page=1`);
      const sharedStatus = sharedResponse.status;
      const sharedBody = await sharedResponse.text();

      console.log(`   Status: ${sharedStatus} ${sharedResponse.statusText}`);
      if (sharedStatus === 200) {
        console.log('   ✅ Shared sitemap generated successfully');
        console.log(`   Size: ${sharedBody.length} bytes`);
        console.log(`   First 300 characters: ${sharedBody.substring(0, 300)}...`);

        // Count URLs in the sitemap
        const urlCount = (sharedBody.match(/<url>/g) || []).length;
        console.log(`   URLs in sitemap: ${urlCount}`);
      } else {
        console.log('   ❌ Failed to generate shared sitemap');
        console.log(`   Error: ${sharedBody}`);
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }

    console.log('');
    console.log('=== TESTING COMPLETE ===');
    console.log('');
    console.log('Next steps:');
    console.log('1. Deploy your changes to Netlify');
    console.log('2. Test the production URLs:');
    console.log('   - https://isotopeai.in/sitemap.xml');
    console.log('   - https://isotopeai.in/sitemap-static.xml');
    console.log('   - https://isotopeai.in/sitemap-shared-1.xml');
    console.log('3. Submit the main sitemap URL to Google Search Console');

  } catch (error) {
    console.error('Error running tests:', error);
  }
}

// Run the tests
testSitemaps();
