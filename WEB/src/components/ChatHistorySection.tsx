import { <PERSON>, <PERSON>, Trash2, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Filter, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>nO<PERSON> } from "lucide-react";
import { But<PERSON> } from "./ui/button";
import { ScrollArea } from "./ui/scroll-area";
import { useToast } from "@/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "./ui/alert-dialog";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "./ui/collapsible";
import { ChatHistory } from "@/types/chat";
import { useState } from "react";
import { Input } from "./ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";

interface ChatHistorySectionProps {
  chatHistory: ChatHistory[];
  isHistoryOpen: boolean;
  setIsHistoryOpen: (isOpen: boolean) => void;
  loadChat: (chatId: string) => void;
  toggleStarChat: (chatId: string) => void;
  deleteChat: (chatId: string) => void;
  togglePinChat?: (chatId: string) => void;
}

export const ChatHistorySection = ({
  chatHistory,
  isHistoryOpen,
  setIsHistoryOpen,
  loadChat,
  toggleStarChat,
  deleteChat,
  togglePinChat,
}: ChatHistorySectionProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [filter, setFilter] = useState<"all" | "starred">("all");
  const [sortBy, setSortBy] = useState<"newest" | "oldest" | "name">("newest");
  const { toast } = useToast();

  const filterAndSortChats = (chats: ChatHistory[]) => {
    return chats.filter(chat => {
      const matchesSearch = chat.preview.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesFilter = filter === "all" || (filter === "starred" && chat.isStarred);
      return matchesSearch && matchesFilter;
    }).sort((a, b) => {
      if (sortBy === "newest") {
        return b.timestamp - a.timestamp;
      } else if (sortBy === "oldest") {
        return a.timestamp - b.timestamp;
      } else {
        // Sort by name (preview)
        return a.preview.localeCompare(b.preview);
      }
    });
  };
  
  const pinnedChats = filterAndSortChats(chatHistory.filter(chat => chat.isPinned));
  const regularChats = filterAndSortChats(chatHistory.filter(chat => !chat.isPinned));
  
  const handlePinChat = (chatId: string) => {
    if (togglePinChat) {
      togglePinChat(chatId);
    } else {
      toast({
        title: "Error",
        description: "Pin functionality not available.",
        variant: "destructive"
      });
    }
  };

  const renderChatItem = (chat: ChatHistory) => (
    <div
      key={chat.id}
      className="group p-4 rounded-lg bg-background hover:bg-accent/5 transition-all duration-200 border border-border/50 hover:border-border"
    >
      <div className="flex justify-between items-start gap-4">
        <div 
          className="flex-1 cursor-pointer space-y-1" 
          onClick={() => loadChat(chat.id)}
        >
          <p className="font-medium line-clamp-2 group-hover:text-primary transition-colors">
            {chat.preview}
          </p>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Clock className="h-3 w-3" />
            <span>{new Date(chat.timestamp).toLocaleDateString(undefined, {
              month: 'short',
              day: 'numeric',
              year: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}</span>
          </div>
        </div>
        <div className="flex gap-1">
          {togglePinChat && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handlePinChat(chat.id)}
              className={`${chat.isPinned ? "text-primary opacity-100" : "opacity-0"} group-hover:opacity-100 transition-opacity`}
            >
              {chat.isPinned ? <PinOff className="h-4 w-4" /> : <Pin className="h-4 w-4" />}
            </Button>
          )}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => toggleStarChat(chat.id)}
            className={`${chat.isStarred ? "text-yellow-500 opacity-100" : "opacity-0"} group-hover:opacity-100 transition-opacity`}
          >
            <Star className="h-4 w-4" />
          </Button>
          <div className="opacity-0 group-hover:opacity-100 transition-opacity">
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Trash2 className="h-4 w-4" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Chat History</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to delete this chat? This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction 
                    onClick={() => deleteChat(chat.id)}
                    className="bg-destructive hover:bg-destructive/90"
                  >
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="mt-8 mb-12">
      <Collapsible
        open={isHistoryOpen}
        onOpenChange={setIsHistoryOpen}
        className="w-full"
      >
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="bg-primary/10 p-2 rounded-lg">
              <History className="h-5 w-5 text-primary" />
            </div>
            <h2 className="text-2xl font-semibold">Previous Chats</h2>
          </div>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" size="sm">
              {isHistoryOpen ? "Hide" : "Show"}
            </Button>
          </CollapsibleTrigger>
        </div>
        <CollapsibleContent>
          <div className="space-y-4">
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Input
                  placeholder="Search chats..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
                <MessageSquare className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="icon">
                    <ArrowUpDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setSortBy("newest")}>
                    <Clock className="mr-2 h-4 w-4" />
                    Newest First
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setSortBy("oldest")}>
                    <Clock className="mr-2 h-4 w-4" />
                    Oldest First
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setSortBy("name")}>
                    <MessageSquare className="mr-2 h-4 w-4" />
                    By Name
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="icon">
                    <Filter className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setFilter("all")}>
                    All Chats
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setFilter("starred")}>
                    Starred Only
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            
            <ScrollArea className="h-[400px] rounded-xl border bg-card/50 backdrop-blur-sm p-4">
              {pinnedChats.length > 0 && (
                <div className="mb-6">
                  <div className="flex items-center gap-2 mb-3">
                    <Pin className="h-4 w-4 text-primary" />
                    <h3 className="text-sm font-medium text-primary">Pinned Chats</h3>
                  </div>
                  <div className="space-y-3">
                    {pinnedChats.map(renderChatItem)}
                  </div>
                </div>
              )}
              
              <div className={`${pinnedChats.length > 0 ? 'mt-6' : ''}`}>
                {pinnedChats.length > 0 && (
                  <div className="flex items-center gap-2 mb-3">
                    <MessageSquare className="h-4 w-4 text-muted-foreground" />
                    <h3 className="text-sm font-medium text-muted-foreground">All Chats</h3>
                  </div>
                )}
                <div className="space-y-3">
                  {regularChats.length === 0 && pinnedChats.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      No chats found
                    </div>
                  ) : regularChats.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      No unpinned chats found
                    </div>
                  ) : (
                    regularChats.map(renderChatItem)
                  )}
                </div>
              </div>
            </ScrollArea>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
};