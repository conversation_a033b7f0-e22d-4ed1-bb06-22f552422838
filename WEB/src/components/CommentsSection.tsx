import React, { useState } from 'react';
import { Comment } from '../types/chat';
import { CommentComponent } from './Comment';
import { Button } from './ui/button';
import { Textarea } from './ui/textarea';
import { Input } from './ui/input';

interface CommentsSectionProps {
  comments: Comment[];
  onAddComment: (content: string, author: string) => void;
  onAddReply: (parentId: string, content: string) => void;
}

export const DiscussionSection: React.FC<CommentsSectionProps> = ({
  comments,
  onAddComment,
  onAddReply,
}) => {
  const [newComment, setNewComment] = useState('');
  const [author, setAuthor] = useState('');
  const [isAddingComment, setIsAddingComment] = useState(false);
  const [expandedReplyIds, setExpandedReplyIds] = useState<Set<string>>(new Set());

  // Calculate visible comments based on index
  const getVisibleCommentCount = (index: number): boolean => {
    const MESSAGES_PER_SECTION = 5;
    return index < MESSAGES_PER_SECTION;
  };

  const handleExpandReply = (replyId: string) => {
    setExpandedReplyIds(prev => new Set([...prev, replyId]));
  };

  const handleCollapseReply = (replyId: string) => {
    setExpandedReplyIds(prev => {
      const newSet = new Set(prev);
      newSet.delete(replyId);
      return newSet;
    });
  };

  const handleSubmitComment = () => {
    if (newComment.trim() && author.trim()) {
      onAddComment(newComment, author);
      setNewComment('');
      setIsAddingComment(false);
    }
  };

  return (
    <div className="mt-8 bg-card rounded-2xl shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold">Discussion</h2>
        <Button
          variant="default"
          onClick={() => setIsAddingComment(!isAddingComment)}
          className="hover:opacity-90"
        >
          {isAddingComment ? 'Cancel' : '+ Add Discussion'}
        </Button>
      </div>

      {isAddingComment && (
        <div className="mb-6 space-y-4 bg-white/5 p-4 rounded-lg border border-border">
          <Input
            placeholder="Your name"
            value={author}
            onChange={(e) => setAuthor(e.target.value)}
            className="max-w-md"
          />
          <Textarea
            placeholder="Share your thoughts..."
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            className="min-h-[100px]"
          />
          <Button onClick={handleSubmitComment}>Submit</Button>
        </div>
      )}

      <div className="space-y-4">
        {comments.map((comment, index) => (
          <CommentComponent
            key={comment.id}
            comment={comment}
            onReply={onAddReply}
            expandedReplyIds={expandedReplyIds}
            onExpandReply={handleExpandReply}
            onCollapseReply={handleCollapseReply}
            isVisible={getVisibleCommentCount(index)}
          />
        ))}
        {comments.length === 0 && (
          <p className="text-gray-500 text-center py-8 bg-white/5 rounded-lg">
            No discussions yet. Be the first to start a discussion!
          </p>
        )}
      </div>
    </div>
  );
};