import { But<PERSON> } from "./ui/button";
import { PartyPopper } from "lucide-react";

export const CreatorButton = () => {
  return (
    
    <div className="flex justify-center mt-8">
      <a 
        href="https://bento.me/arnavsingh" 
        target="_blank" 
        rel="noopener noreferrer"
        className="inline-block"
      >
        
        <Button
          className="group relative overflow-hidden rounded-full px-6 py-2 transition-all duration-300 
          hover:shadow-xl shadow-lg bg-gradient-to-r from-pink-100 via-purple-100 to-indigo-100 
          hover:shadow-purple-100/50 text-purple-800"
        >
          <PartyPopper className="mr-2 h-5 w-5 inline-block" />
          <span>Meet the Creator</span>
          <span className="ml-2 inline-block transition-transform group-hover:translate-x-1">→</span>
        </Button>
      </a>
    </div>
  
  );
};
