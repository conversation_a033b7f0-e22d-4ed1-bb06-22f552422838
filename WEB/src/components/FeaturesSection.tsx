import { Brain, CheckCircle, ReceiptIndianRupee } from "lucide-react";
import { FeatureCard } from "./FeatureCard";

export const FeaturesSection = () => {
  return (
    <div className="grid md:grid-cols-3 gap-8 mb-4 mt-16">
      <div className="animate-fade-in" style={{ animationDelay: "0.2s" }}>
        <FeatureCard
          icon={Brain}
          title="AI Generated Solutions"
          description="Get detailed, step-by-step solutions to complex academic problems."
        />
      </div>
      <div className="animate-fade-in" style={{ animationDelay: "0.4s" }}>
        <FeatureCard
          icon={CheckCircle}
          title="Multiple Learning Formats"
          description="Support for text, images, and formulas. Upload screenshots or type your questions in any format."
        />
      </div>
      <div className="animate-fade-in" style={{ animationDelay: "0.6s" }}>
        <FeatureCard
          icon={ReceiptIndianRupee}
          title="100% FREE"
          description="This application is made by a student for students, and is completely free to use."
        />
      </div>
    </div>
  );
};