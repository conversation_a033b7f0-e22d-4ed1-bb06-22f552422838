
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';

interface FeedbackWidgetProps {
  className?: string;
}

export function FeedbackWidget({ className }: FeedbackWidgetProps) {
  const { user } = useSupabaseAuth();

  return (
    <a
      href="https://isotopeai.featurebase.app"
      target="_blank"
      rel="noopener noreferrer"
      data-featurebase-link
      className={className}
    >
      Feedback
    </a>
  );
} 
