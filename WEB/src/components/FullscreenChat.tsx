import React from 'react';

interface FullscreenChatProps {
  initialChatId?: string;
  initialQuery?: string;
  initialImage?: File;
}

export function ChatInterface({ initialChatId, initialQuery, initialImage }: FullscreenChatProps) {
  return (
    <div className="flex h-screen bg-background">
      <div className="flex-1 flex flex-col">
        <div className="flex-1 p-4">
          <div className="text-center text-muted-foreground">
            <h2 className="text-2xl font-semibold mb-4">Chat Interface</h2>
            <p>This component is being migrated to Supabase.</p>
            <p>Please check back later for full functionality.</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ChatInterface;
