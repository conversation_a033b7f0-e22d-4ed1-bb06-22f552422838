import React from "react";
import { useLocation } from "react-router-dom";
import { ChatInterface } from "./FullscreenChat";

/**
 * A wrapper component that extracts the chatId from URL query parameters
 * and passes it to the ChatInterface component.
 * This ensures consistent chat IDs between different views.
 * 
 * Note: All chat data is stored in Firebase, not in localStorage.
 */
export const FullscreenChatWrapper: React.FC = () => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const chatId = searchParams.get('chatId');
  
  console.log('FullscreenChatWrapper: extracted chatId from URL:', chatId);
  
  return <ChatInterface initialChatId={chatId || undefined} />;
};

export default FullscreenChatWrapper; 