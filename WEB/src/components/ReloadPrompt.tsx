import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

export const ReloadPrompt = () => {
  const [needRefresh, setNeedRefresh] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        setNeedRefresh(true);
      });
    }
  }, []);

  const handleRefresh = () => {
    setNeedRefresh(false);
    window.location.reload();
  };

  useEffect(() => {
    if (needRefresh) {
      toast({
        title: "New Content Available",
        description: "New content is available. Click to update.",
        action: (
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            Reload
          </Button>
        ),
        duration: 10000,
      });
    }
  }, [needRefresh]);

  return null;
}; 