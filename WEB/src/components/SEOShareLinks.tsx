import React, { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { generateStructuredData } from '@/utils/seoUtils';

interface SEOShareLinksProps {
  chatId: string;
  shareUrl: string;
  chatData: any;
}

export function SEOShareLinks({ chatId, shareUrl, chatData }: SEOShareLinksProps) {
  const [firstQuestion, setFirstQuestion] = useState<string>('AI Conversation');
  const [firstResponse, setFirstResponse] = useState<string>('');
  
  useEffect(() => {
    if (chatData?.messages?.length > 0) {
      // Get first user question
      const userQuestion = chatData.messages.find((msg: any) => msg.isUser)?.content;
      if (userQuestion) {
        setFirstQuestion(userQuestion);
      }
      
      // Get first AI response
      const aiResponse = chatData.messages.find((msg: any) => !msg.isUser)?.content;
      if (aiResponse) {
        // Truncate response for SEO
        setFirstResponse(aiResponse.length > 150 
          ? aiResponse.substring(0, 150) + "..." 
          : aiResponse);
      }
    }
  }, [chatData]);
  
  // Only render if we have a valid share URL
  if (!shareUrl) return null;
  
  const metaDescription = `AI conversation about "${firstQuestion}". View this shared chat on IsotopeAI.`;
  
  // Generate structured data using our utility
  const structuredData = generateStructuredData({
    title: firstQuestion,
    description: metaDescription,
    url: shareUrl,
    authorName: chatData?.username || 'IsotopeAI User',
    datePublished: new Date().toISOString(),
    content: firstResponse
  });
  
  return (
    <Helmet>
      <title>{firstQuestion} | IsotopeAI Chat</title>
      <meta name="description" content={metaDescription} />
      <meta name="keywords" content={`ai chat, ai conversation, ${firstQuestion.toLowerCase()}, isotope ai, ai answers`} />
      
      {/* Open Graph / Social Media */}
      <meta property="og:title" content={`${firstQuestion} | IsotopeAI Chat`} />
      <meta property="og:description" content={metaDescription} />
      <meta property="og:type" content="website" />
      <meta property="og:url" content={shareUrl} />
      <meta property="og:site_name" content="IsotopeAI" />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={`${firstQuestion} | IsotopeAI Chat`} />
      <meta name="twitter:description" content={metaDescription} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={shareUrl} />
      
      {/* Structured data for rich results */}
      <script type="application/ld+json">
        {structuredData}
      </script>
    </Helmet>
  );
} 