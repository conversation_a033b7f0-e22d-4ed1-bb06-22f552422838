import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
// TODO: Implement Supabase chat functions
import { Button } from './ui/button';
import { ChatMessage } from './ChatMessage';
import { ArrowLeft, Shield, Share2, AlertTriangle, Eye, Copy, Check, Calendar, MessageSquare, ExternalLink, Sparkles, Link as LinkIcon, Bookmark, Cloud, Tag } from 'lucide-react';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { Skeleton } from './ui/skeleton';
import { Message } from '@/types/chat';
import { motion, AnimatePresence } from 'framer-motion';
import { Badge } from './ui/badge';
import { Separator } from './ui/separator';
import { useToast } from '@/hooks/use-toast';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';

function SharedChatView() {
  const { chatId } = useParams<{ chatId: string }>();
  const [chatData, setChatData] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCopied, setIsCopied] = useState(false);
  const { user } = useSupabaseAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  // Extract the first user question for SEO purposes
  const firstUserQuestion = chatData?.messages?.find(msg => msg.isUser)?.content || chatData?.title || "Shared AI Conversation";
  const metaDescription = `AI conversation about "${firstUserQuestion}". View this shared chat on IsotopeAI.`;

  // Get AI response for SEO rich snippet
  const firstAIResponse = chatData?.messages?.find(msg => !msg.isUser)?.content || "";
  const truncatedAIResponse = firstAIResponse.length > 150
    ? firstAIResponse.substring(0, 150) + "..."
    : firstAIResponse;

  useEffect(() => {
    const fetchChat = async () => {
      if (!chatId) {
        setError('Invalid chat ID');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        console.log('SharedChatView: Fetching shared chat:', chatId, 'Auth state:', user ? 'logged in' : 'not logged in');

        // Use any type to avoid TypeScript errors
        const data: any = await getAIChat(chatId);

        if (!data) {
          console.log('SharedChatView: Chat data not found');
          setError('This chat could not be found. It may have been deleted.');
          setLoading(false);
          return;
        }

        console.log('SharedChatView: Successfully loaded shared chat data:', data.title || 'Untitled');

        // Safely log username information if it exists
        const username = data.username || 'Unknown';
        console.log('SharedChatView: Shared by user field check:', {
          username: username,
          hasUsername: Boolean(username !== 'Unknown')
        });

        // Increment view count
        if (chatId) {
          console.log('SharedChatView: Incrementing view count');
          try {
            await incrementChatViewCount(chatId);
            console.log('SharedChatView: View count incremented successfully');
          } catch (viewErr) {
            console.error('SharedChatView: Error incrementing view count (non-critical):', viewErr);
            // Continue anyway as this is non-critical
          }
        }

        setChatData(data);
      } catch (err: any) {
        console.error('SharedChatView: Error fetching shared chat:', err);
        if (err.message?.includes('not found')) {
          setError('This chat does not exist or has been deleted.');
        } else {
          setError(err.message || 'Failed to load chat');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchChat();
  }, [chatId, user]);

  const goBack = () => {
    navigate(-1);
  };

  const copyShareLink = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      setIsCopied(true);
      toast({
        title: "Link Copied!",
        description: "Share link copied to clipboard.",
      });
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      toast({
        title: "Copy Failed",
        description: "Failed to copy link to clipboard. Please try again.",
        variant: "destructive"
      });
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background to-background/95 relative overflow-hidden shared-page-container custom-scrollbar">
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl opacity-60 pointer-events-none" />
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-secondary/5 rounded-full blur-3xl opacity-60 pointer-events-none" />

        {/* Blur circles */}
        <div className="blur-circle blur-circle-1"></div>
        <div className="blur-circle blur-circle-2"></div>

        {/* Animated particles */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1.5 h-1.5 rounded-full bg-primary/30"
              initial={{
                x: Math.random() * window.innerWidth,
                y: Math.random() * window.innerHeight,
                scale: Math.random() * 0.5 + 0.5,
                opacity: Math.random() * 0.6 + 0.2
              }}
              animate={{
                y: [null, Math.random() * -200 - 100],
                opacity: [null, 0]
              }}
              transition={{
                duration: Math.random() * 8 + 12,
                repeat: Infinity,
                ease: "linear"
              }}
            />
          ))}
        </div>

        <div className="max-w-4xl mx-auto relative z-10 mt-16">
          {/* Skeleton for Gemini-style header */}
          <div className="mb-8 content-panel rounded-xl p-6 shadow-md">
            <div className="flex flex-col space-y-6">
              <Skeleton className="h-8 w-3/4 mx-auto rounded-md" />
              <div className="flex justify-center items-center gap-4">
                <Skeleton className="h-4 w-24 rounded-full" />
                <Skeleton className="h-5 w-5 rounded-full" />
              </div>
              <Skeleton className="h-4 w-48 mx-auto rounded-full" />
            </div>
          </div>

          <div className="space-y-8">
            {[1, 2, 3, 4].map((i) => (
              <motion.div
                key={i}
                className="space-y-3 content-panel rounded-xl p-6 shadow-md shared-transition-all"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: i * 0.1 }}
              >
                <div className="flex items-center gap-3">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                </div>
                <Skeleton className="h-28 w-full rounded-lg" />
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background to-background/95 relative overflow-hidden shared-page-container custom-scrollbar">
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl opacity-60 pointer-events-none" />
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-secondary/5 rounded-full blur-3xl opacity-60 pointer-events-none" />

        {/* Blur circles */}
        <div className="blur-circle blur-circle-1"></div>
        <div className="blur-circle blur-circle-2"></div>

        {/* Animated particles */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1.5 h-1.5 rounded-full bg-primary/30"
              initial={{
                x: Math.random() * window.innerWidth,
                y: Math.random() * window.innerHeight,
                scale: Math.random() * 0.5 + 0.5,
                opacity: Math.random() * 0.6 + 0.2
              }}
              animate={{
                y: [null, Math.random() * -200 - 100],
                opacity: [null, 0]
              }}
              transition={{
                duration: Math.random() * 8 + 12,
                repeat: Infinity,
                ease: "linear"
              }}
            />
          ))}
        </div>

        <div className="max-w-4xl mx-auto relative z-10 mt-16">
          <motion.div
            className="text-center py-16 px-6 content-panel rounded-xl shadow-lg subtle-transition"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.4 }}
          >
            <div className="relative w-16 h-16 mx-auto mb-6">
              <div className="relative flex items-center justify-center w-full h-full bg-yellow-500/10 rounded-full border border-yellow-500/30">
                <AlertTriangle className="h-8 w-8 text-yellow-500" />
              </div>
            </div>
            <h2 className="text-2xl font-bold mb-3 font-spaceGrotesk text-foreground">
              Chat Not Available
            </h2>
            <p className="text-muted-foreground mb-8 max-w-md mx-auto">{error}</p>
          </motion.div>
        </div>
      </div>
    );
  }

  const messageCount = chatData?.messages?.length || 0;
  const formattedDate = (() => {
    try {
      if (!chatData?.createdAt) return 'Unknown date';

      const createdAt = chatData.createdAt;
      console.log('CreatedAt data type:', typeof createdAt, 'Value:', createdAt);

      // Check for our new structured date format
      if (typeof createdAt === 'object' && createdAt !== null &&
          'day' in createdAt && 'month' in createdAt && 'year' in createdAt) {
        // If formatted field exists, use it directly
        if (createdAt.formatted && typeof createdAt.formatted === 'string') {
          return createdAt.formatted;
        }

        // Otherwise format it manually
        const { day, month, year } = createdAt;
        const date = new Date(year, month - 1, day); // Month is 0-indexed in Date constructor
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      }

      // If it's a Firebase Timestamp with toMillis function
      if (typeof createdAt.toMillis === 'function') {
        return new Date(createdAt.toMillis()).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      }

      // Serialized Firebase Timestamp with seconds field
      if (createdAt.seconds && typeof createdAt.seconds === 'number') {
        return new Date(createdAt.seconds * 1000).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      }

      // Serialized Firebase Timestamp with _seconds field (older format)
      if (createdAt._seconds && typeof createdAt._seconds === 'number') {
        return new Date(createdAt._seconds * 1000).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      }

      // Plain timestamp in milliseconds
      if (typeof createdAt === 'number') {
        return new Date(createdAt).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      }

      // Handle serialized timestamp from Firestore (object with seconds and nanoseconds)
      if (typeof createdAt === 'object' && createdAt !== null && 'seconds' in createdAt && 'nanoseconds' in createdAt) {
        return new Date(createdAt.seconds * 1000).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      }

      // Check for ISO string format
      if (typeof createdAt === 'string') {
        const dateObject = new Date(createdAt);
        if (!isNaN(dateObject.getTime())) {
          return dateObject.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
          });
        }
      }

      console.log('Unknown createdAt format:', createdAt);
      return 'Unknown date';
    } catch (err) {
      console.error('Error formatting date:', err, chatData?.createdAt);
      return 'Unknown date';
    }
  })();
  const viewCount = chatData?.viewCount || 0;

  console.log('Rendering chat with username:', chatData?.username);

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-background/95 relative overflow-hidden shared-page-container custom-scrollbar">
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl opacity-60 pointer-events-none" />
      <div className="absolute bottom-0 left-0 w-96 h-96 bg-secondary/5 rounded-full blur-3xl opacity-60 pointer-events-none" />

      {/* Blur circles */}
      <div className="blur-circle blur-circle-1"></div>
      <div className="blur-circle blur-circle-2"></div>

      {/* Animated particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1.5 h-1.5 rounded-full bg-primary/30"
            initial={{
              x: Math.random() * window.innerWidth,
              y: Math.random() * window.innerHeight,
              scale: Math.random() * 0.5 + 0.5,
              opacity: Math.random() * 0.6 + 0.2
            }}
            animate={{
              y: [null, Math.random() * -200 - 100],
              opacity: [null, 0]
            }}
            transition={{
              duration: Math.random() * 8 + 12,
              repeat: Infinity,
              ease: "linear"
            }}
          />
        ))}
      </div>

      <Helmet>
        <html lang="en" />
        <title>{firstUserQuestion} | IsotopeAI Chat</title>
        <meta name="description" content={metaDescription} />
        <meta name="keywords" content={`ai chat, ai conversation, ${firstUserQuestion.toLowerCase()}, isotope ai, ai answers, ${chatData?.subject || ''}`} />

        {/* Open Graph / Social Media */}
        <meta property="og:title" content={`${firstUserQuestion} | IsotopeAI Chat`} />
        <meta property="og:description" content={metaDescription} />
        <meta property="og:type" content="website" />
        <meta property="og:url" content={window.location.href} />
        <meta property="og:site_name" content="IsotopeAI" />

        {/* Twitter Card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={`${firstUserQuestion} | IsotopeAI Chat`} />
        <meta name="twitter:description" content={metaDescription} />

        {/* Canonical URL */}
        <link rel="canonical" href={window.location.href} />

        {/* Structured data for rich results */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Conversation",
            "headline": firstUserQuestion,
            "datePublished": (() => {
              try {
                if (!chatData?.createdAt) return new Date().toISOString();

                const createdAt = chatData.createdAt;

                // Check for our new structured date format
                if (typeof createdAt === 'object' && createdAt !== null &&
                    'day' in createdAt && 'month' in createdAt && 'year' in createdAt) {
                  const { day, month, year } = createdAt;
                  // Use timestamp if available for more precision
                  if (createdAt.timestamp && typeof createdAt.timestamp === 'number') {
                    return new Date(createdAt.timestamp).toISOString();
                  }
                  // Otherwise construct from components
                  const date = new Date(year, month - 1, day);
                  return date.toISOString();
                }

                if (typeof createdAt.toMillis === 'function') {
                  return new Date(createdAt.toMillis()).toISOString();
                }

                if (createdAt.seconds && typeof createdAt.seconds === 'number') {
                  return new Date(createdAt.seconds * 1000).toISOString();
                }

                if (createdAt._seconds && typeof createdAt._seconds === 'number') {
                  return new Date(createdAt._seconds * 1000).toISOString();
                }

                if (typeof createdAt === 'number') {
                  return new Date(createdAt).toISOString();
                }

                // Handle serialized timestamp from Firestore (object with seconds and nanoseconds)
                if (typeof createdAt === 'object' && createdAt !== null && 'seconds' in createdAt && 'nanoseconds' in createdAt) {
                  return new Date(createdAt.seconds * 1000).toISOString();
                }

                // Check for ISO string format
                if (typeof createdAt === 'string') {
                  const dateObject = new Date(createdAt);
                  if (!isNaN(dateObject.getTime())) {
                    return dateObject.toISOString();
                  }
                }

                return new Date().toISOString();
              } catch (err) {
                console.error('Error formatting ISO date:', err);
                return new Date().toISOString();
              }
            })(),
            "author": {
              "@type": "Person",
              "name": chatData?.username || "IsotopeAI User"
            },
            "publisher": {
              "@type": "Organization",
              "name": "IsotopeAI",
              "logo": {
                "@type": "ImageObject",
                "url": `${window.location.origin}/icon-192x192.png`
              }
            },
            "description": metaDescription,
            "mainEntityOfPage": {
              "@type": "WebPage",
              "@id": window.location.href
            }
          })}
        </script>

        {/* Breadcrumbs structured data */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
              {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": window.location.origin
              },
              {
                "@type": "ListItem",
                "position": 2,
                "name": firstUserQuestion,
                "item": window.location.href
              }
            ]
          })}
        </script>

        {/* FAQ structured data (converting the Q&A to FAQ format for search engines) */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": [
              {
                "@type": "Question",
                "name": firstUserQuestion,
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": truncatedAIResponse
                }
              }
            ]
          })}
        </script>
      </Helmet>

      {/* IsotopeAI logo in top left */}
      <div className="fixed top-4 left-4 z-20">
        <Link to="/" className="flex items-center space-x-2 group">
          <div className="relative w-8 h-8 transition-all duration-300">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full blur-md group-hover:opacity-100 opacity-0 transition-opacity"></div>
            <img
              src="/icon-192x192.png"
              alt="IsotopeAI Logo"
              className="w-full h-full rounded-full border border-border/30 shadow-lg relative z-10 group-hover:scale-105 transition-transform duration-300"
            />
          </div>
          <span className="font-bold text-lg bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/70 transition-all duration-300">
            IsotopeAI
          </span>
        </Link>
      </div>

      <div className="max-w-4xl mx-auto px-4 pt-24 pb-40 relative z-10 shared-page-typography">
        {/* Back button */}


        {/* Gemini-style Header */}
        <motion.div
          className="mb-8 bg-background/50 backdrop-blur-sm rounded-xl p-6 border border-border/40 shadow-md relative overflow-hidden"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Decorative gradient */}
          <div className="absolute -inset-0.5 bg-gradient-to-r from-primary/10 via-transparent to-primary/10 pointer-events-none opacity-50"></div>

          <div className="relative z-10 flex flex-col space-y-5">
            {/* Title showing the user's prompt */}
            <h1 className="text-2xl md:text-3xl font-bold font-spaceGrotesk gradient-heading text-center">
              {firstUserQuestion}
            </h1>

            {/* User info and metadata */}
            <div className="flex flex-wrap items-center justify-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1.5 bg-background/60 px-3 py-1.5 rounded-full border border-border/30 enhanced-badge">
                <LinkIcon className="h-4 w-4" />
                <span className="font-medium">{chatData?.username || "User"}</span>
              </div>

              <div className="flex items-center gap-1.5 bg-background/60 px-3 py-1.5 rounded-full border border-border/30 enhanced-badge">
                <Calendar className="h-4 w-4" />
                <span>{formattedDate}</span>
              </div>

              <div className="flex items-center gap-1.5 bg-background/60 px-3 py-1.5 rounded-full border border-border/30 enhanced-badge">
                <Eye className="h-4 w-4" />
                <span>{viewCount} views</span>
              </div>

              <div className="flex items-center gap-1.5 bg-background/60 px-3 py-1.5 rounded-full border border-border/30 enhanced-badge">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={copyShareLink}
                  className="flex items-center gap-1.5 p-0 h-auto hover:bg-transparent hover:text-primary transition-colors"
                  aria-label="Copy share link"
                >
                  {isCopied ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Share2 className="h-4 w-4" />
                  )}
                  <span>Share</span>
                </Button>
              </div>
            </div>
          </div>
        </motion.div>

        {/* User info panel */}
        <motion.div
          className="mb-10 bg-background/50 backdrop-blur-sm rounded-xl p-6 border border-border/40 shadow-md relative overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-5">
            <div className="flex items-start gap-4">
              <div className="relative floating-animation-delayed">
                <div className="absolute -inset-0.5 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full blur-[2px]"></div>
                <Avatar className="w-14 h-14 relative border-2 border-background">
                  {chatData?.userPhotoUrl ? (
                    <AvatarImage src={chatData.userPhotoUrl} alt={chatData.username || "User"} />
                  ) : (
                    <AvatarFallback className="bg-primary/20 text-primary">
                      {chatData?.username?.charAt(0)?.toUpperCase() || "U"}
                    </AvatarFallback>
                  )}
                </Avatar>
              </div>

              <div>
                <div className="flex items-center mb-2 flex-wrap gap-2">
                  {chatData?.username && (
                    <h2 className="text-base font-medium">
                      Shared by <span className="text-primary font-semibold">{chatData.username}</span>
                    </h2>
                  )}
                  {chatData?.userVerified && (
                    <Badge variant="outline" className="ml-0.5 bg-primary/10 text-primary font-normal text-xs enhanced-badge">
                      <Shield className="h-3 w-3 mr-1" /> Verified
                    </Badge>
                  )}
                </div>

                <div className="flex items-center flex-wrap gap-2">
                  {chatData?.subject && (
                    <Badge className="bg-secondary/15 text-secondary hover:bg-secondary/20 border-secondary/30 enhanced-badge">
                      <Tag className="h-3 w-3 mr-1.5" />
                      {chatData.subject}
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            {chatData?.title && (
              <div className="md:text-right mt-4 md:mt-0 p-3 bg-background/70 rounded-lg border border-border/40 md:max-w-xs transition-all duration-300 hover:bg-background/90 hover:border-primary/20 hover:shadow-md floating-animation">
                <h3 className="text-lg font-bold font-spaceGrotesk mb-1 gradient-heading">{chatData.title}</h3>
                <p className="text-sm text-muted-foreground">
                  <MessageSquare className="h-3.5 w-3.5 inline-block mr-1.5" />
                  {messageCount} message{messageCount !== 1 ? 's' : ''}
                </p>
              </div>
            )}
          </div>
        </motion.div>

        {/* Chat messages */}
        <div className="space-y-6 mb-40">
          <AnimatePresence mode="popLayout">
            {chatData?.messages?.map((message: Message, index: number) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                className={`clean-message transition-all duration-300 ${index === 0 ? 'first-message' : ''}`}
              >
                <ChatMessage
                  content={message.content}
                  isUser={message.isUser}
                  image={message.image}
                  hideCodeBlocks={!message.isUser}
                />
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {/* Fixed footer */}
        <motion.div
          className="fixed bottom-0 left-0 right-0 py-6 bg-background/95 backdrop-blur-md border-t border-border/40 shadow-[0_-4px_20px_rgba(0,0,0,0.08)] z-20"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <div className="max-w-4xl mx-auto px-4">
            <div className="flex flex-col items-center gap-4 w-full">
              <div className="flex flex-wrap justify-center gap-5 mb-4 text-sm">
                <Link to="/ai-landing" className="text-primary hover:text-primary/80 transition-colors hover:underline flex items-center gap-1.5 enhanced-badge px-2 py-1 rounded-full">
                  <Sparkles className="h-4 w-4" /> AI
                </Link>
                <span className="text-muted-foreground">•</span>
                <Link to="/groups-landing" className="text-primary hover:text-primary/80 transition-colors hover:underline flex items-center gap-1.5 enhanced-badge px-2 py-1 rounded-full">
                  <MessageSquare className="h-4 w-4" /> Groups
                </Link>
                <span className="text-muted-foreground">•</span>
                <Link to="/productivity-landing" className="text-primary hover:text-primary/80 transition-colors hover:underline flex items-center gap-1.5 enhanced-badge px-2 py-1 rounded-full">
                  <Bookmark className="h-4 w-4" /> Productivity
                </Link>
                <span className="text-muted-foreground">•</span>
                <Link to="/tasks-landing" className="text-primary hover:text-primary/80 transition-colors hover:underline flex items-center gap-1.5 enhanced-badge px-2 py-1 rounded-full">
                  <Cloud className="h-4 w-4" /> Tasks
                </Link>
              </div>

              <Link
                to="/"
                className="group relative overflow-hidden rounded-full px-6 py-3 transition-all duration-300 button-glow"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-primary/10 to-primary/20 group-hover:from-primary/30 group-hover:via-primary/20 group-hover:to-primary/30 transition-all duration-500"></div>
                <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                  <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-primary/30 to-primary/20 animate-shimmer bg-[length:200%_100%]"></div>
                </div>
                <span className="relative z-10 font-medium text-primary">
                  Go to IsotopeAI
                </span>
              </Link>

              <p className="mt-3 text-muted-foreground text-sm text-center max-w-lg">
                IsotopeAI may sometimes display inaccurate information, so please double-check important facts.
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}

export default SharedChatView;