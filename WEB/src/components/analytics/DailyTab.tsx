import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { ThemeProvider } from '@mui/material';
import { <PERSON><PERSON><PERSON>, LineChart } from '@mui/x-charts';
import { Calendar, Clock, Timer, BookOpen, Download, TrendingUp, ListChecks } from "lucide-react";
import { <PERSON><PERSON><PERSON>2, <PERSON><PERSON><PERSON>, LineChart as LineChartIcon } from 'lucide-react';
import DaySelector from '@/components/analytics/DaySelector';
import DailyOverview from '@/components/analytics/DailyOverview';
import StudySessionsView from '@/components/analytics/StudySessionsView';
import { secondsToHours } from '@/utils/timeUtils';

// Interface for timeline sessions
interface TimelineSession {
  id: string;
  subject: string;
  taskType: string;
  taskDescription: string;
  startTime: Date;
  endTime: Date;
  duration: number;
  subjectColor?: string;
}

interface Analytics {
  dailyStats: {
    date: string;
    totalDuration: number;
    subjectDurations: { [key: string]: number };
    completedPomodor<PERSON>: number;
  }[];
  weeklyStats: {
    weekNumber: number;
    year: number;
    totalDuration: number;
    subjectDurations: { [key: string]: number };
    completedPomodoros: number;
  }[];
  monthlyStats: {
    month: string;
    year: number;
    monthKey: string;
    totalDuration: number;
    subjectDurations: { [key: string]: number };
    completedPomodoros: number;
  }[];
  subjectStats: {
    subject: string;
    totalDuration: number;
    completedPomodoros: number;
    averageSessionDuration: number;
  }[];
}

interface DailyTabProps {
  analytics: Analytics;
  dailySessions?: { 
    date: string;
    sessions: TimelineSession[];
  }[];
  selectedDate: string;
  setSelectedDate: (date: string) => void;
  formatDuration: (seconds: number) => string;
  subjectColorMap: { [subject: string]: string };
  targetHours: number;
  theme: string;
  muiTheme: any;
}

const DailyTab: React.FC<DailyTabProps> = ({
  analytics,
  dailySessions = [],
  selectedDate,
  setSelectedDate,
  formatDuration,
  subjectColorMap,
  targetHours,
  theme,
  muiTheme
}) => {
  // Get the sessions for the selected date
  const selectedDaySessions = dailySessions.find(day => day.date === selectedDate)?.sessions || [];

  // Check if selected date is today
  const today = new Date();
  const formattedToday = today.toISOString().split('T')[0]; // Format as YYYY-MM-DD
  const isSelectedDateToday = selectedDate === formattedToday;

  // Get task type counts for the selected day
  const taskTypeCounts = selectedDaySessions.reduce((counts: {[key: string]: number}, session) => {
    const taskType = session.taskType || 'Study';
    counts[taskType] = (counts[taskType] || 0) + 1;
    return counts;
  }, {});

  // Convert to array for easier rendering
  const taskTypeCountsArray = Object.entries(taskTypeCounts)
    .map(([type, count]) => ({ type, count }))
    .sort((a, b) => b.count - a.count);

  return (
    <>
      <DaySelector
        selectedDate={selectedDate}
        onDateChange={setSelectedDate}
        dailyStats={analytics.dailyStats}
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <div className="lg:col-span-2">
          <DailyOverview
            dailyStats={analytics.dailyStats}
            subjectStats={analytics.subjectStats}
            selectedDate={selectedDate}
            onDateChange={setSelectedDate}
            formatDuration={formatDuration}
            subjectColorMap={subjectColorMap}
          />
        </div>

        <Card className="bg-card/80 backdrop-blur-sm text-card-foreground border shadow-xl rounded-xl overflow-hidden h-full">
          <CardHeader className="border-b pb-3">
            <CardTitle className="text-lg font-medium flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-primary" />
              <span>Daily Summary</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-6 flex flex-col h-[calc(100%-5rem)] space-y-6">
            {selectedDate && analytics?.dailyStats.length ? (
              <>
                <div className="flex items-center justify-between p-3 bg-muted/40 rounded-lg border backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <div className="p-2.5 bg-primary/10 rounded-full">
                      <Clock className="h-5 w-5 text-primary" />
                    </div>
                    <span className="text-sm font-medium">Study Duration</span>
                  </div>
                  <span className="text-xl font-semibold text-primary">
                    {formatDuration(analytics.dailyStats.find(day => day.date === selectedDate)?.totalDuration || 0)}
                  </span>
                </div>

                <div className="flex items-center justify-between p-3 bg-muted/40 rounded-lg border backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <div className="p-2.5 bg-pink-500/10 rounded-full">
                      <Timer className="h-5 w-5 text-pink-500" />
                    </div>
                    <span className="text-sm font-medium">Pomodoros</span>
                  </div>
                  <span className="text-xl font-semibold text-pink-500">
                    {analytics.dailyStats.find(day => day.date === selectedDate)?.completedPomodoros || 0}
                  </span>
                </div>

                <div className="flex items-center justify-between p-3 bg-muted/40 rounded-lg border backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <div className="p-2.5 bg-cyan-500/10 rounded-full">
                      <BookOpen className="h-5 w-5 text-cyan-500" />
                    </div>
                    <span className="text-sm font-medium">Subjects</span>
                  </div>
                  <span className="text-xl font-semibold text-cyan-500">
                    {Object.keys(analytics.dailyStats.find(day => day.date === selectedDate)?.subjectDurations || {})
                      .filter(subject => (analytics.dailyStats.find(day => day.date === selectedDate)?.subjectDurations[subject] || 0) > 0)
                      .length}
                  </span>
                </div>

                <div className="mt-auto p-4 bg-gradient-to-br from-muted/30 to-muted/10 rounded-lg border backdrop-blur-sm">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm font-medium text-muted-foreground">Target Progress</span>
                    <span className={`text-sm font-medium ${
                      ((analytics.dailyStats.find(day => day.date === selectedDate)?.totalDuration || 0) >= (targetHours * 3600)) ?
                      'text-emerald-500' : 'text-foreground'
                    }`}>
                      {Math.round(Math.min(100, ((analytics.dailyStats.find(day => day.date === selectedDate)?.totalDuration || 0) / (targetHours * 3600)) * 100))}%
                    </span>
                  </div>
                  <div className="h-2 bg-muted rounded-full overflow-hidden">
                    <div
                      className="h-full rounded-full transition-all duration-500 bg-gradient-to-r from-indigo-500 to-pink-500"
                      style={{
                        width: `${Math.min(100, ((analytics.dailyStats.find(day => day.date === selectedDate)?.totalDuration || 0) / (targetHours * 3600)) * 100)}%`
                      }}
                    />
                  </div>
                  <div className="flex justify-between text-xs text-muted-foreground mt-2">
                    <span>0h</span>
                    <span>{targetHours}h</span>
                  </div>
                </div>
              </>
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-center">
                <Calendar className="h-16 w-16 text-muted-foreground mb-4 opacity-50" />
                <p className="text-muted-foreground">No data available for the selected date.</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <Card className="bg-card/80 backdrop-blur-sm text-card-foreground border shadow-xl rounded-xl overflow-hidden">
          <CardHeader className="border-b pb-3 flex flex-row items-center justify-between">
            <CardTitle className="text-lg font-medium flex items-center gap-2">
              <LineChartIcon className="h-5 w-5 text-primary" />
              <span>Study Time Trends</span>
            </CardTitle>
            <button
              className="p-1.5 bg-muted hover:bg-accent rounded-lg transition-colors"
              title="Download chart"
            >
              <Download className="h-4 w-4 text-muted-foreground" />
            </button>
          </CardHeader>
          <CardContent className="h-[500px] pt-6">
            <ThemeProvider theme={muiTheme}>
              <LineChart
                className="chart-with-theme"
                sx={{
                  '& .MuiChartsAxis-tickLabel, & .MuiChartsAxis-label, & .MuiChartsLegend-series': {
                    fill: theme === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.8)'
                  }
                }}
                xAxis={[{
                  data: analytics?.dailyStats.map(stat => stat.date) || [],
                  scaleType: 'band',
                  label: 'Date',
                }]}
                yAxis={[
                  {
                    id: 'hours',
                    scaleType: 'linear',
                    label: 'Study Time (hours)',
                    min: 0,
                  },
                  {
                    id: 'pomodoros',
                    scaleType: 'linear',
                    label: 'Pomodoros Completed',
                    position: 'right',
                    min: 0,
                  }
                ]}
                series={[
                  {
                    data: analytics?.dailyStats.map(stat => secondsToHours(stat.totalDuration)) || [],
                    label: 'Total Time (hours)',
                    color: muiTheme.palette.primary.main,
                    area: true,
                    showMark: true,
                    curve: 'monotoneX',
                    connectNulls: true,
                    yAxisKey: 'hours',
                    valueFormatter: (value) => {
                      const seconds = value * 3600;
                      return formatDuration(seconds);
                    },
                  },
                  {
                    data: analytics?.dailyStats.map(stat => stat.completedPomodoros) || [],
                    label: 'Completed Pomodoros',
                    color: muiTheme.palette.secondary.main,
                    area: true,
                    showMark: true,
                    curve: 'monotoneX',
                    connectNulls: true,
                    yAxisKey: 'pomodoros',
                  },
                ]}
                height={450}
                margin={{ top: 20, right: 60, bottom: 80, left: 80 }} // Increased left margin for Y-axis label
                slotProps={{
                  legend: {
                    direction: 'row',
                    position: { vertical: 'bottom', horizontal: 'middle' },
                    padding: 10,
                    itemMarkWidth: 10,
                    itemMarkHeight: 10,
                    markGap: 5,
                    itemGap: 15,
                  },
                }}
              />
            </ThemeProvider>
          </CardContent>
        </Card>

        <Card className="bg-card/80 backdrop-blur-sm text-card-foreground border shadow-xl rounded-xl overflow-hidden">
          <CardHeader className="border-b pb-3 flex flex-row items-center justify-between">
            <CardTitle className="text-lg font-medium flex items-center gap-2">
              <ChartPie className="h-5 w-5 text-pink-500" />
              <span>Subject Distribution</span>
            </CardTitle>
            <div className="flex items-center gap-2">
              <button
                className="p-1.5 bg-muted hover:bg-accent rounded-lg transition-colors"
                title="Switch chart type"
              >
                <BarChart2 className="h-4 w-4 text-muted-foreground" />
              </button>
              <button
                className="p-1.5 bg-muted hover:bg-accent rounded-lg transition-colors"
                title="Download chart"
              >
                <Download className="h-4 w-4 text-muted-foreground" />
              </button>
            </div>
          </CardHeader>
          <CardContent className="h-[500px] pt-6">
            <ThemeProvider theme={muiTheme}>
              <BarChart
                className="chart-with-theme"
                sx={{
                  '& .MuiChartsAxis-tickLabel, & .MuiChartsAxis-label, & .MuiChartsLegend-series': {
                    fill: theme === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.8)'
                  }
                }}
                xAxis={[{
                  data: analytics?.dailyStats.slice(-7).map(stat => stat.date) || [],
                  scaleType: 'band',
                  label: 'Date',
                }]}
                yAxis={[{
                  scaleType: 'linear',
                  label: 'Study Time (hours)',
                  min: 0,
                }]}
                series={
                  analytics?.subjectStats.map((subject, index) => ({
                    data: analytics.dailyStats.slice(-7).map(day =>
                      secondsToHours(day.subjectDurations[subject.subject] || 0)
                    ),
                    label: subject.subject,
                    color: subjectColorMap[subject.subject] || '#6366f1',
                    stack: 'total',
                    valueFormatter: (value) => {
                      const seconds = value * 3600;
                      return formatDuration(seconds);
                    },
                  })) || []
                }
                height={450}
                margin={{ top: 20, right: 30, bottom: 80, left: 80 }} // Increased left margin for Y-axis label
                slotProps={{
                  legend: {
                    direction: 'row',
                    position: { vertical: 'bottom', horizontal: 'middle' },
                    padding: 10,
                    itemMarkWidth: 10,
                    itemMarkHeight: 10,
                    markGap: 5,
                    itemGap: 15,
                  },
                }}
              />
            </ThemeProvider>
          </CardContent>
        </Card>
      </div>

      {/* Study Trend Chart */}
      <div className="mb-6">
        <Card className="bg-card text-card-foreground border shadow-md overflow-hidden h-full">
          <CardHeader className="border-b pb-4">
            <CardTitle className="text-lg font-medium flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-primary" />
              <span>Study Trend</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <ThemeProvider theme={muiTheme}>
              <LineChart
                height={240}
                series={[
                  {
                    data: analytics ? Object.values(analytics.dailyStats.map(day => secondsToHours(day.totalDuration))) : [],
                    label: 'Daily Study Hours',
                    area: true,
                    showMark: false,
                    color: theme === 'dark' ? '#818cf8' : '#6366f1',
                  },
                ]}
                xAxis={[
                  {
                    data: analytics ? Object.values(analytics.dailyStats.map((day, index) => index)) : [],
                    scaleType: 'point',
                    valueFormatter: (value: number) => {
                      const dates = analytics ? analytics.dailyStats.map(day => day.date) : [];
                      return dates[value] || '';
                    },
                  },
                ]}
                sx={{
                  '.MuiMarkElement-root': {
                    stroke: '#6366f1',
                    fill: '#fff',
                    strokeWidth: 2,
                  },
                  '.MuiChartsAxis-tickLabel': {
                    fill: theme === 'dark' ? '#a1a1aa' : '#71717a',
                  },
                  '.MuiChartsAxis-line': {
                    stroke: theme === 'dark' ? '#27272a' : '#e4e4e7',
                  },
                  '.MuiChartsAxis-tick': {
                    stroke: theme === 'dark' ? '#27272a' : '#e4e4e7',
                  },
                  '.MuiAreaElement-root': {
                    fill: theme === 'dark' ? 'url(#darkGradient)' : 'url(#lightGradient)',
                  },
                }}
              >
                <defs>
                  <linearGradient id="darkGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="0%" stopColor="#818cf8" stopOpacity={0.4} />
                    <stop offset="100%" stopColor="#818cf8" stopOpacity={0} />
                  </linearGradient>
                  <linearGradient id="lightGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="0%" stopColor="#6366f1" stopOpacity={0.4} />
                    <stop offset="100%" stopColor="#6366f1" stopOpacity={0} />
                  </linearGradient>
                </defs>
              </LineChart>
            </ThemeProvider>
          </CardContent>
        </Card>
      </div>

      {/* Add Study Sessions View */}
      <div className="mb-6">
        <StudySessionsView
          dailySessions={dailySessions}
          selectedDate={selectedDate}
          formatDuration={formatDuration}
          title="Today's Study Sessions"
        />
      </div>
    </>
  );
};

export default DailyTab;
