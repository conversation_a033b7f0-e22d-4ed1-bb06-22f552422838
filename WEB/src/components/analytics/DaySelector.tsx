import { ArrowLeft, ArrowRight, Calendar } from "lucide-react";

// Helper function to format Date object to YYYY-MM-DD in local time
const formatDateToLocalYYYYMMDD = (date: Date): string => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are 0-indexed
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

interface DaySelectorProps {
  selectedDate: string;
  onDateChange: (date: string) => void;
  dailyStats: {
    date: string;
    totalDuration: number;
    subjectDurations: { [key: string]: number };
    completedPomodoros: number;
  }[];
}

const DaySelector: React.FC<DaySelectorProps> = ({
  selectedDate,
  onDateChange,
  dailyStats
}) => {
  // Get 5 days around selected date (instead of 7)
  const getDaysAround = () => {
    const todayIndex = dailyStats.findIndex(day => day.date === selectedDate);
    if (todayIndex === -1 && dailyStats.length > 0) {
        // If selectedDate not found, default to last 5 days
        return dailyStats.slice(-5);
    } else if (todayIndex === -1) {
        // No data at all
        return [];
    }

    const startIndex = Math.max(0, todayIndex - 2);
    const endIndex = Math.min(dailyStats.length - 1, todayIndex + 2);

    // Ensure we get exactly 5 days if possible, centering around the selected date
    let daysAround = dailyStats.slice(startIndex, endIndex + 1);

    // Adjust if we are near the beginning or end
    if (daysAround.length < 5) {
        if (startIndex === 0) {
            daysAround = dailyStats.slice(0, Math.min(5, dailyStats.length));
        } else if (endIndex === dailyStats.length - 1) {
            daysAround = dailyStats.slice(Math.max(0, dailyStats.length - 5));
        }
    }

    return daysAround;
  };

  const daysToShow = getDaysAround();

  const navigateDates = (direction: 'prev' | 'next') => {
    const currentIndex = dailyStats.findIndex(day => day.date === selectedDate);
    if (currentIndex === -1) return; // Should not happen if daysToShow is populated

    if (direction === 'prev' && currentIndex > 0) {
      onDateChange(dailyStats[currentIndex - 1].date);
    } else if (direction === 'next' && currentIndex < dailyStats.length - 1) {
      onDateChange(dailyStats[currentIndex + 1].date);
    }
  };

  // Get current month and year for display
  const currentDate = new Date(selectedDate);
  const monthYear = currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });

  // Check if navigation buttons should be disabled
  const currentIndex = dailyStats.findIndex(day => day.date === selectedDate);
  const isPrevDisabled = currentIndex <= 0;
  const isNextDisabled = currentIndex >= dailyStats.length - 1;

  if (dailyStats.length === 0) {
      return <div className="text-center text-muted-foreground mb-4">No data to display dates.</div>;
  }

  return (
    <div className="mb-4">
      <div className={`bg-card border shadow-md rounded-xl p-2.5 ${
        daysToShow.length <= 3 ? 'w-full max-w-[280px]' : 'w-full max-w-[360px]'
      } mx-auto`}>
        <div className="flex items-center justify-between mb-2">
          <button
            onClick={() => navigateDates('prev')}
            className={`p-1.5 rounded-lg ${isPrevDisabled
              ? 'bg-muted text-muted-foreground/50 cursor-not-allowed'
              : 'bg-muted hover:bg-accent text-muted-foreground hover:text-accent-foreground transition-colors'}`}
            aria-label="Previous date"
            disabled={isPrevDisabled}
          >
            <ArrowLeft className="h-4 w-4" />
          </button>
          {/* Use theme text color */}
          <h3 className="text-foreground text-xs font-medium flex items-center gap-1.5">
            <Calendar className="h-4 w-4 text-primary" />
            {/* Keep gradient for emphasis or use primary color */}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-500 to-pink-500 font-semibold">{monthYear}</span>
          </h3>
          <button
            onClick={() => navigateDates('next')}
            className={`p-1.5 rounded-lg ${isNextDisabled
              ? 'bg-muted text-muted-foreground/50 cursor-not-allowed'
              : 'bg-muted hover:bg-accent text-muted-foreground hover:text-accent-foreground transition-colors'}`}
            aria-label="Next date"
            disabled={isNextDisabled}
          >
            <ArrowRight className="h-4 w-4" />
          </button>
        </div>

        <div className="flex justify-center px-1"> {/* Centered days */}
          {daysToShow.map(day => {
            // Parse date string into date object
            const date = new Date(day.date);
            const dayName = date.toLocaleDateString('en-US', { weekday: 'short' }).substring(0, 1).toUpperCase();
            const dayNum = date.getDate();
            // const monthNum = date.getMonth() + 1; // Month is 0-indexed
            const isSelected = day.date === selectedDate;

            // Check if day has data
            const hasData = day.totalDuration > 0;

            // Check if it's today using local date format
            const today = formatDateToLocalYYYYMMDD(new Date());
            const isToday = day.date === today;

            // Calculate dynamic margin based on number of days
            const dynamicMargin = (() => {
              switch(daysToShow.length) {
                case 1: return 'mx-6';
                case 2: return 'mx-4';
                case 3: return 'mx-3';
                case 4: return 'mx-2';
                default: return 'mx-1';
              }
            })();

            return (
              <button
                key={day.date}
                onClick={() => onDateChange(day.date)}
                className={`group relative w-14 h-12 ${dynamicMargin} rounded-lg flex flex-col items-center justify-center transition-all ${
                  isSelected // Use primary color for selected
                    ? 'bg-primary text-primary-foreground shadow-lg scale-105 z-10'
                    : hasData
                      ? 'bg-muted text-foreground hover:bg-accent hover:scale-105'
                      : 'bg-muted text-muted-foreground/50 hover:bg-accent hover:text-muted-foreground'
                }`}
              >
                <span className="text-[8px] font-medium">{dayName}</span>
                <span className={`text-sm font-bold ${isToday && !isSelected ? 'text-primary' : ''}`}>{dayNum}</span>
                {hasData && !isSelected && (
                  <div className="absolute bottom-1 w-1 h-1 rounded-full bg-primary"></div>
                )}
                {isToday && !isSelected && (
                  <div className="absolute -top-1 right-1 w-1 h-1 rounded-full bg-pink-500"></div> // Keep pink for today marker?
                )}
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default DaySelector;
