import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { ThemeProvider } from '@mui/material';
import { <PERSON><PERSON><PERSON>, LineChart } from '@mui/x-charts';
import { <PERSON>U<PERSON>, <PERSON>Down, Calendar, Settings, Camera } from "lucide-react";
import { <PERSON><PERSON><PERSON>2, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> as Line<PERSON>hartIcon, TrendingUp } from 'lucide-react';
import { toast } from "@/components/ui/use-toast";
import html2canvas from 'html2canvas';
import StudyCalendar from '@/components/analytics/StudyCalendar';
import DaySelector from '@/components/analytics/DaySelector'; // We'll create this file next
import StudySessionsView from '@/components/analytics/StudySessionsView';
import { Button } from "@/components/ui/button";

// Custom styles for the animations
const customStyles = `
  @keyframes shine {
    0% {
      transform: translateX(-100%);
    }
    50%, 100% {
      transform: translateX(100%);
    }
  }

  .animate-shine {
    position: relative;
    overflow: hidden;
    animation: shine 4s infinite ease-in-out;
  }

  @keyframes pulse-subtle {
    0%, 100% {
      opacity: 0.7;
    }
    50% {
      opacity: 0.4;
    }
  }

  .animate-pulse-subtle {
    animation: pulse-subtle 3s infinite ease-in-out;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-5px);
    }
  }

  .animate-float {
    animation: float 5s infinite ease-in-out;
  }

  /* Prevent overflow in certain containers */
  .custom-scrollbar::-webkit-scrollbar {
    width: 5px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 10px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.15);
  }

  [data-theme="dark"] .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
  }

  [data-theme="dark"] .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
  }

  [data-theme="dark"] .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.15);
  }
`;

// Interfaces
interface DailySubjectOverview {
  subject: string;
  duration: number;
  percentageOfDay: number;
  date: string;
  color: string;
}

interface Analytics {
  dailyStats: {
    date: string;
    totalDuration: number;
    subjectDurations: { [key: string]: number };
    completedPomodoros: number;
  }[];
  weeklyStats: {
    weekNumber: number;
    year: number;
    totalDuration: number;
    subjectDurations: { [key: string]: number };
    completedPomodoros: number;
  }[];
  monthlyStats: {
    month: string;
    year: number;
    monthKey: string;
    totalDuration: number;
    subjectDurations: { [key: string]: number };
    completedPomodoros: number;
  }[];
  subjectStats: {
    subject: string;
    totalDuration: number;
    completedPomodoros: number;
    averageSessionDuration: number;
  }[];
}

interface FocusAnalytics {
  totalFocusTime: number;
  avgFocusPerDay: number;
  categoryDistribution: {
    subject: string;
    timeSpent: number;
    ratio: number;
    change: number;
    color: string;
  }[];
  dailyFocusTime: {
    [date: string]: number;
  };
  dailyFocusPercentChange: {
    [date: string]: number;
  };
  avgFocusPercentChange: number;
}

interface OverviewTabProps {
  analytics: Analytics;
  focusAnalytics: FocusAnalytics;
  selectedDate: string;
  setSelectedDate: (date: string) => void;
  formatDuration: (seconds: number) => string;
  subjectColorMap: { [subject: string]: string };
  targetHours: number;
  setTargetHours: (hours: number) => void;
  dailyMotivation: {
    quote: string;
    author: string;
  };
  setDailyMotivation: (motivation: {quote: string, author: string}) => void;
  streakInfo: {
    currentStreak: number;
    longestStreak: number;
    streakMap: { [date: string]: boolean };
  };
  handleDayClick: (date: Date, studyData: any) => void;
  theme: string;
  muiTheme: any;
  dailySessions?: {
    date: string;
    sessions: {
      id: string;
      subject: string;
      taskType: string;
      taskDescription: string;
      startTime: Date;
      endTime: Date;
      duration: number;
      subjectColor?: string;
    }[];
  }[];
  onEditSession?: (sessionId: string, updatedSession: Partial<{
    id: string;
    subject: string;
    taskType: string;
    taskDescription: string;
    startTime: Date;
    endTime: Date;
    duration: number;
    subjectColor?: string;
  }>) => Promise<boolean>;
  onDeleteSession?: (sessionId: string) => Promise<boolean>;
  subjects?: string[];
  taskTypes?: string[];
}

// Create a component for the daily subject overview
const DailyOverview = ({
  dailyStats,
  subjectStats,
  selectedDate,
  formatDuration,
  subjectColorMap
}: {
  dailyStats: Analytics['dailyStats'],
  subjectStats: Analytics['subjectStats'],
  selectedDate: string,
  onDateChange: (date: string) => void, // Keep prop for type consistency if needed elsewhere
  formatDuration: (seconds: number) => string,
  subjectColorMap: { [subject: string]: string }
}) => {
  // Find the selected day's data
  const selectedDayData = dailyStats.find(day => day.date === selectedDate) || dailyStats[dailyStats.length - 1];

  // If we have no data yet, show a message
  if (!selectedDayData) {
    return (
      <Card className="backdrop-blur-sm bg-card/60 border-border/40 shadow-lg h-full relative group overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-transparent rounded-xl opacity-70"></div>
        <CardContent className="pt-6 flex flex-col items-center justify-center h-full relative z-10">
          <div className="w-16 h-16 rounded-full bg-muted/60 flex items-center justify-center mb-4 shadow-sm backdrop-blur-sm">
            <Calendar className="w-8 h-8 text-muted-foreground/80" />
          </div>
          <p className="text-center text-muted-foreground font-medium">No study data available yet.</p>
        </CardContent>
      </Card>
    );
  }

  // Create overview data for each subject studied that day
  const subjectOverviews: DailySubjectOverview[] = Object.entries(selectedDayData.subjectDurations)
    .filter(([_, duration]) => duration > 0)
    .map(([subject, duration], index) => ({
      subject,
      duration,
      percentageOfDay: selectedDayData.totalDuration > 0 ? (duration / selectedDayData.totalDuration) * 100 : 0,
      date: selectedDayData.date,
      color: subjectColorMap[subject] || '#6366f1'
    }))
    .sort((a, b) => b.duration - a.duration);

  const captureScreenshot = async () => {
    try {
      const element = document.getElementById('daily-overview-card');
      if (!element) return;

      // Capture the element as it is rendered, respecting theme styles
      // Ensure styles are fully applied before capturing
      await new Promise(resolve => setTimeout(resolve, 100)); // Small delay for styles

      const canvas = await html2canvas(element, {
          useCORS: true, // Allow loading cross-origin images if any
          // Let html2canvas determine the background from the element's computed style
          background: window.getComputedStyle(element).backgroundColor
      });

      const dataUrl = canvas.toDataURL('image/png');

      // Create a temporary link and trigger download
      const link = document.createElement('a');
      link.href = dataUrl;
      link.download = `daily-overview-${selectedDate}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "Screenshot captured!",
        description: "Your daily overview has been saved.",
      });
    } catch (error) {
      console.error("Error capturing screenshot:", error);
      toast({
        title: "Screenshot failed",
        description: "There was an error capturing the screenshot.",
        variant: "destructive"
      });
    }
  };

  return (
    <Card id="daily-overview-card" className="backdrop-blur-sm bg-card/60 border-border/40 shadow-lg h-full overflow-hidden relative group">
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-transparent rounded-xl opacity-70"></div>
      <CardHeader className="border-b border-border/40 pb-4 relative z-10">
        <div className="flex justify-between items-center">
          <CardTitle className="text-xl font-medium flex items-center gap-2">
            <div className="w-7 h-7 rounded-lg bg-primary/10 flex items-center justify-center">
              <Calendar className="h-4 w-4 text-primary" />
            </div>
            <span>Daily Subject Overview</span>
          </CardTitle>
          <button
            onClick={captureScreenshot}
            className="p-1.5 rounded-lg bg-muted/80 hover:bg-accent/80 text-muted-foreground hover:text-accent-foreground transition-colors backdrop-blur-sm"
            aria-label="Capture screenshot"
            title="Capture screenshot"
          >
            <Camera className="h-4 w-4" />
          </button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6 pt-6 text-card-foreground relative z-10">
        {/* Main stats in one row */}
        <div className="grid grid-cols-3 gap-4">
          <div className="backdrop-blur-md bg-primary/5 rounded-2xl p-4 border border-primary/10 shadow-sm relative group overflow-hidden">
            <div className="absolute -inset-1 bg-gradient-to-br from-indigo-400/20 via-transparent to-transparent rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1 flex items-center gap-1.5">
              <span className="w-1.5 h-1.5 rounded-full bg-indigo-400"></span>
              Study Time
            </h3>
            <p className="text-lg sm:text-xl lg:text-2xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-indigo-600">
              {formatDuration(selectedDayData.totalDuration)}
            </p>
          </div>
          <div className="backdrop-blur-md bg-pink-500/5 rounded-2xl p-4 border border-pink-500/10 shadow-sm relative group overflow-hidden">
            <div className="absolute -inset-1 bg-gradient-to-br from-pink-500/20 via-transparent to-transparent rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1 flex items-center gap-1.5">
              <span className="w-1.5 h-1.5 rounded-full bg-pink-500"></span>
              Pomodoros
            </h3>
            <p className="text-lg sm:text-xl lg:text-2xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-pink-500 to-pink-600">
              {selectedDayData.completedPomodoros}
            </p>
          </div>
          <div className="backdrop-blur-md bg-cyan-500/5 rounded-2xl p-4 border border-cyan-500/10 shadow-sm relative group overflow-hidden">
            <div className="absolute -inset-1 bg-gradient-to-br from-cyan-500/20 via-transparent to-transparent rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1 flex items-center gap-1.5">
              <span className="w-1.5 h-1.5 rounded-full bg-cyan-500"></span>
              Subjects
            </h3>
            <p className="text-lg sm:text-xl lg:text-2xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-cyan-500 to-cyan-600">
              {subjectOverviews.length}
            </p>
          </div>
        </div>

        {/* Subjects list in separate rows */}
        {subjectOverviews.length > 0 ? (
          <div className="grid grid-cols-1 gap-3 max-h-[300px] overflow-y-auto pr-2 custom-scrollbar">
            {subjectOverviews.map((subject) => (
              <div
                key={subject.subject}
                className="flex items-center p-3.5 backdrop-blur-sm bg-muted/30 hover:bg-accent/30 transition-all duration-300 rounded-xl border border-border/30 shadow-sm relative group overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-shine pointer-events-none"></div>
                <div
                  className="w-9 h-9 rounded-xl mr-4 flex items-center justify-center shadow-sm"
                  style={{ backgroundColor: `${subject.color}10` }}
                >
                  <div
                    className="w-3.5 h-3.5 rounded-md"
                    style={{ backgroundColor: subject.color }}
                  />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-foreground text-sm">{subject.subject}</h4>
                  <div className="mt-1.5 h-1.5 w-full bg-muted/60 rounded-full overflow-hidden shadow-inner relative">
                    <div
                      className="h-full rounded-full transition-all duration-500"
                      style={{
                        width: `${subject.percentageOfDay}%`,
                        backgroundColor: subject.color,
                        boxShadow: `0 0 6px 0 ${subject.color}60`
                      }}
                    >
                      {/* Animated shine effect */}
                      <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 animate-shine"></div>
                    </div>
                  </div>
                </div>
                <div className="text-right ml-4">
                  <p className="text-sm font-medium text-foreground">{formatDuration(subject.duration)}</p>
                  <div className="flex items-center justify-end mt-0.5">
                    <div className="px-1.5 py-0.5 rounded-full text-xs bg-muted/60 border border-muted-foreground/5">
                      <span className="font-medium" style={{ color: subject.color }}>
                        {subject.percentageOfDay.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-10 backdrop-blur-sm bg-muted/20 rounded-xl border border-border/30">
            <div className="w-14 h-14 rounded-full bg-muted/40 flex items-center justify-center mb-3 shadow-sm backdrop-blur-sm">
              <Calendar className="w-7 h-7 text-muted-foreground/70" />
            </div>
            <p className="text-center text-muted-foreground font-medium">No subjects studied on this day.</p>
            <p className="text-center text-xs text-muted-foreground/70 mt-1">Select another day to view data</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

const OverviewTab: React.FC<OverviewTabProps> = ({
  analytics,
  focusAnalytics,
  selectedDate,
  setSelectedDate,
  formatDuration,
  subjectColorMap,
  targetHours,
  setTargetHours,
  dailyMotivation,
  setDailyMotivation,
  streakInfo,
  handleDayClick,
  theme,
  muiTheme,
  dailySessions = [],
  onEditSession,
  onDeleteSession,
  subjects,
  taskTypes
}) => {
  // Add local state for the input field
  const [localTargetHours, setLocalTargetHours] = useState<number>(targetHours);
  // Add state for edit mode
  const [isEditingTarget, setIsEditingTarget] = useState<boolean>(false);

  // Add local state for daily motivation
  const [localMotivation, setLocalMotivation] = useState<{quote: string, author: string}>(dailyMotivation);
  // Add state for editing motivation
  const [isEditingMotivation, setIsEditingMotivation] = useState<boolean>(false);

  // Update local state when props change
  useEffect(() => {
    setLocalTargetHours(targetHours);
    setLocalMotivation(dailyMotivation);
  }, [targetHours, dailyMotivation]);

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: customStyles }} />
      <div className="flex-1">
        {analytics && focusAnalytics && (
          <DaySelector
            selectedDate={selectedDate}
            onDateChange={setSelectedDate}
            dailyStats={analytics.dailyStats}
          />
        )}
      </div>

      <div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Top stat cards with modern design */}
          <Card className="backdrop-blur-sm bg-card/60 border-border/40 shadow-lg overflow-hidden group hover:shadow-xl transition-shadow duration-300">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-transparent rounded-xl opacity-70"></div>
            <CardHeader className="border-b border-border/40 pb-3">
              <CardTitle className="text-base font-medium flex items-center gap-2">
                <div className="w-6 h-6 rounded-lg bg-primary/10 flex items-center justify-center">
                  <BarChart2 className="h-3.5 w-3.5 text-primary" />
                </div>
                <span>Avg. Focus This Month</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="flex items-center justify-center p-6">
              <div className="text-2xl sm:text-3xl md:text-4xl font-bold text-primary text-center relative">
                <div className="absolute -inset-6 bg-primary/5 rounded-full blur-xl opacity-0 group-hover:opacity-70 transition-opacity duration-500"></div>
                <span className="relative">{formatDuration(focusAnalytics?.avgFocusPerDay || 0)}</span>
              </div>
            </CardContent>
          </Card>
          <Card className="backdrop-blur-sm bg-card/60 border-border/40 shadow-lg overflow-hidden group hover:shadow-xl transition-shadow duration-300">
            <div className="absolute inset-0 bg-gradient-to-br from-pink-500/5 via-transparent to-transparent rounded-xl opacity-70"></div>
            <CardHeader className="border-b border-border/40 pb-3">
              <CardTitle className="text-base font-medium flex items-center gap-2">
                <div className="w-6 h-6 rounded-lg bg-pink-500/10 flex items-center justify-center">
                  <LineChartIcon className="h-3.5 w-3.5 text-pink-500" />
                </div>
                <span>Completed Pomodoros</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="flex items-center justify-center p-6">
              <div className="text-2xl sm:text-3xl md:text-4xl font-bold text-pink-500 text-center relative">
                <div className="absolute -inset-6 bg-pink-500/5 rounded-full blur-xl opacity-0 group-hover:opacity-70 transition-opacity duration-500"></div>
                <span className="relative">{analytics?.dailyStats.reduce((acc, stat) => acc + stat.completedPomodoros, 0) || 0}</span>
              </div>
            </CardContent>
          </Card>
          <Card className="backdrop-blur-sm bg-card/60 border-border/40 shadow-lg overflow-hidden group hover:shadow-xl transition-shadow duration-300">
            <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/5 via-transparent to-transparent rounded-xl opacity-70"></div>
            <CardHeader className="border-b border-border/40 pb-3">
              <CardTitle className="text-base font-medium flex items-center gap-2">
                <div className="w-6 h-6 rounded-lg bg-cyan-500/10 flex items-center justify-center">
                  <ChartPie className="h-3.5 w-3.5 text-cyan-500" />
                </div>
                <span>Subjects Studied Today</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="flex items-center justify-center p-6">
              <div className="text-2xl sm:text-3xl md:text-4xl font-bold text-cyan-500 text-center relative">
                <div className="absolute -inset-6 bg-cyan-500/5 rounded-full blur-xl opacity-0 group-hover:opacity-70 transition-opacity duration-500"></div>
                <span className="relative">
                  {
                    selectedDate && analytics?.dailyStats.length
                      ? Object.keys(analytics.dailyStats.find(day => day.date === selectedDate)?.subjectDurations || {})
                          .filter(subject => (analytics.dailyStats.find(day => day.date === selectedDate)?.subjectDurations[subject] || 0) > 0)
                          .length
                      : 0
                  }
                </span>
              </div>
            </CardContent>
          </Card>
          <Card className="backdrop-blur-sm bg-card/60 border-border/40 shadow-lg overflow-hidden group hover:shadow-xl transition-shadow duration-300">
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-400/5 via-transparent to-transparent rounded-xl opacity-70"></div>
            <CardHeader className="border-b border-border/40 pb-3">
              <CardTitle className="text-base font-medium flex items-center gap-2">
                <div className="w-6 h-6 rounded-lg bg-emerald-400/10 flex items-center justify-center">
                  <TrendingUp className="h-3.5 w-3.5 text-emerald-400" />
                </div>
                <span>Study Streak</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-6 space-y-2">
               {/* Display Current Streak */}
               <div className="flex justify-between items-baseline">
                 <span className="text-sm text-muted-foreground">Current</span>
                 <div className="text-xl sm:text-2xl md:text-3xl font-bold text-emerald-400 relative">
                   <div className="absolute -inset-4 bg-emerald-400/5 rounded-full blur-lg opacity-0 group-hover:opacity-70 transition-opacity duration-500"></div>
                   <span className="relative">{streakInfo.currentStreak} day{streakInfo.currentStreak !== 1 ? 's' : ''}</span>
                 </div>
               </div>
               {/* Display Longest Streak */}
               <div className="flex justify-between items-baseline border-t border-border/30 pt-2">
                 <span className="text-sm text-muted-foreground">Longest</span>
                 <div className="text-xl sm:text-2xl md:text-3xl font-bold text-emerald-400">
                   {streakInfo.longestStreak} day{streakInfo.longestStreak !== 1 ? 's' : ''}
                 </div>
               </div>
            </CardContent>
          </Card>
        </div>

        {/* Focus metrics */}
        {focusAnalytics && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <Card className="backdrop-blur-sm bg-card/60 border-border/40 shadow-lg overflow-hidden relative group hover:shadow-xl transition-shadow duration-300">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-transparent rounded-xl opacity-70"></div>
                <CardHeader className="border-b border-border/40 pb-3 relative z-10">
                  <CardTitle className="text-lg font-medium flex items-center gap-2">
                    <div className="w-6 h-6 rounded-lg bg-primary/10 flex items-center justify-center">
                      <BarChart2 className="h-3.5 w-3.5 text-primary" />
                    </div>
                    <span>Total Focus (Today)</span>
                    {selectedDate && (focusAnalytics?.dailyFocusPercentChange[selectedDate] || 0) !== 0 && (
                      <span className={`ml-auto text-sm font-normal flex items-center px-2 py-0.5 rounded-full ${
                        (focusAnalytics?.dailyFocusPercentChange[selectedDate] || 0) >= 0
                          ? 'bg-emerald-500/10 text-emerald-500 border border-emerald-500/20'
                          : 'bg-red-500/10 text-red-500 border border-red-500/20'
                      }`}>
                        {Math.abs(Math.round(focusAnalytics?.dailyFocusPercentChange[selectedDate] || 0))}%
                        {(focusAnalytics?.dailyFocusPercentChange[selectedDate] || 0) >= 0 ? (
                          <ArrowUp className="ml-1 h-3 w-3" />
                        ) : (
                          <ArrowDown className="ml-1 h-3 w-3" />
                        )}
                        <span className="ml-1 text-xs hidden sm:inline">vs yesterday</span>
                      </span>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6 sm:p-8 relative z-10">
                  <div className="flex flex-col items-center justify-center space-y-8 w-full">
                    {/* Beautiful time display with subtle glow effect */}
                    <div className="relative">
                      <div className="absolute -inset-1 bg-gradient-to-r from-primary/10 to-pink-500/10 rounded-3xl blur-xl opacity-50 group-hover:opacity-80 transition-opacity duration-700"></div>
                      <div className="relative bg-card/40 backdrop-blur-md rounded-2xl px-8 py-6 shadow-lg border border-primary/5 flex flex-col items-center">
                        <div className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-pink-500">
                          {formatDuration(
                            selectedDate && analytics?.dailyStats.length
                              ? (analytics.dailyStats.find(day => day.date === selectedDate)?.totalDuration || 0)
                              : 0
                          )}
                        </div>
                        <div className="mt-2 text-xs text-muted-foreground font-medium uppercase tracking-wider">Total focus time</div>
                      </div>
                    </div>

                    {/* Enhanced progress container */}
                    <div className="w-full space-y-3">
                      <div className="flex justify-between items-center px-1">
                        <span className="text-sm font-medium text-foreground flex items-center gap-2">
                          <span className="w-2 h-2 inline-block rounded-full bg-gradient-to-r from-indigo-500 to-pink-500"></span>
                          Progress toward daily target
                        </span>
                        <span className="text-sm font-semibold">
                          {Math.min(100, Math.round(((focusAnalytics.dailyFocusTime[selectedDate] || 0) / (targetHours * 3600)) * 100))}%
                        </span>
                      </div>

                      {/* Enhanced progress bar with subtle glow effect */}
                      <div className="w-full h-2.5 bg-muted/30 rounded-full overflow-hidden relative p-0.5 backdrop-blur-sm border border-muted/20">
                        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-pink-500/5 animate-pulse"></div>
                        {/* Base progress bar with gradient */}
                        <div
                          className="h-full rounded-full transition-all duration-700 ease-out bg-gradient-to-r from-indigo-500 to-pink-500 shadow-[0_0_6px_rgba(139,92,246,0.3)]"
                          style={{
                            width: `${Math.min(100, ((focusAnalytics.dailyFocusTime[selectedDate] || 0) / (targetHours * 3600)) * 100)}%`
                          }}
                        >
                          {/* Animated gradient shine effect */}
                          <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 animate-shine"></div>
                        </div>

                        {/* Golden overlay with enhanced glow for exceeding target */}
                        {((focusAnalytics.dailyFocusTime[selectedDate] || 0) > (targetHours * 3600)) && (
                          <div
                            className="h-full rounded-full transition-all duration-700 ease-out bg-gradient-to-r from-yellow-300 to-amber-500 absolute top-0 left-0 shadow-[0_0_8px_rgba(251,191,36,0.5)]"
                            style={{
                              width: `${Math.min(100, (((focusAnalytics.dailyFocusTime[selectedDate] || 0) - (targetHours * 3600)) / (targetHours * 3600)) * 100)}%`
                            }}
                          >
                            <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 animate-shine"></div>
                          </div>
                        )}
                      </div>

                      {/* Enhanced stats display */}
                      <div className="flex justify-between items-center mt-2 px-1">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-foreground">
                            {formatDuration(focusAnalytics.dailyFocusTime[selectedDate] || 0)}
                          </span>
                          <span className="text-xs text-muted-foreground">of {targetHours}h target</span>
                        </div>

                        {/* Enhanced exceeding target indicator */}
                        {((focusAnalytics.dailyFocusTime[selectedDate] || 0) > (targetHours * 3600)) && (
                          <div className="flex items-center gap-2">
                            <div className="px-2 py-0.5 bg-gradient-to-r from-yellow-500/10 to-amber-500/10 border border-amber-500/20 rounded-full">
                              <span className="text-xs font-medium text-amber-500 flex items-center">
                                <span className="mr-1">+</span>
                                {formatDuration((focusAnalytics.dailyFocusTime[selectedDate] || 0) - (targetHours * 3600))}
                                <span className="ml-1.5 w-1.5 h-1.5 rounded-full bg-amber-500 animate-pulse"></span>
                              </span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              {analytics && (
                <div className="lg:col-span-1 h-full">
                  <DailyOverview
                    dailyStats={analytics.dailyStats}
                    subjectStats={analytics.subjectStats}
                    selectedDate={selectedDate}
                    onDateChange={setSelectedDate} // Pass prop down
                    formatDuration={formatDuration}
                    subjectColorMap={subjectColorMap}
                  />
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              {/* Target Hours Card */}
              <Card className="backdrop-blur-sm bg-card/60 border-border/40 shadow-lg overflow-hidden group hover:shadow-xl transition-shadow duration-300">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-transparent rounded-xl opacity-70"></div>
                <CardHeader className="border-b border-border/40 pb-3 relative z-10">
                  <CardTitle className="text-lg font-medium flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 rounded-lg bg-primary/10 flex items-center justify-center">
                        <Settings className="h-3.5 w-3.5 text-primary" />
                      </div>
                      <span>Daily Target</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-xs text-muted-foreground font-normal">Set your daily focus goal</div>
                      <button
                        onClick={() => setIsEditingTarget(!isEditingTarget)}
                        className="p-1.5 rounded-lg bg-muted/80 hover:bg-accent/80 text-muted-foreground hover:text-accent-foreground transition-colors backdrop-blur-sm"
                        aria-label={isEditingTarget ? "Cancel editing" : "Edit target"}
                        title={isEditingTarget ? "Cancel editing" : "Edit target"}
                      >
                        {isEditingTarget ? (
                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-x">
                            <path d="M18 6 6 18"/>
                            <path d="m6 6 12 12"/>
                          </svg>
                        ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-pencil">
                            <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/>
                            <path d="m15 5 4 4"/>
                          </svg>
                        )}
                      </button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-6 relative z-10">
                  <div className="flex flex-col space-y-6 mb-6">
                    <div className="backdrop-blur-sm bg-muted/20 rounded-2xl p-5 border border-border/30 shadow-sm">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex flex-col">
                          <span className="text-sm font-medium text-foreground mb-1">Your Target</span>
                          <span className="text-xs text-muted-foreground">Hours per day</span>
                        </div>
                        <div className="flex items-center gap-2">
                          {isEditingTarget ? (
                            <>
                              <div className="relative flex items-center">
                                <button
                                  className="w-7 h-7 rounded-l-md bg-muted/70 hover:bg-accent/70 border border-r-0 border-border/40 flex items-center justify-center backdrop-blur-sm"
                                  onClick={() => {
                                    if (localTargetHours > 1) {
                                      setLocalTargetHours(localTargetHours - 1);
                                    }
                                  }}
                                >
                                  <span className="text-foreground font-bold">-</span>
                                </button>
                                <input
                                  type="number"
                                  min="1"
                                  max="24"
                                  value={localTargetHours}
                                  onChange={(e) => {
                                    const value = Math.min(24, Math.max(1, parseInt(e.target.value) || 1));
                                    setLocalTargetHours(value);
                                  }}
                                  className="w-16 h-7 px-3 py-0 bg-input/70 backdrop-blur-sm border-y border-border/40 text-foreground focus:outline-none focus:ring-1 focus:ring-ring text-center"
                                  aria-label="Target hours"
                                />
                                <button
                                  className="w-7 h-7 rounded-r-md bg-muted/70 hover:bg-accent/70 border border-l-0 border-border/40 flex items-center justify-center backdrop-blur-sm"
                                  onClick={() => {
                                    if (localTargetHours < 24) {
                                      setLocalTargetHours(localTargetHours + 1);
                                    }
                                  }}
                                >
                                  <span className="text-foreground font-bold">+</span>
                                </button>
                              </div>
                              <Button
                                size="sm"
                                onClick={() => {
                                  setTargetHours(localTargetHours);
                                  setIsEditingTarget(false);
                                  toast({
                                    title: "Target hours updated",
                                    description: `Your daily target has been set to ${localTargetHours} hours`,
                                  });
                                }}
                                className="bg-gradient-to-r from-indigo-500 to-pink-500 hover:from-indigo-600 hover:to-pink-600 text-white font-medium shadow-sm hover:shadow-md transition-all duration-200 border-none"
                              >
                                Save
                              </Button>
                            </>
                          ) : (
                            <span className="text-xl font-bold text-primary">{localTargetHours}h</span>
                          )}
                        </div>
                      </div>

                      <div className="mt-2 flex items-center justify-center">
                        <div className="flex items-center justify-center w-32 h-32 rounded-full bg-muted/20 border-4 border-muted/30 relative backdrop-blur-sm group-hover:border-muted/50 transition-colors duration-300">
                          <div
                            className="absolute inset-0 rounded-full overflow-hidden"
                            style={{
                              clipPath: 'circle(50% at center)',
                              background: `conic-gradient(
                                ${(focusAnalytics.dailyFocusTime[selectedDate] || 0) > (targetHours * 3600)
                                  ? 'from 0deg, #f59e0b 0deg, rgba(139, 92, 246, 0.8) '
                                  : 'from 0deg, rgba(139, 92, 246, 0.8) '
                                }
                                ${Math.min(360, ((focusAnalytics.dailyFocusTime[selectedDate] || 0) / (targetHours * 3600)) * 360)}deg,
                                transparent ${Math.min(360, ((focusAnalytics.dailyFocusTime[selectedDate] || 0) / (targetHours * 3600)) * 360)}deg,
                                transparent 360deg
                              )`
                            }}
                          />
                          <div className="w-24 h-24 rounded-full bg-card/80 backdrop-blur-md flex flex-col items-center justify-center z-10 border border-border/20 shadow-sm">
                            <span className="text-2xl font-bold text-primary">
                              {Math.round(Math.min(100, ((focusAnalytics.dailyFocusTime[selectedDate] || 0) / (targetHours * 3600)) * 100))}%
                            </span>
                            <span className="text-xs text-muted-foreground mt-1">Completed</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <div className="text-sm font-medium text-foreground">Today's Progress</div>
                      <div className="text-sm font-medium flex items-center gap-2">
                        {((focusAnalytics.dailyFocusTime[selectedDate] || 0) > (targetHours * 3600)) && (
                          <span className="text-xs px-2 py-0.5 bg-amber-500/10 text-amber-500 font-medium rounded-full border border-amber-500/20">Target Exceeded!</span>
                        )}
                      </div>
                    </div>
                    <div className="h-2.5 bg-muted/30 rounded-full overflow-hidden relative backdrop-blur-sm border border-border/20">
                      {/* Base progress bar with original gradient */}
                      <div
                        className="h-full rounded-full transition-all duration-500 bg-gradient-to-r from-indigo-500 to-pink-500"
                        style={{
                          width: `${Math.min(100, ((focusAnalytics.dailyFocusTime[selectedDate] || 0) / (targetHours * 3600)) * 100)}%`
                        }}
                      >
                        {/* Animated shine effect */}
                        <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 animate-shine"></div>
                      </div>

                      {/* Golden overlay for the portion that exceeds the target */}
                      {((focusAnalytics.dailyFocusTime[selectedDate] || 0) > (targetHours * 3600)) && (
                        <div
                          className="h-full rounded-full transition-all duration-500 bg-gradient-to-r from-yellow-300 to-amber-500 absolute top-0 left-0 shadow-[0_0_8px_rgba(251,191,36,0.4)]"
                          style={{
                            width: `${Math.min(100, (((focusAnalytics.dailyFocusTime[selectedDate] || 0) - (targetHours * 3600)) / (targetHours * 3600)) * 100)}%`
                          }}
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 animate-shine"></div>
                        </div>
                      )}
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground flex items-center gap-1.5">
                        <span className="inline-block w-1.5 h-1.5 rounded-full bg-indigo-500"></span>
                        {formatDuration(focusAnalytics.dailyFocusTime[selectedDate] || 0)}
                      </span>
                      {((focusAnalytics.dailyFocusTime[selectedDate] || 0) > (targetHours * 3600)) ? (
                        <span className="text-amber-500 font-medium flex items-center gap-1.5">
                          <span className="inline-block w-1.5 h-1.5 rounded-full bg-amber-500 animate-pulse"></span>
                          +{formatDuration((focusAnalytics.dailyFocusTime[selectedDate] || 0) - (targetHours * 3600))}
                        </span>
                      ) : (
                        <span className="text-muted-foreground flex items-center gap-1.5">
                          <span className="inline-block w-1.5 h-1.5 rounded-full bg-pink-500"></span>
                          {targetHours}h target
                        </span>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Daily Motivation Card */}
              <Card className="backdrop-blur-sm bg-card/60 border-border/40 shadow-lg overflow-hidden h-full relative group hover:shadow-xl transition-shadow duration-300">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-transparent rounded-xl opacity-70"></div>
                <CardHeader className="border-b border-border/40 pb-3 relative z-10">
                  <CardTitle className="text-lg font-medium flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 rounded-lg bg-primary/10 flex items-center justify-center">
                        <Calendar className="h-3.5 w-3.5 text-primary" />
                      </div>
                      <span>Daily Motivation</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-xs text-muted-foreground font-normal italic">For your study journey</div>
                      <button
                        onClick={() => setIsEditingMotivation(!isEditingMotivation)}
                        className="p-1.5 rounded-lg bg-muted/80 hover:bg-accent/80 text-muted-foreground hover:text-accent-foreground transition-colors backdrop-blur-sm"
                        aria-label={isEditingMotivation ? "Cancel editing" : "Edit motivation"}
                        title={isEditingMotivation ? "Cancel editing" : "Edit motivation"}
                      >
                        {isEditingMotivation ? (
                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-x">
                            <path d="M18 6 6 18"/>
                            <path d="m6 6 12 12"/>
                          </svg>
                        ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-pencil">
                            <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/>
                            <path d="m15 5 4 4"/>
                          </svg>
                        )}
                      </button>
                    </div>
                  </CardTitle>
                </CardHeader>

                <CardContent className="flex flex-col justify-center items-center h-[calc(100%-88px)] p-6 text-center space-y-8 relative z-10">
                  {!isEditingMotivation ? (
                    <>
                      <div className="w-20 h-20 rounded-full bg-primary/5 backdrop-blur-md p-1 shadow-sm relative group-hover:bg-primary/10 transition-colors duration-300">
                        <div className="absolute -inset-1 bg-gradient-to-br from-primary/10 to-pink-500/10 rounded-full blur-lg opacity-0 group-hover:opacity-70 transition-opacity duration-500"></div>
                        <div className="w-full h-full rounded-full bg-background/40 backdrop-blur-md flex items-center justify-center border border-primary/10">
                          <Calendar className="w-8 h-8 text-primary" />
                        </div>
                      </div>

                      <div className="max-w-md mx-auto relative">
                        <div className="absolute -left-3 -top-3 text-5xl text-primary/10 font-serif">"</div>
                        <div className="absolute -right-3 -bottom-3 text-5xl text-primary/10 font-serif">"</div>
                        <p className="text-xl sm:text-2xl font-serif italic leading-relaxed text-foreground relative">
                          {localMotivation.quote}
                        </p>
                      </div>

                      <div className="flex flex-col items-center space-y-4">
                        <div className="w-12 h-0.5 bg-primary/20 rounded-full"></div>
                        <div className="text-base text-foreground/80 font-medium tracking-wide">
                          — {localMotivation.author}
                        </div>
                        <div className="w-24 h-0.5 bg-primary/20 rounded-full"></div>
                      </div>

                      <div className="absolute right-6 bottom-6 opacity-5 group-hover:opacity-10 transition-opacity duration-300">
                        <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z"></path>
                          <path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z"></path>
                        </svg>
                      </div>
                    </>
                  ) : (
                    <div className="w-full max-w-md space-y-6">
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-foreground">Quote</label>
                        <textarea
                          value={localMotivation.quote}
                          onChange={(e) => setLocalMotivation({...localMotivation, quote: e.target.value})}
                          className="w-full h-32 px-3 py-2 bg-input/70 backdrop-blur-sm border border-border/40 rounded-md text-foreground focus:outline-none focus:ring-1 focus:ring-ring shadow-sm"
                          placeholder="Enter your motivational quote"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium text-foreground">Author</label>
                        <input
                          type="text"
                          value={localMotivation.author}
                          onChange={(e) => setLocalMotivation({...localMotivation, author: e.target.value})}
                          className="w-full px-3 py-2 bg-input/70 backdrop-blur-sm border border-border/40 rounded-md text-foreground focus:outline-none focus:ring-1 focus:ring-ring shadow-sm"
                          placeholder="Enter the author's name"
                        />
                      </div>

                      <Button
                        onClick={async () => {
                          try {
                            await setDailyMotivation(localMotivation);
                            setIsEditingMotivation(false);
                            toast({
                              title: "Motivation updated",
                              description: "Your daily motivation has been updated successfully",
                            });
                          } catch (error) {
                            console.error('Error saving motivation:', error);
                            toast({
                              title: "Error",
                              description: "Failed to update motivation. Please try again.",
                              variant: "destructive",
                            });
                          }
                        }}
                        className="w-full bg-gradient-to-r from-indigo-500 to-pink-500 hover:from-indigo-600 hover:to-pink-600 text-white font-medium shadow-sm hover:shadow-md transition-all duration-200 border-none"
                      >
                        Save Motivation
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Insert Study Calendar Here */}
              {analytics && (
                <StudyCalendar
                  dailyStats={analytics.dailyStats}
                  formatDuration={formatDuration}
                  onDayClick={handleDayClick} // Pass the click handler
                  streakData={streakInfo.streakMap} // Pass streak map
                  dailyTargetSeconds={targetHours * 3600} // Pass daily target in seconds
                  subjectColorMap={subjectColorMap} // Pass color map
                />
              )}
            </div>
          </>
        )}

        {/* Add Study Sessions View */}
        <div className="mb-8">
          <StudySessionsView
            dailySessions={dailySessions}
            selectedDate={selectedDate}
            formatDuration={formatDuration}
            title="What You Studied"
            onEditSession={onEditSession}
            onDeleteSession={onDeleteSession}
            subjects={subjects}
            taskTypes={taskTypes}
          />
        </div>
      </div>
    </>
  );
};

export default OverviewTab;
