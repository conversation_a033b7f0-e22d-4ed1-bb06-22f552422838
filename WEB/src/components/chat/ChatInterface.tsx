import { useEffect, useRef, useState } from 'react'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { useUserStore } from '@/stores/userStore'
import { useChatStore } from '@/stores/chatStore'
import { subscribeToGroupMessages, sendMessage } from '@/utils/db'
import { Send } from 'lucide-react'
import { format } from 'date-fns'

export function ChatInterface() {
  const [newMessage, setNewMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const user = useUserStore((state) => state.user)
  const { activeGroupId, messages, setMessages } = useChatStore()
  const activeGroupMessages = activeGroupId ? messages[activeGroupId] || [] : []

  useEffect(() => {
    if (activeGroupId) {
      const unsubscribe = subscribeToGroupMessages(activeGroupId, (newMessages) => {
        setMessages(activeGroupId, newMessages)
      })
      return () => unsubscribe()
    }
  }, [activeGroupId, setMessages])

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [activeGroupMessages])

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !activeGroupId || !newMessage.trim() || isLoading) return

    setIsLoading(true)
    try {
      await sendMessage({
        content: newMessage.trim(),
        senderId: user.uid,
        timestamp: Date.now(),
        groupId: activeGroupId
      })
      setNewMessage('')
    } catch (error) {
      console.error('Error sending message:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (!activeGroupId) {
    return (
      <div className="flex-1 flex items-center justify-center text-muted-foreground">
        Select a group to start chatting
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col h-full">
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {activeGroupMessages.map((message, index) => {
            const isCurrentUser = message.senderId === user?.uid
            return (
              <div
                key={message.id}
                className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[70%] rounded-lg p-3 ${
                    isCurrentUser
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted'
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <p className="text-xs opacity-70 mt-1">
                    {format(message.timestamp, 'HH:mm')}
                  </p>
                </div>
              </div>
            )
          })}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      <form
        onSubmit={handleSendMessage}
        className="border-t p-4 flex gap-2"
      >
        <Input
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          placeholder="Type a message..."
          disabled={isLoading}
        />
        <Button type="submit" size="icon" disabled={isLoading}>
          <Send className="h-4 w-4" />
        </Button>
      </form>
    </div>
  )
} 