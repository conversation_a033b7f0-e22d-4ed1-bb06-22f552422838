import { useEffect, useState } from 'react'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useChatStore } from '@/stores/chatStore'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { User, Crown, MoreVertical, Globe, Lock, MessageSquare, Share2, Users, Calendar, Clock, Flame, Settings2, Copy, Trash2, LogOut, UserCircle, CalendarClock } from 'lucide-react'
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { Switch } from '@/components/ui/switch'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { UserProfileDialog } from "@/components/profile/UserProfileDialog"
import { Link } from "react-router-dom"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Input } from "@/components/ui/input"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  getUserProfile,
  getStudySessions,
  updateGroup,
  deleteGroup,
  leaveGroup,
  calculateUserStudyTime,
  calculateUserStudyStreak,
  getGroupStats,
  GroupStats
} from '@/utils/supabase'

interface UserProfile {
  id: string
  displayName: string
  username: string
  photoURL?: string
  email: string
  createdAt: string
  joinedAt?: string
  stats?: {
    totalStudyTime: number
    studyStreak: number
  }
}

interface GroupDetailsProps {
  groupId: string;
}

export function GroupDetails({ groupId }: GroupDetailsProps) {
  const [members, setMembers] = useState<UserProfile[]>([])
  const [groupStats, setGroupStats] = useState<GroupStats | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingStats, setIsLoadingStats] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isLeaving, setIsLeaving] = useState(false)
  const [removingMemberId, setRemovingMemberId] = useState<string | null>(null)
  const [isEditingDescription, setIsEditingDescription] = useState(false)
  const [description, setDescription] = useState('')
  const [isSavingDescription, setIsSavingDescription] = useState(false)
  const [isUpdatingPrivacy, setIsUpdatingPrivacy] = useState(false)
  const { groups, removeGroup } = useChatStore()
  const { user } = useSupabaseAuth()
  const { toast } = useToast()
  
  const activeGroup = groups.find(g => g.id === groupId)

  // Function to update group privacy
  const updateGroupPrivacy = async (isPublic: boolean) => {
    if (!groupId || !activeGroup || activeGroup.owner_id !== user?.id) return

    setIsUpdatingPrivacy(true)
    try {
      // Update the group privacy setting in Supabase
      await updateGroup(groupId, {
        isPublic: isPublic
      })

      toast({
        title: isPublic ? "Group is now public" : "Group is now private",
        description: isPublic
          ? "Anyone can discover and join this group"
          : "Only people with the invite code can join this group"
      })
    } catch (error) {
      console.error('Error updating group privacy:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update privacy settings"
      })
    } finally {
      setIsUpdatingPrivacy(false)
    }
  }

  useEffect(() => {
    const loadMembers = async () => {
      if (!groupId) return

      setIsLoading(true)
      try {
        const memberProfiles = await Promise.all(
          activeGroup?.members?.map(async (memberId) => {
            const userProfile = await getUserProfile(memberId)
            if (userProfile) {
              // Calculate user stats using Supabase functions
              const totalStudyTime = await calculateUserStudyTime(memberId)
              const studyStreak = await calculateUserStudyStreak(memberId)

              // Add joinedAt timestamp - for now use created_at as fallback
              const joinedAt = userProfile.created_at || new Date().toISOString();

              return {
                id: userProfile.id,
                displayName: userProfile.display_name || userProfile.displayName || 'Anonymous',
                username: userProfile.username || '',
                photoURL: userProfile.photo_url || userProfile.photoURL || undefined,
                email: userProfile.email || '',
                createdAt: userProfile.created_at || '',
                joinedAt: joinedAt,
                stats: {
                  totalStudyTime: totalStudyTime / 60, // Convert from seconds to minutes
                  studyStreak
                }
              } as UserProfile
            }
            return null
          }) || []
        )

        const profiles = memberProfiles.filter((profile): profile is UserProfile => profile !== null);

        // Sort members: owner first, then by study time (highest to lowest), then by join time
        const sortedProfiles = profiles.sort((a, b) => {
          // Group owner always comes first
          if (a.id === activeGroup?.owner_id || a.id === activeGroup?.createdBy) return -1;
          if (b.id === activeGroup?.owner_id || b.id === activeGroup?.createdBy) return 1;

          // Sort by study time (highest to lowest)
          const timeA = a.stats?.totalStudyTime || 0;
          const timeB = b.stats?.totalStudyTime || 0;
          if (timeA !== timeB) return timeB - timeA;

          // Then sort by join time (earliest first)
          const joinTimeA = new Date(a.joinedAt || a.createdAt || '').getTime();
          const joinTimeB = new Date(b.joinedAt || b.createdAt || '').getTime();
          return joinTimeA - joinTimeB;
        });

        setMembers(sortedProfiles);
      } catch (error) {
        console.error('Error loading members:', error)
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load group members"
        })
      } finally {
        setIsLoading(false)
      }
    }

    loadMembers()
  }, [groupId, activeGroup?.members, activeGroup?.owner_id, activeGroup?.createdBy, toast])

  // Load group statistics
  useEffect(() => {
    const loadGroupStats = async () => {
      if (!groupId) return

      setIsLoadingStats(true)
      try {
        const stats = await getGroupStats(groupId)
        // Convert totalStudyTime and averageStudyTimePerMember from seconds to minutes
        const convertedStats = {
          ...stats,
          totalStudyTime: stats.totalStudyTime / 60,
          averageStudyTimePerMember: stats.averageStudyTimePerMember / 60
        };
        setGroupStats(convertedStats)
      } catch (error) {
        console.error('Error loading group stats:', error)
      } finally {
        setIsLoadingStats(false)
      }
    }

    loadGroupStats()
  }, [groupId])

  useEffect(() => {
    if (activeGroup) {
      setDescription(activeGroup.description || '')
    }
  }, [activeGroup])

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = Math.floor(minutes % 60)

    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  const handleDeleteGroup = async () => {
    if (!groupId || !activeGroup || (activeGroup.owner_id !== user?.id && activeGroup.createdBy !== user?.id)) return

    setIsDeleting(true)
    try {
      // Delete the group from Supabase
      await deleteGroup(groupId)

      // Remove from local state
      removeGroup(groupId)

      toast({
        title: "Group deleted",
        description: "The group has been successfully deleted"
      })
    } catch (error) {
      console.error('Error deleting group:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to delete group"
      })
    } finally {
      setIsDeleting(false)
    }
  }

  const handleLeaveGroup = async () => {
    if (!groupId || !activeGroup || activeGroup.owner_id === user?.id || activeGroup.createdBy === user?.id) return

    setIsLeaving(true)
    try {
      // Leave the group using Supabase function
      await leaveGroup(groupId, user?.id || '')

      // Remove from local state
      removeGroup(groupId)

      toast({
        title: "Left group",
        description: "You have successfully left the group"
      })
    } catch (error) {
      console.error('Error leaving group:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to leave group"
      })
    } finally {
      setIsLeaving(false)
    }
  }

  const handleRemoveMember = async (memberId: string) => {
    if (!groupId || !activeGroup || (activeGroup.owner_id !== user?.id && activeGroup.createdBy !== user?.id)) return

    setRemovingMemberId(memberId)
    try {
      // Get current members and remove the specified member
      const currentMembers = activeGroup.members || []
      const updatedMembers = currentMembers.filter((id: string) => id !== memberId)

      // Update the group in Supabase
      await updateGroup(groupId, {
        members: updatedMembers
      })

      // Update local state - reload members
      setMembers(members.filter(member => member.id !== memberId))

      toast({
        title: "Member removed",
        description: "The member has been removed from the group"
      })
    } catch (error) {
      console.error('Error removing member:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to remove member"
      })
    } finally {
      setRemovingMemberId(null)
    }
  }

  const handleSaveDescription = async () => {
    if (!groupId || !activeGroup) return

    setIsSavingDescription(true)
    try {
      // Update the group description in Supabase
      await updateGroup(groupId, {
        description: description
      })

      toast({
        title: "Description updated",
        description: "The group description has been updated"
      })

      setIsEditingDescription(false)
    } catch (error) {
      console.error('Error updating description:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update description"
      })
    } finally {
      setIsSavingDescription(false)
    }
  }

  if (!activeGroup) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center space-y-2">
          <p className="text-white/60">Group not found</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-10">
      {/* Header section */}
      <div className="relative rounded-xl overflow-hidden">
        {/* Background gradient for header (Dark mode only) */}
        <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/20 via-purple-500/20 to-pink-500/20 opacity-50 dark:block hidden"></div>
        <div className="absolute inset-0 backdrop-blur-sm dark:block hidden"></div>
        
        {/* Theme-aware header container */}
        <div className="relative p-6 border border-border dark:border-white/10 rounded-xl bg-card dark:bg-transparent">
          <div className="flex flex-col md:flex-row md:items-center gap-4 justify-between">
            <div className="space-y-2 flex-1">
              <div className="flex items-center gap-2">
                <h2 className="text-2xl font-bold">{activeGroup.name}</h2>
                {activeGroup.isPublic ? (
                  <span className="text-xs bg-indigo-500/20 text-indigo-400 px-2 py-0.5 rounded-full flex items-center gap-1">
                    <Globe className="h-3 w-3" />
                    <span>Public</span>
                  </span>
                ) : (
                  <span className="text-xs bg-slate-500/20 text-slate-400 px-2 py-0.5 rounded-full flex items-center gap-1">
                    <Lock className="h-3 w-3" />
                    <span>Private</span>
                  </span>
                )}
              </div>
              
              {/* Group Description */}
              {isEditingDescription ? (
                <div className="mt-2 flex items-start gap-2">
                  <Input
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Add a brief description"
                    maxLength={100}
                    className="bg-white/5 border-white/10"
                  />
                  <div className="flex gap-2">
                    <Button 
                      size="sm" 
                      onClick={handleSaveDescription}
                      disabled={isSavingDescription}
                    >
                      {isSavingDescription ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
                      ) : (
                        "Save"
                      )}
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={() => {
                        setIsEditingDescription(false)
                        setDescription(activeGroup.description || '')
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="mt-2 flex items-center gap-2 group">
                  {/* Theme-aware description text */}
                  <p className="text-sm text-muted-foreground dark:text-white/80">
                    {activeGroup.description || 'No description available'}
                  </p>
                  {(activeGroup.owner_id === user?.id || activeGroup.createdBy === user?.id) && (
                    // Theme-aware edit button
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-6 px-2 text-xs text-muted-foreground dark:text-white/60 hover:text-foreground dark:hover:text-white opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() => setIsEditingDescription(true)}
                    >
                      {activeGroup.description ? 'Edit' : 'Add description'}
                    </Button>
                  )}
                </div>
              )}
              
              <div className="flex items-center gap-4 mt-3">
                {/* Theme-aware text */}
                <div className="text-sm text-muted-foreground dark:text-white/60 flex items-center">
                  <Users className="h-4 w-4 mr-1" />
                  <span>{members.length} {members.length === 1 ? 'member' : 'members'}</span>
                </div>
                
                {/* Theme-aware text */}
                <div className="text-sm text-muted-foreground dark:text-white/60 flex items-center">
                  <Calendar className="h-4 w-4 mr-1" />
                  <span>Created {activeGroup.createdAt ? new Date(activeGroup.createdAt).toLocaleDateString() : 'recently'}</span>
                </div>
              </div>
            </div>
            
            <div className="flex flex-wrap items-center gap-2 mt-2 md:mt-0">
              <Dialog>
                <DialogTrigger asChild>
                  {/* Theme-aware share button */}
                  <Button 
                    variant="outline" 
                    size="sm"
                    className="bg-secondary/50 dark:bg-white/5 hover:bg-secondary dark:hover:bg-white/10 border-border dark:border-white/10 h-8 px-3 text-muted-foreground dark:text-white"
                  >
                    <Share2 className="h-4 w-4" />
                  </Button>
                </DialogTrigger>
                {/* Theme-aware share dialog */}
                <DialogContent className="bg-background dark:bg-gradient-to-br dark:from-slate-800/95 dark:to-slate-900/95 border-border dark:border-white/10 text-foreground dark:text-white sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>Share Group Invite</DialogTitle>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                      <Label>Invite Link</Label>
                      <div className="flex gap-2">
                        {/* Theme-aware input */}
                        <Input
                          value={`https://isotopeai.in/i/${activeGroup.inviteCode || activeGroup.invite_code}`}
                          readOnly
                          className="bg-muted dark:bg-white/5 border-border dark:border-white/10 text-foreground dark:text-white"
                        />
                        {/* Theme-aware button */}
                        <Button
                          size="icon"
                          variant="outline"
                          className="bg-secondary/50 dark:bg-white/5 hover:bg-secondary dark:hover:bg-white/10 border-border dark:border-white/10 text-muted-foreground dark:text-white"
                          onClick={async () => {
                            const link = `https://isotopeai.in/i/${activeGroup.inviteCode || activeGroup.invite_code}`;
                            await navigator.clipboard.writeText(link);
                            toast({
                              title: "Copied!",
                              description: "Invite link copied to clipboard"
                            });
                          }}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                      <p className="text-sm text-white/60">
                        Share this link for one-click joining
                      </p>
                    </div>
                    
                    <div className="grid gap-2">
                      <Label>Invite Code</Label>
                      <div className="flex gap-2">
                        {/* Theme-aware input */}
                        <Input
                          value={activeGroup.inviteCode || activeGroup.invite_code || ''}
                          readOnly
                          className="bg-muted dark:bg-white/5 border-border dark:border-white/10 text-foreground dark:text-white"
                        />
                        {/* Theme-aware button */}
                        <Button
                          size="icon"
                          variant="outline"
                          className="bg-secondary/50 dark:bg-white/5 hover:bg-secondary dark:hover:bg-white/10 border-border dark:border-white/10 text-muted-foreground dark:text-white"
                          onClick={() => {
                            navigator.clipboard.writeText(activeGroup.inviteCode || activeGroup.invite_code || '');
                            toast({
                              title: "Copied!",
                              description: "Invite code copied to clipboard"
                            });
                          }}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                      <p className="text-sm text-white/60">
                        Share this code with others to let them join your group
                      </p>
                    </div>
                    
                    <div className="grid gap-2">
                      <Label>Share Options</Label>
                      <div className="grid grid-cols-2 gap-2">
                        {/* Theme-aware button */}
                        <Button 
                          variant="outline" 
                          className="flex items-center gap-2 bg-secondary/50 dark:bg-white/5 hover:bg-secondary dark:hover:bg-white/10 border-border dark:border-white/10 text-muted-foreground dark:text-white" 
                          onClick={() => {
                            const inviteCode = activeGroup.inviteCode || activeGroup.invite_code || '';
                            const inviteMessage = `Join our study group on IsotopeAI!\n\n` +
                              `Group: ${activeGroup.name}\n` +
                              `Code: ${inviteCode}\n\n` +
                              `Join directly: https://isotopeai.in/i/${inviteCode}\n\n` +
                              `✨ Let's study together!`;

                              navigator.clipboard.writeText(inviteMessage);
                              toast({
                                title: "Copied!",
                                description: "Invite message copied to clipboard"
                              });
                          }}
                        >
                          <Copy className="h-4 w-4" />
                          <span>Copy Invite</span>
                        </Button>
                        
                        {/* Theme-aware button */}
                        <Button 
                          variant="outline"
                          className="flex items-center gap-2 bg-secondary/50 dark:bg-white/5 hover:bg-green-600/20 border-border dark:border-white/10 text-muted-foreground dark:text-white" 
                          onClick={() => {
                            const inviteCode = activeGroup.inviteCode || activeGroup.invite_code || '';
                            const inviteMessage = `Join our study group on IsotopeAI!\n\n` +
                              `Group: ${activeGroup.name}\n` +
                              `Code: ${inviteCode}\n\n` +
                              `Join directly: https://isotopeai.in/i/${inviteCode}\n\n` +
                              `✨ Let's study together!`;

                              // Create WhatsApp share URL
                              const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(inviteMessage)}`;

                              // Open WhatsApp share in new window
                              window.open(whatsappUrl, '_blank');
                          }}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-green-500">
                            <path d="M3 21l1.65-3.8a9 9 0 1 1 3.4 2.9L3 21" />
                            <path d="M9 10a.5.5 0 0 0 1 0V9a.5.5 0 0 0-1 0v1Z" />
                            <path d="M13.5 10a.5.5 0 0 0 1 0V9a.5.5 0 0 0-1 0v1Z" />
                            <path d="M9 13.5a.5.5 0 0 0 .5.5h5a.5.5 0 0 0 0-1h-5a.5.5 0 0 0-.5.5Z" />
                          </svg>
                          <span>Share via WhatsApp</span>
                        </Button>
                      </div>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>

              {(activeGroup.owner_id === user?.id || activeGroup.createdBy === user?.id) ? (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="destructive"
                      size="sm"
                      className="bg-red-500/20 text-red-400 hover:bg-red-500/30 h-8 px-3"
                      disabled={isDeleting}
                    >
                      {isDeleting ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
                      ) : (
                        <Trash2 className="h-4 w-4" />
                      )}
                    </Button>
                  </AlertDialogTrigger>
                  {/* Theme-aware dialog */}
                  <AlertDialogContent className="bg-background dark:bg-[#1a1f3c] border-border dark:border-white/20 text-foreground dark:text-white">
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                      <AlertDialogDescription className="text-muted-foreground dark:text-white/60">
                        This action cannot be undone. This will permanently delete the group
                        and remove all members.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      {/* Theme-aware cancel */}
                      <AlertDialogCancel className="bg-transparent dark:bg-white/10 hover:bg-accent dark:hover:bg-white/20 border-border dark:border-0">
                        Cancel
                      </AlertDialogCancel>
                      {/* Theme-aware action */}
                      <AlertDialogAction
                        onClick={handleDeleteGroup}
                        className="bg-destructive/10 dark:bg-red-500/20 text-destructive dark:text-red-400 hover:bg-destructive/20 dark:hover:bg-red-500/30 border-0"
                      >
                        Delete
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              ) : (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="destructive"
                      size="sm"
                      className="bg-red-500/20 text-red-400 hover:bg-red-500/30 h-8 px-3"
                      disabled={isLeaving}
                    >
                      {isLeaving ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
                      ) : (
                        <LogOut className="h-4 w-4" />
                      )}
                    </Button>
                  </AlertDialogTrigger>
                  {/* Theme-aware dialog */}
                  <AlertDialogContent className="bg-background dark:bg-[#1a1f3c] border-border dark:border-white/20 text-foreground dark:text-white">
                    <AlertDialogHeader>
                      <AlertDialogTitle>Leave Group?</AlertDialogTitle>
                      <AlertDialogDescription className="text-muted-foreground dark:text-white/60">
                        Are you sure you want to leave this group? You'll need an invite to rejoin.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      {/* Theme-aware cancel */}
                      <AlertDialogCancel className="bg-transparent dark:bg-white/10 hover:bg-accent dark:hover:bg-white/20 border-border dark:border-0">
                        Cancel
                      </AlertDialogCancel>
                      {/* Theme-aware action */}
                      <AlertDialogAction
                        onClick={handleLeaveGroup}
                        className="bg-destructive/10 dark:bg-red-500/20 text-destructive dark:text-red-400 hover:bg-destructive/20 dark:hover:bg-red-500/30 border-0"
                      >
                        Leave
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
            </div>
          </div>

          {(activeGroup.owner_id === user?.id || activeGroup.createdBy === user?.id) && (
            // Theme-aware border
            <div className="mt-4 pt-4 border-t border-border dark:border-white/10">
              <div className="flex items-center gap-2">
                {/* Switch should adapt */}
                <Switch
                  checked={Boolean(activeGroup.isPublic)}
                  onCheckedChange={(checked) => updateGroupPrivacy(checked)}
                  className="data-[state=checked]:bg-indigo-600"
                />
                <div>
                  {/* Theme-aware text */}
                  <p className="text-sm text-muted-foreground dark:text-white/60">
                    {activeGroup.isPublic
                      ? "Group is public (anyone can discover and join)"
                      : "Group is private (only people with invite code can join)"}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Group Statistics section */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium flex items-center gap-2 text-foreground">
            <Clock className="h-5 w-5 text-indigo-400" />
            <span>Group Statistics</span>
          </h3>
        </div>

        {isLoadingStats ? (
          <div className="flex items-center justify-center h-[100px]">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
          </div>
        ) : groupStats ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Study Time */}
            <div className="p-6 rounded-xl border border-border dark:border-white/10 bg-card dark:bg-white/5 backdrop-blur-sm">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-indigo-500/20">
                  <Clock className="h-5 w-5 text-indigo-400" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground dark:text-white/60">Total Study Time</p>
                  <p className="text-xl font-bold text-foreground dark:text-white">
                    {formatDuration(groupStats.totalStudyTime)}
                  </p>
                </div>
              </div>
            </div>

            {/* Total Sessions */}
            <div className="p-6 rounded-xl border border-border dark:border-white/10 bg-card dark:bg-white/5 backdrop-blur-sm">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-green-500/20">
                  <CalendarClock className="h-5 w-5 text-green-400" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground dark:text-white/60">Total Sessions</p>
                  <p className="text-xl font-bold text-foreground dark:text-white">
                    {groupStats.totalSessions}
                  </p>
                </div>
              </div>
            </div>

            {/* Average per Member */}
            <div className="p-6 rounded-xl border border-border dark:border-white/10 bg-card dark:bg-white/5 backdrop-blur-sm">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-purple-500/20">
                  <Users className="h-5 w-5 text-purple-400" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground dark:text-white/60">Avg per Member</p>
                  <p className="text-xl font-bold text-foreground dark:text-white">
                    {formatDuration(groupStats.averageStudyTimePerMember)}
                  </p>
                </div>
              </div>
            </div>

            {/* Last Activity */}
            <div className="p-6 rounded-xl border border-border dark:border-white/10 bg-card dark:bg-white/5 backdrop-blur-sm">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-orange-500/20">
                  <Calendar className="h-5 w-5 text-orange-400" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground dark:text-white/60">Last Activity</p>
                  <p className="text-xl font-bold text-foreground dark:text-white">
                    {groupStats.lastActiveDate
                      ? new Date(groupStats.lastActiveDate).toLocaleDateString()
                      : 'No activity'
                    }
                  </p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground dark:text-white/60">
            No statistics available
          </div>
        )}
      </div>

      {/* Members section */}
      <div>
        <div className="flex items-center justify-between mb-6">
          {/* Theme-aware title */}
          <h3 className="text-lg font-medium flex items-center gap-2 text-foreground">
            <Users className="h-5 w-5 text-indigo-400" />
            <span>Group Members</span>
          </h3>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center h-[200px]">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
          </div>
        ) : members.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {members.map((member) => (
              <UserProfileDialog
                key={member.id}
                userId={member.id}
                username={member.username || 'Anonymous'}
                photoURL={member.photoURL}
              >
                {/* Theme-aware member card */}
                <div className="flex flex-col items-center p-6 rounded-xl border border-border dark:border-white/10 bg-card dark:bg-white/5 backdrop-blur-sm relative cursor-pointer hover:bg-accent dark:hover:bg-white/10 transition-all duration-300 hover:shadow-lg dark:hover:shadow-indigo-500/10 hover:border-primary/20 dark:hover:border-indigo-500/30 group">
                  {/* Profile Header */}
                  <div className="absolute top-3 right-3 flex items-center gap-2">
                    {(activeGroup.owner_id === member.id || activeGroup.createdBy === member.id) && (
                      <div className="relative">
                        <div className="absolute -inset-1 bg-yellow-500/20 rounded-full blur-sm animate-pulse"></div>
                        <Crown className="h-5 w-5 text-yellow-500 relative" />
                      </div>
                    )}
                    {member.id === user?.id && (
                      <span className="text-xs bg-purple-500/20 text-purple-400 px-2 py-0.5 rounded">
                        You
                      </span>
                    )}
                    {(activeGroup.owner_id === user?.id || activeGroup.createdBy === user?.id) && member.id !== user?.id && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          {/* Theme-aware button */}
                          <Button variant="ghost" size="icon" className="h-8 w-8 text-muted-foreground dark:text-white/60 hover:text-foreground dark:hover:text-white hover:bg-accent/50 dark:hover:bg-white/10">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        {/* Theme-aware dropdown */}
                        <DropdownMenuContent align="end" className="bg-popover border-border dark:bg-[#1a1f3c] dark:border-white/10 text-popover-foreground dark:text-white">
                          <DropdownMenuItem 
                            className="text-red-500 dark:text-red-400 focus:text-red-500 dark:focus:text-red-400 focus:bg-destructive/10 dark:focus:bg-red-400/10 cursor-pointer hover:bg-destructive/10 dark:hover:bg-red-900/50" // Added hover style
                            onClick={() => handleRemoveMember(member.id)}
                            disabled={removingMemberId === member.id}
                          >
                            {removingMemberId === member.id ? (
                              <div className="flex items-center gap-2">
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
                                <span>Removing...</span>
                              </div>
                            ) : (
                              "Remove from Group"
                            )}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </div>

                  {/* Avatar */}
                  <div className="mb-4 relative">
                    {/* Dark mode only gradient */}
                    <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/20 via-purple-500/20 to-pink-500/20 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity dark:block hidden"></div>
                    {/* Theme-aware avatar */}
                    <Avatar className="h-24 w-24 border-2 border-border dark:border-white/10 group-hover:border-primary/40 dark:group-hover:border-indigo-500/40 transition-colors">
                      <AvatarImage src={member.photoURL} />
                      <AvatarFallback className="bg-muted dark:bg-white/5">
                        <User className="h-12 w-12 text-muted-foreground dark:text-white/60" />
                      </AvatarFallback>
                    </Avatar>
                  </div>

                  {/* User Info */}
                  <div className="text-center space-y-1">
                    {/* Theme-aware text */}
                    <h3 className="font-semibold text-lg text-foreground dark:text-white group-hover:text-primary dark:group-hover:text-indigo-300 transition-colors">{member.username || 'Anonymous'}</h3>
                  </div>

                  {/* Stats Section */}
                  {/* Theme-aware border */}
                  <div className="w-full mt-6 pt-4 border-t border-border dark:border-white/10 space-y-3">
                    <div className="flex justify-between items-center text-sm group">
                      {/* Theme-aware text */}
                      <span className="text-muted-foreground dark:text-white/60 flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>Time Studied</span>
                      </span>
                      <div className="relative">
                        {/* Theme-aware badge style */}
                        <span className="font-medium text-indigo-700 bg-indigo-100 dark:text-indigo-400 dark:bg-indigo-400/10 px-2 py-0.5 rounded">
                          {member.stats ? formatDuration(member.stats.totalStudyTime) : '0m'}
                        </span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center text-sm">
                      {/* Theme-aware text */}
                      <span className="text-muted-foreground dark:text-white/60 flex items-center gap-1">
                        <Flame className="h-3 w-3 text-orange-400" />
                        <span>Study Streak</span>
                      </span>
                      <div className="relative">
                        {/* Theme-aware badge style */}
                        <span className="font-medium text-orange-700 bg-orange-100 dark:text-orange-400 dark:bg-orange-400/10 px-2 py-0.5 rounded">
                          {member.stats?.studyStreak || 0} days
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </UserProfileDialog>
            ))}
          </div>
        ) : (
          // Theme-aware text
          <div className="text-center py-8 text-muted-foreground dark:text-white/60">
            No members found in this group
          </div>
        )}
      </div>
    </div>
  )
}
