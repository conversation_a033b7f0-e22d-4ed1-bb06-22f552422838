import { useEffect, useState } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext'
import { useChatStore } from '@/stores/chatStore'
import { getGroups, updateGroup, findGroupByInviteCode } from '@/utils/supabase'
import { useToast } from '@/components/ui/use-toast'

export function JoinHandler() {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const navigate = useNavigate()
  const location = useLocation()
  const { user, loading } = useSupabaseAuth()
  const { groups, addGroup } = useChatStore()
  const { toast } = useToast()

  useEffect(() => {
    // Extract the invite code from the URL
    const searchParams = new URLSearchParams(location.search)
    const inviteCode = searchParams.get('code')

    const processInviteCode = async () => {
      // If no invite code is provided, redirect to groups page
      if (!inviteCode) {
        navigate('/groups')
        return
      }

      try {
        // Wait for auth to be ready
        if (loading) return

        // If user is not logged in, store the invite code and redirect to the invite landing page
        if (!user) {
          // Store the invite code in localStorage
          localStorage.setItem('pendingInviteCode', inviteCode)
          // Redirect to the invite landing page with the code
          navigate(`/invite?code=${inviteCode}`)
          return
        }

        // Find group with matching invite code
        const targetGroup = await findGroupByInviteCode(inviteCode)

        if (!targetGroup) {
          setError('Invalid invite code. No group found with this code.')
          toast({
            variant: "destructive",
            title: "Invalid Code",
            description: "No group found with this invite code"
          })
          setTimeout(() => navigate('/groups'), 3000)
          return
        }

        // Check if user is already a member
        if (targetGroup.members?.includes(user.id)) {
          toast({
            title: "Already a Member",
            description: "You are already a member of this group"
          })
          navigate(`/groups`)
          return
        }

        // Add user to group members
        const updatedMembers = [...(targetGroup.members || []), user.id]
        await updateGroup(targetGroup.id, {
          members: updatedMembers
        })

        // Add group to local state
        addGroup({
          id: targetGroup.id,
          name: targetGroup.name,
          members: updatedMembers,
          createdAt: targetGroup.createdAt || new Date(),
          ownerId: targetGroup.owner_id || targetGroup.createdBy,
          inviteCode: targetGroup.invite_code || targetGroup.inviteCode,
          isPublic: targetGroup.isPublic || false
        })

        // Clear the pending invite code from localStorage
        localStorage.removeItem('pendingInviteCode')

        toast({
          title: "Success!",
          description: `You have joined the group "${targetGroup.name}"`
        })

        // Redirect to groups page
        navigate('/groups')
      } catch (error) {
        console.error('Error processing invite code:', error)
        setError('Failed to join group. Please try again.')
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to join group. Please try again."
        })
        setTimeout(() => navigate('/groups'), 3000)
      } finally {
        setIsLoading(false)
      }
    }

    processInviteCode()
  }, [user, loading, location.search, navigate, addGroup, toast])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-[#0f172a] to-[#1e293b] text-white">
      <div className="text-center p-8 max-w-md">
        {isLoading ? (
          <>
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-500 mx-auto mb-4"></div>
            <h1 className="text-2xl font-bold mb-2">Processing Invite...</h1>
            <p className="text-white/60">Please wait while we process your group invitation.</p>
          </>
        ) : error ? (
          <>
            <div className="bg-red-500/20 p-4 rounded-lg mb-4">
              <h1 className="text-2xl font-bold mb-2">Error</h1>
              <p className="text-white/80">{error}</p>
              <p className="text-white/60 mt-4">Redirecting you to the groups page...</p>
            </div>
          </>
        ) : (
          <>
            <div className="bg-green-500/20 p-4 rounded-lg mb-4">
              <h1 className="text-2xl font-bold mb-2">Success!</h1>
              <p className="text-white/80">You have successfully joined the group.</p>
              <p className="text-white/60 mt-4">Redirecting you to the groups page...</p>
            </div>
          </>
        )}
      </div>
    </div>
  )
} 