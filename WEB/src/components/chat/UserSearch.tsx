import { useState, useEffect } from 'react'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { X, Check, User } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'

interface User {
  id: string
  name: string
  username: string
  photoURL?: string
}

interface UserSearchProps {
  selectedUsers: User[]
  onSelectUser: (user: User) => void
  onRemoveUser: (userId: string) => void
}

export function UserSearch({ selectedUsers, onSelectUser, onRemoveUser }: UserSearchProps) {
  const [searchResults, setSearchResults] = useState<User[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const { user: currentUser } = useAuth()

  useEffect(() => {
    const searchUsers = async () => {
      if (!searchQuery.trim() || searchQuery.length < 2) {
        setSearchResults([])
        return
      }

      setIsSearching(true)
      try {
        const usersRef = collection(db, 'users')
        const q = searchQuery.toLowerCase()
        console.log('Searching for:', q)

        const results = new Map<string, User>()

        // Search by username first since it's more specific
        const usernameQuery = firestoreQuery(
          usersRef,
          where('username', '>=', q),
          where('username', '<=', q + '\uf8ff')
        )

        console.log('Fetching users by username...')
        const usernameSnapshot = await getDocs(usernameQuery)
        console.log('Username results:', usernameSnapshot.size)

        usernameSnapshot.forEach((doc) => {
          const data = doc.data()
          console.log('User data (username):', data)
          if (doc.id !== currentUser?.uid && !results.has(doc.id)) {
            results.set(doc.id, {
              id: doc.id,
              name: data.displayName || 'Anonymous',
              username: data.username || '',
              photoURL: data.photoURL
            })
          }
        })

        // Search by display name if no username results
        if (results.size === 0) {
          const nameQuery = firestoreQuery(
            usersRef,
            where('displayName', '>=', q),
            where('displayName', '<=', q + '\uf8ff')
          )

          console.log('Fetching users by display name...')
          const nameSnapshot = await getDocs(nameQuery)
          console.log('Display name results:', nameSnapshot.size)

          nameSnapshot.forEach((doc) => {
            const data = doc.data()
            console.log('User data (name):', data)
            if (doc.id !== currentUser?.uid && !results.has(doc.id)) {
              results.set(doc.id, {
                id: doc.id,
                name: data.displayName || 'Anonymous',
                username: data.username || '',
                photoURL: data.photoURL
              })
            }
          })
        }

        // If still no results, try a broader search
        if (results.size === 0) {
          console.log('No exact matches found, trying broader search...')
          const broadQuery = firestoreQuery(usersRef)
          const allUsers = await getDocs(broadQuery)
          
          allUsers.forEach((doc) => {
            const data = doc.data()
            if (doc.id !== currentUser?.uid && 
                !results.has(doc.id) && 
                ((data.username || '').toLowerCase().includes(q) || 
                 (data.displayName || '').toLowerCase().includes(q))) {
              results.set(doc.id, {
                id: doc.id,
                name: data.displayName || 'Anonymous',
                username: data.username || '',
                photoURL: data.photoURL
              })
            }
          })
        }

        const finalResults = Array.from(results.values())
        console.log('Final results:', finalResults)
        setSearchResults(finalResults)
      } catch (error) {
        console.error('Error searching users:', error)
        setSearchResults([])
      } finally {
        setIsSearching(false)
      }
    }

    const timeoutId = setTimeout(searchUsers, 500)
    return () => clearTimeout(timeoutId)
  }, [searchQuery, currentUser])

  return (
    <div className="space-y-4">
      <div className="rounded-lg border shadow-md">
        <Command>
          <CommandInput 
            placeholder="Search users by name or username..." 
            value={searchQuery}
            onValueChange={setSearchQuery}
          />
          <CommandList>
            <CommandEmpty>
              {isSearching ? 'Searching...' : 'No users found.'}
            </CommandEmpty>
            <CommandGroup>
              <ScrollArea className="h-48">
                {searchResults.map((user) => {
                  const isSelected = selectedUsers.some(u => u.id === user.id)
                  return (
                    <CommandItem
                      key={user.id}
                      onSelect={() => {
                        if (isSelected) {
                          onRemoveUser(user.id)
                        } else {
                          onSelectUser(user)
                        }
                      }}
                      className="flex items-center gap-2"
                    >
                      <Avatar className="h-6 w-6 flex-shrink-0">
                        <AvatarImage src={user.photoURL} />
                        <AvatarFallback>
                          <User className="h-4 w-4" />
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col flex-1 min-w-0">
                        <span className="text-sm font-medium truncate">{user.name}</span>
                        {user.username && (
                          <span className="text-xs text-muted-foreground truncate">
                            @{user.username}
                          </span>
                        )}
                      </div>
                      {isSelected && <Check className="h-4 w-4 flex-shrink-0" />}
                    </CommandItem>
                  )
                })}
              </ScrollArea>
            </CommandGroup>
          </CommandList>
        </Command>
      </div>

      <div className="flex flex-wrap gap-2">
        {selectedUsers.map((user) => (
          <Badge
            key={user.id}
            variant="secondary"
            className="gap-2 pr-1"
          >
            <Avatar className="h-4 w-4">
              <AvatarImage src={user.photoURL} />
              <AvatarFallback>
                <User className="h-3 w-3" />
              </AvatarFallback>
            </Avatar>
            <span className="truncate max-w-[150px]">
              {user.name}
              {user.username && (
                <span className="text-xs text-muted-foreground ml-1">
                  @{user.username}
                </span>
              )}
            </span>
            <button
              className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  onRemoveUser(user.id)
                }
              }}
              onMouseDown={(e) => {
                e.preventDefault()
                e.stopPropagation()
              }}
              onClick={() => onRemoveUser(user.id)}
            >
              <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
            </button>
          </Badge>
        ))}
      </div>
    </div>
  )
} 