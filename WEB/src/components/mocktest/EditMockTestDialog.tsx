import { useState, useEffect } from "react";
import { Calendar as CalendarIcon, Plus, Trash2 } from "lucide-react";
import { format } from "date-fns";
import { MockTest, SubjectMarks } from "@/types/mockTest";
import { updateMockTest, calculateTestTotals } from "@/utils/mockTestUtils";
import { Subject } from "../productivity/SubjectManager";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogHeader,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { getSubjects } from "@/utils/subjectDataManager"; // Added import
import { useSupabaseSubjectStore } from "@/stores/supabaseSubjectStore"; // Added import
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "../ui/badge";

interface EditMockTestDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  mockTest: MockTest | null;
  onSave: () => void;
  userId: string;
}

interface SubjectMarkInput {
  subjectId: string;
  marksObtained: string;
  totalMarks: string;
  subject?: string;
  subjectColor?: string;
}

export function EditMockTestDialog({
  open,
  onOpenChange,
  mockTest,
  onSave,
  userId,
}: EditMockTestDialogProps) {
  const { subjects, isLoading: subjectsLoading, setSubjects: setStoreSubjects } = useSupabaseSubjectStore(); // Zustand store for subjects
  const [testName, setTestName] = useState("");
  // const [subjects, setSubjects] = useState<Subject[]>([]); // Replaced with Zustand store
  const [subjectInputs, setSubjectInputs] = useState<SubjectMarkInput[]>([]);
  const [notes, setNotes] = useState("");
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load subjects using the data manager
  useEffect(() => {
    if (open && userId) {
      getSubjects(userId).then(loadedSubjects => {
        // subjectDataManager updates the store
      });
    }
  }, [open, userId]);

  // Reset form when mock test changes
  useEffect(() => {
    if (mockTest && subjects.length > 0) {
      setTestName(mockTest.name);
      
      // Convert subjectMarks to subjectInputs
      const inputs = mockTest.subjectMarks.map(subjectMark => {
        // Find the subject ID
        const subject = subjects.find(s => s.name === subjectMark.subject);
        return {
          subjectId: subject?.id || "",
          marksObtained: subjectMark.marksObtained.toString(),
          totalMarks: subjectMark.totalMarks.toString(),
          subject: subjectMark.subject,
          subjectColor: subjectMark.subjectColor
        };
      });
      
      // If no subjects, add an empty one
      if (inputs.length === 0) {
        inputs.push({
          subjectId: "",
          marksObtained: "",
          totalMarks: "",
          subject: "",
          subjectColor: ""
        });
      }
      
      setSubjectInputs(inputs);
      setNotes(mockTest.notes || "");
      setSelectedDate(new Date(mockTest.date));
    }
  }, [mockTest, subjects]);

  const addSubjectInput = () => {
    setSubjectInputs([...subjectInputs, {
      subjectId: "",
      marksObtained: "",
      totalMarks: "",
      subject: "",
      subjectColor: ""
    }]);
  };

  const removeSubjectInput = (index: number) => {
    if (subjectInputs.length > 1) {
      const newInputs = [...subjectInputs];
      newInputs.splice(index, 1);
      setSubjectInputs(newInputs);
    }
  };

  const handleSubjectChange = (index: number, field: keyof SubjectMarkInput, value: string) => {
    const newInputs = [...subjectInputs];
    newInputs[index] = {
      ...newInputs[index],
      [field]: value
    };
    setSubjectInputs(newInputs);
  };

  const validateInputs = (): boolean => {
    if (!testName) {
      toast({
        title: "Missing information",
        description: "Please provide a test name",
        variant: "destructive",
      });
      return false;
    }

    // Check if at least one subject is selected
    const hasSelectedSubject = subjectInputs.some(input => input.subjectId);
    if (!hasSelectedSubject) {
      toast({
        title: "Missing information",
        description: "Please select at least one subject",
        variant: "destructive",
      });
      return false;
    }

    // Validate each subject input
    for (let i = 0; i < subjectInputs.length; i++) {
      const input = subjectInputs[i];
      
      // Skip empty rows (except if it's the only row)
      if (!input.subjectId && subjectInputs.length > 1) continue;
      
      if (!input.subjectId) {
        toast({
          title: "Missing information",
          description: `Please select a subject for row ${i + 1}`,
          variant: "destructive",
        });
        return false;
      }

      if (!input.marksObtained || !input.totalMarks) {
        toast({
          title: "Missing information",
          description: `Please enter marks for subject in row ${i + 1}`,
          variant: "destructive",
        });
        return false;
      }

      const obtainedMarks = parseFloat(input.marksObtained);
      const totalMarks = parseFloat(input.totalMarks);
      
      if (isNaN(obtainedMarks) || isNaN(totalMarks)) {
        toast({
          title: "Invalid marks",
          description: `Please enter valid numbers for marks in row ${i + 1}`,
          variant: "destructive",
        });
        return false;
      }

      if (obtainedMarks > totalMarks) {
        toast({
          title: "Invalid marks",
          description: `Obtained marks cannot be greater than total marks in row ${i + 1}`,
          variant: "destructive",
        });
        return false;
      }
    }

    return true;
  };

  const handleSave = async () => {
    if (!mockTest) return;
    if (!validateInputs()) return;

    setIsSubmitting(true);

    try {
      // Process subject inputs
      const subjectMarks: SubjectMarks[] = subjectInputs
        .filter(input => input.subjectId) // Filter out empty rows
        .map(input => {
          const subject = subjects.find(s => s.id === input.subjectId);
          if (!subject) throw new Error("Subject not found");
          
          return {
            subject: subject.name,
            subjectColor: subject.color,
            marksObtained: parseFloat(input.marksObtained),
            totalMarks: parseFloat(input.totalMarks)
          };
        });

      // Calculate totals
      const { totalMarksObtained, totalMarks } = calculateTestTotals(subjectMarks);

      const updatedTest: MockTest = {
        ...mockTest,
        name: testName,
        date: format(selectedDate, "yyyy-MM-dd"),
        subjectMarks,
        totalMarksObtained,
        totalMarks,
        notes: notes,
      };

      await updateMockTest(userId, updatedTest);

      toast({
        title: "Success",
        description: "Mock test updated successfully",
      });
      
      onSave();
      onOpenChange(false);
    } catch (error) {
      console.error("Error updating mock test:", error);
      toast({
        title: "Error",
        description: "Failed to update mock test",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Calculate total marks
  const calculateTotal = () => {
    let obtained = 0;
    let total = 0;

    subjectInputs.forEach(input => {
      const obtainedMarks = parseFloat(input.marksObtained);
      const totalMarks = parseFloat(input.totalMarks);

      if (!isNaN(obtainedMarks)) obtained += obtainedMarks;
      if (!isNaN(totalMarks)) total += totalMarks;
    });

    return {
      obtained,
      total,
      percentage: total > 0 ? ((obtained / total) * 100).toFixed(2) : "0.00"
    };
  };

  const totals = calculateTotal();

  if (!mockTest) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[650px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Mock Test</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="testName">Test Name</Label>
              <Input
                id="testName"
                placeholder="Enter test name"
                value={testName}
                onChange={(e) => setTestName(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label>Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !selectedDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {selectedDate ? (
                      format(selectedDate, "PPP")
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={(date) => date && setSelectedDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Label>Subjects & Marks</Label>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={addSubjectInput}
                type="button"
              >
                <Plus className="h-3 w-3 mr-1" /> Add Subject
              </Button>
            </div>
            
            {subjectInputs.map((input, index) => (
              <div key={index} className="flex items-end gap-2 mb-2">
                <div className="w-full md:w-1/2">
                  <Label className="text-xs mb-1 block">Subject</Label>
                  <Select
                    value={input.subjectId}
                    onValueChange={(value) => handleSubjectChange(index, "subjectId", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select subject" />
                    </SelectTrigger>
                    <SelectContent>
                      {subjects.map((subject) => (
                        <SelectItem key={subject.id} value={subject.id}>
                          <div className="flex items-center">
                            <div
                              className="h-3 w-3 rounded-full mr-2"
                              style={{ backgroundColor: subject.color }}
                            ></div>
                            {subject.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="w-full md:w-1/4">
                  <Label className="text-xs mb-1 block">Marks Obtained</Label>
                  <Input
                    type="number"
                    placeholder="Obtained"
                    value={input.marksObtained}
                    onChange={(e) => handleSubjectChange(index, "marksObtained", e.target.value)}
                  />
                </div>
                <div className="w-full md:w-1/4">
                  <Label className="text-xs mb-1 block">Total Marks</Label>
                  <Input
                    type="number"
                    placeholder="Total"
                    value={input.totalMarks}
                    onChange={(e) => handleSubjectChange(index, "totalMarks", e.target.value)}
                  />
                </div>
                {subjectInputs.length > 1 && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="flex-shrink-0"
                    onClick={() => removeSubjectInput(index)}
                    type="button"
                  >
                    <Trash2 className="h-4 w-4 text-destructive" />
                  </Button>
                )}
              </div>
            ))}
          </div>

          {/* Show total marks */}
          <Card className="bg-secondary/50">
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm font-medium">Total Marks</p>
                  <p className="text-xl font-bold">{totals.obtained} / {totals.total}</p>
                </div>
                <Badge 
                  className={cn(
                    "text-lg px-3 py-1.5",
                    parseFloat(totals.percentage) >= 90 ? "bg-green-500" :
                    parseFloat(totals.percentage) >= 75 ? "bg-green-400" :
                    parseFloat(totals.percentage) >= 60 ? "bg-yellow-400" :
                    parseFloat(totals.percentage) >= 40 ? "bg-orange-400" :
                    "bg-red-500"
                  )}
                >
                  {totals.percentage}%
                </Badge>
              </div>
            </CardContent>
          </Card>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              placeholder="Add any notes about this test"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="min-h-[100px]"
            />
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isSubmitting}>
            {isSubmitting ? "Saving..." : "Save Changes"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}