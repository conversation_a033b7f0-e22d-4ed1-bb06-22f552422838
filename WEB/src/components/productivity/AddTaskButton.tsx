import { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Plus } from 'lucide-react';
import { format } from 'date-fns';
import { useSupabaseTodoStore } from '@/stores/supabaseTodoStore';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';

interface AddTaskButtonProps {
  groupId?: string;
}

export function AddTaskButton({ groupId }: AddTaskButtonProps = {}) {
  const { user } = useSupaba<PERSON>Auth();
  const { addTask } = useSupabaseTodoStore();

  const [isAddingTask, setIsAddingTask] = useState(false);
  const [newTaskTitle, setNewTaskTitle] = useState('');
  const [newTaskDescription, setNewTaskDescription] = useState('');
  const [newTaskPriority, setNewTaskPriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [newTaskDueDate, setNewTaskDueDate] = useState<Date | undefined>(undefined);
  const [descriptionError, setDescriptionError] = useState('');

  const handleAddTask = async () => {
    // Reset error state
    setDescriptionError('');

    // Validate inputs
    if (!user || !newTaskTitle.trim()) return;

    if (!newTaskDescription.trim()) {
      setDescriptionError('Description is required');
      return;
    }

    // Create task data object, omitting undefined values
    const taskData: any = {
      title: newTaskTitle,
      description: newTaskDescription,
      priority: newTaskPriority,
      createdBy: user.id,
    };

    // Only add dueDate if it exists
    if (newTaskDueDate) {
      taskData.dueDate = newTaskDueDate.getTime();
    }

    // Only add groupId if it exists and is not undefined
    if (groupId) {
      taskData.groupId = groupId;
    }

    await addTask(taskData);

    // Reset form
    setNewTaskTitle('');
    setNewTaskDescription('');
    setNewTaskPriority('medium');
    setNewTaskDueDate(undefined);
    setIsAddingTask(false);
  };

  return (
    <>
      <Button
        size="sm"
        className="bg-purple-600 hover:bg-purple-700 text-white"
        onClick={(e) => {
          e.stopPropagation();
          setIsAddingTask(true);
        }}
      >
        <Plus className="h-4 w-4 mr-1" />
        Add Task
      </Button>

      {/* Add Task Dialog */}
      <Dialog open={isAddingTask} onOpenChange={setIsAddingTask}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Task</DialogTitle>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="title" className="text-sm font-medium">
                Title
              </label>
              <Input
                id="title"
                value={newTaskTitle}
                onChange={(e) => setNewTaskTitle(e.target.value)}
                placeholder="Task title"
              />
            </div>

            <div className="grid gap-2">
              <label htmlFor="description" className="text-sm font-medium">
                Description
              </label>
              <Textarea
                id="description"
                value={newTaskDescription}
                onChange={(e) => setNewTaskDescription(e.target.value)}
                placeholder="Describe your task in detail. What needs to be done? Any specific requirements or notes?"
                rows={3}
                className={descriptionError ? "border-red-500" : ""}
              />
              {descriptionError && (
                <p className="text-sm text-red-500 mt-1">{descriptionError}</p>
              )}
            </div>

            <div className="grid gap-2">
              <label htmlFor="priority" className="text-sm font-medium">
                Priority
              </label>
              <Select
                value={newTaskPriority}
                onValueChange={(value) => setNewTaskPriority(value as 'low' | 'medium' | 'high')}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <label htmlFor="dueDate" className="text-sm font-medium">
                Due Date
              </label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {newTaskDueDate ? format(newTaskDueDate, 'PPP') : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={newTaskDueDate}
                    onSelect={setNewTaskDueDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <DialogFooter>
            <Button onClick={handleAddTask}>Add Task</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}