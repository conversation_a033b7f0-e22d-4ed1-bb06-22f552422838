import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { 
  ArrowRight, 
  GripHorizontal, 
  ListTodo, 
  LayoutGrid, 
  Clock, 
  Plus, 
  Edit, 
  ArrowLeftRight,
  HelpCircle
} from 'lucide-react';

interface TutorialStep {
  title: string;
  description: string;
  icon: React.ReactNode;
}

interface KanbanTutorialProps {
  showHelpButton?: boolean;
}

export function KanbanTutorial({ showHelpButton = true }: KanbanTutorialProps) {
  const [showTutorial, setShowTutorial] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  
  const tutorialSteps: TutorialStep[] = [
    {
      title: "Welcome to Tasks!",
      description: "This tutorial will guide you through the basics of using the Kanban board to manage your tasks efficiently.",
      icon: <ListTodo className="h-12 w-12 text-purple-500" />
    },
    {
      title: "Columns Represent Workflow Stages",
      description: "Tasks move from left to right through columns like 'Todo', 'In Progress', and 'Done' as you work on them.",
      icon: <LayoutGrid className="h-12 w-12 text-purple-500" />
    },
    {
      title: "Drag and Drop Tasks",
      description: "Simply click and drag a task card to move it between columns as its status changes.",
      icon: <GripHorizontal className="h-12 w-12 text-purple-500" />
    },
    {
      title: "Create New Tasks",
      description: "Click the 'Add Task' button to create new tasks with title, description, priority, and due date.",
      icon: <Plus className="h-12 w-12 text-purple-500" />
    },
    {
      title: "Edit Tasks",
      description: "Click on any task card to edit its details or update its information.",
      icon: <Edit className="h-12 w-12 text-purple-500" />
    },
    {
      title: "Switch Between Views",
      description: "Toggle between Kanban board and Table view depending on how you prefer to manage your tasks.",
      icon: <ArrowLeftRight className="h-12 w-12 text-purple-500" />
    },
    {
      title: "Tasks Are Prioritized",
      description: "Tasks are automatically sorted by priority (High, Medium, Low) and then by due date.",
      icon: <Clock className="h-12 w-12 text-purple-500" />
    }
  ];
  
  useEffect(() => {
    // Check if this is the first visit to the tasks page
    const hasSeenTutorial = localStorage.getItem('hasSeenKanbanTutorial');
    if (!hasSeenTutorial) {
      setShowTutorial(true);
    }
  }, []);
  
  const handleNextStep = () => {
    if (currentStep < tutorialSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      completeTutorial();
    }
  };
  
  const handlePrevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };
  
  const completeTutorial = () => {
    // Mark tutorial as seen
    localStorage.setItem('hasSeenKanbanTutorial', 'true');
    setShowTutorial(false);
  };
  
  const skipTutorial = () => {
    // Mark tutorial as seen but allow user to skip
    localStorage.setItem('hasSeenKanbanTutorial', 'true');
    setShowTutorial(false);
  };
  
  const showTutorialManually = () => {
    setCurrentStep(0);
    setShowTutorial(true);
  };
  
  return (
    <>
      {showHelpButton && (
        <div className="fixed bottom-6 left-6 z-50">
          <Button
            variant="outline"
            size="icon"
            className="rounded-full bg-purple-600 hover:bg-purple-700 text-white border-none shadow-lg"
            onClick={showTutorialManually}
            title="Show Kanban Tutorial"
          >
            <HelpCircle className="h-5 w-5" />
          </Button>
        </div>
      )}
      
      <Dialog open={showTutorial} onOpenChange={setShowTutorial}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <div className="flex justify-center mb-4">
              {tutorialSteps[currentStep].icon}
            </div>
            <DialogTitle className="text-xl text-center">
              {tutorialSteps[currentStep].title}
            </DialogTitle>
            <DialogDescription className="text-center pt-2">
              {tutorialSteps[currentStep].description}
            </DialogDescription>
          </DialogHeader>
          
          {/* Step indicator */}
          <div className="flex justify-center gap-1 py-4">
            {tutorialSteps.map((_, index) => (
              <div 
                key={index} 
                className={`h-2 w-2 rounded-full ${
                  index === currentStep ? 'bg-purple-500' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
          
          <DialogFooter className="flex justify-between items-center">
            <div>
              {currentStep > 0 ? (
                <Button variant="outline" onClick={handlePrevStep}>
                  Back
                </Button>
              ) : (
                <Button variant="outline" onClick={skipTutorial}>
                  Skip
                </Button>
              )}
            </div>
            
            <Button onClick={handleNextStep}>
              {currentStep < tutorialSteps.length - 1 ? (
                <>
                  Next
                  <ArrowRight className="ml-2 h-4 w-4" />
                </>
              ) : (
                'Got it!'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
} 