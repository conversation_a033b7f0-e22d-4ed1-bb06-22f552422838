import React, { useState } from 'react';
import { Link, LinkProps, useNavigate } from 'react-router-dom';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Timer, ExternalLink } from 'lucide-react';

interface ProductivityLinkProps extends LinkProps {
  children: React.ReactNode;
}

export const ProductivityLink: React.FC<ProductivityLinkProps> = ({ 
  to, 
  children, 
  onClick,
  ...props 
}) => {
  const [showWarning, setShowWarning] = useState(false);
  const navigate = useNavigate();

  const handleLinkClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    setShowWarning(true);
    
    // Call the original onClick if provided
    if (onClick) {
      onClick(e);
    }
  };

  const handleContinue = () => {
    setShowWarning(false);
    navigate(to.toString());
  };

  const handleOpenNewTab = () => {
    setShowWarning(false);
    window.open(to.toString(), '_blank');
  };

  return (
    <>
      <Link
        to={to}
        onClick={handleLinkClick}
        {...props}
      >
        {children}
      </Link>

      <AlertDialog open={showWarning} onOpenChange={setShowWarning}>
        <AlertDialogContent className="max-w-md bg-background/95 backdrop-blur-md border border-border/50 shadow-lg dark:shadow-purple-500/5">
          <AlertDialogHeader className="space-y-3">
            <div className="mx-auto bg-amber-500/10 dark:bg-amber-500/20 p-3 rounded-full">
              <Timer className="h-6 w-6 text-amber-500" />
            </div>
            <AlertDialogTitle className="text-center text-lg font-semibold">
              Study Timer Warning
            </AlertDialogTitle>
            <AlertDialogDescription className="text-center text-muted-foreground">
              Navigating away from this page might cause your study timer to lose progress. 
              Would you like to continue or open in a new tab instead?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-0">
            <AlertDialogCancel className="sm:mt-0 border-border/50 text-foreground">
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleOpenNewTab}
              className="bg-primary/10 hover:bg-primary/20 text-primary border border-primary/20 flex items-center gap-2"
            >
              <ExternalLink className="h-4 w-4" />
              Open in New Tab
            </AlertDialogAction>
            <AlertDialogAction 
              onClick={handleContinue}
              className="bg-primary hover:bg-primary/90"
            >
              Continue Anyway
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
