interface ProgressCircleProps {
  progress: number // 0 to 100
}

export function ProgressCircle({ progress }: ProgressCircleProps) {
  const radius = 90
  const strokeWidth = 8
  const normalizedRadius = radius - strokeWidth * 2
  const circumference = normalizedRadius * 2 * Math.PI
  const strokeDashoffset = circumference - (progress / 100) * circumference

  return (
    <svg
      height={radius * 2}
      width={radius * 2}
      className="transform -rotate-90"
    >
      {/* Background circle */}
      <circle
        stroke="hsl(var(--muted))"
        fill="transparent"
        strokeWidth={strokeWidth}
        r={normalizedRadius}
        cx={radius}
        cy={radius}
        className="opacity-20"
      />
      {/* Progress circle */}
      <circle
        stroke="hsl(var(--primary))"
        fill="transparent"
        strokeWidth={strokeWidth}
        strokeDasharray={circumference + ' ' + circumference}
        style={{ strokeDashoffset }}
        r={normalizedRadius}
        cx={radius}
        cy={radius}
        className="transition-all duration-500 ease-in-out"
      />
    </svg>
  )
} 