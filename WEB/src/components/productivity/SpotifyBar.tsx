import { useState, useEffect, useRef } from 'react';
import { Music, X, ChevronDown, ChevronUp, Plus, Trash2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useLocalStorage } from '@/hooks/useLocalStorage';
import { toast } from '@/components/ui/use-toast';

// Study playlists
const DEFAULT_STUDY_PLAYLISTS = [
  {
    id: '37i9dQZF1DWZeKCadgRdKQ',
    name: 'Deep Focus',
    description: 'Keep calm and focus with ambient and post-rock music',
    image: 'https://i.scdn.co/image/ab67706f00000003d804b1c6b8c223a38cbb2332'
  },
  {
    id: '37i9dQZF1DX8NTLI2TtZa6',
    name: '<PERSON><PERSON> Beat<PERSON>',
    description: 'Beats to study and relax to',
    image: 'https://i.scdn.co/image/ab67706f000000034d26d431869cabfc53c67d8e'
  },
  {
    id: '37i9dQZF1DX9sIqqvKsjG8',
    name: 'Jazz for Study',
    description: 'Smooth jazz for focus',
    image: 'https://i.scdn.co/image/ab67706f0000000329a7ef6a8539face2f8f321d'
  },
  {
    id: '37i9dQZF1DX692WcMwL2yW',
    name: 'Atmospheric Piano',
    description: 'Peaceful piano music for focus and reflection',
    image: 'https://i.scdn.co/image/ab67706f000000035cd9dad3a65bdf3e44a1c7a1'
  }
];

interface PlaylistItem {
  id: string;
  name: string;
  description: string;
  image?: string;
  isCustom?: boolean;
}

interface SpotifyBarProps {
  className?: string;
  onToggleCollapse?: (isCollapsed: boolean) => void;
  isCollapsed?: boolean;
  setIsCollapsed?: (value: boolean) => void;
}

export function SpotifyBar({ className, onToggleCollapse, isCollapsed, setIsCollapsed }: SpotifyBarProps) {
  // Store playlists in localStorage to allow custom playlists
  const [playlists, setPlaylists] = useLocalStorage<PlaylistItem[]>('spotify-playlists', DEFAULT_STUDY_PLAYLISTS);
  const [currentPlaylist, setCurrentPlaylist] = useLocalStorage<string | null>('spotify-current-playlist', null);
  const [showPlayDialog, setShowPlayDialog] = useState<boolean>(false);
  const [showEmbeddedPlayer, setShowEmbeddedPlayer] = useState<boolean>(!!currentPlaylist);
  
  // Use the inverted value of isCollapsed from localStorage, defaulting to expanded state (true)
  const [expandedPlayer, setExpandedPlayer] = useLocalStorage<boolean>('spotify-is-expanded', true);
  
  // Use useLocalStorage hook instead of useState to persist visibility
  const [isVisible, setIsVisible] = useLocalStorage<boolean>('spotify-is-visible', true);
  const playerContainerRef = useRef<HTMLDivElement | null>(null);
  
  // For adding new playlists
  const [showAddPlaylistDialog, setShowAddPlaylistDialog] = useState<boolean>(false);
  const [newPlaylistId, setNewPlaylistId] = useState<string>('');
  const [newPlaylistName, setNewPlaylistName] = useState<string>('');
  const [newPlaylistDescription, setNewPlaylistDescription] = useState<string>('');
  
  // For confirming playlist deletion
  const [showDeleteDialog, setShowDeleteDialog] = useState<boolean>(false);
  const [playlistToDelete, setPlaylistToDelete] = useState<string | null>(null);
  
  // For confirming reset to defaults
  const [showResetDialog, setShowResetDialog] = useState<boolean>(false);

  // Set up the embedded player when a playlist is selected
  useEffect(() => {
    if (currentPlaylist && showEmbeddedPlayer) {
      // If there's an existing iframe, remove it first
      if (playerContainerRef.current) {
        while (playerContainerRef.current.firstChild) {
          playerContainerRef.current.removeChild(playerContainerRef.current.firstChild);
        }
      }
      
      // Create a new iframe for the Spotify player
      const iframe = document.createElement('iframe');
      iframe.src = `https://open.spotify.com/embed/playlist/${currentPlaylist}?utm_source=generator&theme=0`;
      iframe.width = '100%';
      iframe.height = expandedPlayer ? '352' : '80';
      iframe.frameBorder = '0';
      iframe.allow = 'autoplay; clipboard-write; encrypted-media; fullscreen; picture-in-picture';
      iframe.loading = 'lazy';
      iframe.style.borderRadius = '12px';
      iframe.style.maxWidth = '100%';
      
      // Add the iframe to the container div
      if (playerContainerRef.current) {
        playerContainerRef.current.appendChild(iframe);
      }
    }
  }, [currentPlaylist, showEmbeddedPlayer, expandedPlayer]);

  // Initialize player state on mount
  useEffect(() => {
    if (currentPlaylist) {
      setShowEmbeddedPlayer(true);
    }
  }, [currentPlaylist]);

  // If isCollapsed prop is provided, use it to control expandedPlayer state
  useEffect(() => {
    if (isCollapsed !== undefined) {
      setExpandedPlayer(!isCollapsed);
    }
  }, [isCollapsed, setExpandedPlayer]);

  // Select a playlist
  const selectPlaylist = (playlistId: string) => {
    setCurrentPlaylist(playlistId);
    
    // If we've already shown the embedded player, update it with the new playlist
    if (showEmbeddedPlayer) {
      // The iframe will be updated by the useEffect
    } else {
      // Show the dialog for first-time users
      setShowPlayDialog(true);
    }
  };

  // Open in Spotify
  const openInSpotify = () => {
    if (currentPlaylist) {
      window.open(`https://open.spotify.com/playlist/${currentPlaylist}`, '_blank');
    }
  };

  // Toggle expanded player
  const toggleExpandedPlayer = () => {
    const newExpandedState = !expandedPlayer;
    setExpandedPlayer(newExpandedState);
    
    // Call onToggleCollapse callback if provided
    if (onToggleCollapse) {
      onToggleCollapse(!newExpandedState);
    }
    
    // Update isCollapsed state if setIsCollapsed is provided
    if (setIsCollapsed) {
      setIsCollapsed(!newExpandedState);
    }
  };

  // Enable the embedded player
  const enableEmbeddedPlayer = () => {
    setShowEmbeddedPlayer(true);
    setShowPlayDialog(false);
  };
  
  // Add a new playlist
  const handleAddPlaylist = () => {
    // Validate playlist ID format
    if (!newPlaylistId.trim()) {
      toast({
        title: "Error",
        description: "Playlist ID is required",
        variant: "destructive",
      });
      return;
    }
    
    // Extract playlist ID from URL if a full URL was pasted
    let playlistId = newPlaylistId.trim();
    const urlMatch = playlistId.match(/playlist\/([a-zA-Z0-9]+)/);
    if (urlMatch && urlMatch[1]) {
      playlistId = urlMatch[1];
    }
    
    // Check if playlist already exists
    if (playlists.some(p => p.id === playlistId)) {
      toast({
        title: "Error",
        description: "This playlist is already in your library",
        variant: "destructive",
      });
      return;
    }
    
    // Create new playlist object
    const newPlaylist: PlaylistItem = {
      id: playlistId,
      name: newPlaylistName.trim() || 'Custom Playlist',
      description: newPlaylistDescription.trim() || 'Added by you',
      isCustom: true
    };
    
    // Update playlists
    setPlaylists([...playlists, newPlaylist]);
    
    // Reset form
    setNewPlaylistId('');
    setNewPlaylistName('');
    setNewPlaylistDescription('');
    setShowAddPlaylistDialog(false);
    
    toast({
      title: "Playlist added",
      description: "Your custom playlist has been added",
    });
  };
  
  // Delete a playlist
  const handleDeletePlaylist = () => {
    if (!playlistToDelete) return;
    
    // Check if the playlist being deleted is currently playing
    if (currentPlaylist === playlistToDelete) {
      setCurrentPlaylist(null);
      setShowEmbeddedPlayer(false);
    }
    
    // Filter out the playlist to delete
    const updatedPlaylists = playlists.filter(p => p.id !== playlistToDelete);
    setPlaylists(updatedPlaylists);
    
    // Close dialog and reset state
    setShowDeleteDialog(false);
    setPlaylistToDelete(null);
    
    toast({
      title: "Playlist removed",
      description: "The playlist has been removed from your library",
    });
  };
  
  // Open delete confirmation dialog
  const confirmDeletePlaylist = (playlistId: string) => {
    setPlaylistToDelete(playlistId);
    setShowDeleteDialog(true);
  };
  
  // Reset playlists to default
  const resetToDefaultPlaylists = () => {
    setPlaylists(DEFAULT_STUDY_PLAYLISTS);
    setShowResetDialog(false);
    
    toast({
      title: "Playlists reset",
      description: "Your playlists have been reset to default",
    });
  };

  // Find the current playlist data
  const currentPlaylistData = playlists.find(playlist => playlist.id === currentPlaylist);

  // If player is not visible, return null (completely remove it)
  if (!isVisible) {
    return null;
  }

  return (
    <div className="w-full relative">
      {/* Control buttons */}
      <div className="absolute -top-2 -right-2 flex space-x-1 z-10">
        {showEmbeddedPlayer && (
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 rounded-full bg-background/80 shadow-sm text-muted-foreground hover:text-foreground"
            onClick={toggleExpandedPlayer}
          >
            {expandedPlayer ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
          </Button>
        )}
      </div>

      {/* Spotify embed section */}
      {showEmbeddedPlayer ? (
        <div className="flex flex-col items-center animate-fadeIn">
          <div 
            ref={playerContainerRef}
            className="w-full max-w-md rounded-lg overflow-hidden shadow-lg bg-black/70 dark:bg-black/40 border border-border dark:border-white/10 transition-all duration-300"
          />
        </div>
      ) : (
        /* Enhanced playlist selection button */
        <div className="flex justify-center">
          <Button
            variant="outline"
            size="lg"
            onClick={() => setShowPlayDialog(true)}
            className="bg-background/60 dark:bg-black/40 backdrop-blur-lg border-2 border-primary/20 hover:border-primary/50 transition-all duration-300 px-6 py-6 shadow-md"
          >
            <Music className="h-5 w-5 mr-3 text-primary" />
            <span className="font-medium">Select Music for Focus</span>
          </Button>
        </div>
      )}
      
      {/* Dialog to show when user clicks to select a playlist */}
      <Dialog open={showPlayDialog} onOpenChange={setShowPlayDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Select a Spotify Playlist</DialogTitle>
            <DialogDescription>
              Choose a playlist to listen to while you work
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col space-y-3 py-4">
            <div className="space-y-2">
              {playlists.map((playlist) => (
                <div key={playlist.id} className="relative flex items-center">
                  <button
                    className={cn(
                      "w-full flex items-center space-x-2 p-3 rounded-md hover:bg-accent text-left border border-transparent hover:border-border",
                      currentPlaylist === playlist.id && "bg-accent/70 border-border"
                    )}
                    onClick={() => selectPlaylist(playlist.id)}
                  >
                    <div className="overflow-hidden">
                      <div className="font-medium truncate">{playlist.name}</div>
                      <div className="text-xs text-muted-foreground truncate">{playlist.description}</div>
                    </div>
                  </button>
                  
                  {/* Delete button for all playlists */}
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="absolute right-2 h-7 w-7 opacity-70 hover:opacity-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      confirmDeletePlaylist(playlist.id);
                    }}
                  >
                    <Trash2 className="h-3.5 w-3.5 text-destructive" />
                  </Button>
                </div>
              ))}
            </div>
            
            {/* Add custom playlist button */}
            <Button 
              variant="outline" 
              className="w-full flex items-center justify-center" 
              onClick={() => setShowAddPlaylistDialog(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Custom Playlist
            </Button>
            
            {/* Reset to default button */}
            <Button 
              variant="ghost" 
              className="w-full flex items-center justify-center text-muted-foreground hover:text-foreground" 
              onClick={() => setShowResetDialog(true)}
            >
              Reset to Default Playlists
            </Button>
            
            <p className="text-sm text-muted-foreground">
              You will need a Spotify account to listen. You can also open the playlist directly in the Spotify app.
            </p>
          </div>
          <DialogFooter className="flex justify-between sm:justify-between">
            <Button variant="outline" onClick={() => setShowPlayDialog(false)}>
              Cancel
            </Button>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={openInSpotify}>
                Open in Spotify
              </Button>
              <Button onClick={enableEmbeddedPlayer}>
                Play Here
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Add playlist dialog */}
      <Dialog open={showAddPlaylistDialog} onOpenChange={setShowAddPlaylistDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add Spotify Playlist</DialogTitle>
            <DialogDescription>
              Add your own Spotify playlist by entering the playlist ID or URL
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="playlist-id">Playlist ID or URL</Label>
              <Input
                id="playlist-id"
                value={newPlaylistId}
                onChange={(e) => setNewPlaylistId(e.target.value)}
                placeholder="spotify:playlist:37i9dQZF1DX... or https://open.spotify.com/playlist/..."
              />
              <p className="text-xs text-muted-foreground">
                Enter the Spotify playlist ID or the full playlist URL
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="playlist-name">Name (optional)</Label>
              <Input
                id="playlist-name"
                value={newPlaylistName}
                onChange={(e) => setNewPlaylistName(e.target.value)}
                placeholder="My Study Playlist"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="playlist-description">Description (optional)</Label>
              <Input
                id="playlist-description"
                value={newPlaylistDescription}
                onChange={(e) => setNewPlaylistDescription(e.target.value)}
                placeholder="My custom playlist for studying"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddPlaylistDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddPlaylist}>
              Add Playlist
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete confirmation dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Remove Playlist</DialogTitle>
            <DialogDescription>
              Are you sure you want to remove this playlist? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeletePlaylist}>
              Remove
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Reset to default confirmation dialog */}
      <Dialog open={showResetDialog} onOpenChange={setShowResetDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Reset Playlists</DialogTitle>
            <DialogDescription>
              Are you sure you want to reset to default playlists? All custom playlists will be removed, and any removed default playlists will be restored.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowResetDialog(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={resetToDefaultPlaylists}>
              Reset to Default
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 