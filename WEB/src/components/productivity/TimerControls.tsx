import { Button } from "@/components/ui/button"
import { Play, Pause, RotateCcw, Maximize, Minimize, Square, Check } from "lucide-react";
import { useState, useEffect } from "react";

// Define the new status type matching StudyTimer
type TimerStatus = "idle" | "running" | "paused";

interface TimerControlsProps {
  timerState: TimerStatus; // Use the new type, keep prop name for now to minimize changes in parent
  mode: "pomodoro" | "stopwatch";
  onStart: () => void;
  onPause: () => void;
  onReset: () => void
  onComplete?: () => void
}

export function TimerControls({
  timerState, // Keep prop name for now
  mode,
  onStart,
  onPause,
  onReset,
  onComplete
}: TimerControlsProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange)
  }, [])

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch(err => {
        console.error('Error attempting to enable fullscreen:', err)
      })
    } else {
      document.exitFullscreen().catch(err => {
        console.error('Error attempting to exit fullscreen:', err)
      })
    }
  }

  // Handle start/pause button click with animation
  const handlePrimaryButtonClick = () => {
    setIsAnimating(true);
    setTimeout(() => {
      setIsAnimating(false);
      if (timerState === "running") {
        onPause();
      } else {
        onStart();
      }
    }, 150);
  };

  // Get appropriate button content based on timer state
  const getPrimaryButtonContent = () => {
    if (timerState === "running") {
      return (
        <>
          <Pause className="h-6 w-6 mr-2" />
          <span>Pause</span>
        </>
      );
    } else if (timerState === "paused") {
      return (
        <>
          <Play className="h-6 w-6 mr-2" />
          <span>Continue</span>
        </>
      );
    } else {
      return (
        <>
          <Play className="h-6 w-6 mr-2" />
          <span className="text-xl font-bold">Start</span>
        </>
      );
    }
  };

  // Show complete button if timer is running or paused in any mode
  const shouldShowCompleteButton = () => {
    return timerState === "running" || timerState === "paused";
  };

  return (
    <div className="flex flex-col items-center">
      <div className="flex items-center gap-6 mb-2">
        <Button
          variant="ghost"
          size="icon"
          onClick={onReset}
          className="text-muted-foreground dark:text-white/60 hover:text-foreground dark:hover:text-white hover:bg-accent/50 dark:hover:bg-transparent rounded-full h-12 w-12"
          title="Reset Timer"
        >
          <RotateCcw className="h-5 w-5" />
        </Button>

        <Button
          variant="ghost"
          size="lg"
          className={`flex items-center justify-center bg-gradient-to-r ${
            timerState === "running" 
              ? "from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700" 
              : "from-purple-500 to-purple-700 hover:from-purple-600 hover:to-purple-800"
          } text-white rounded-full h-16 w-36 text-lg font-medium shadow-lg ${
            timerState === "running" 
              ? "shadow-amber-500/20" 
              : "shadow-purple-500/20"
          } transition-all ${
            isAnimating ? "scale-95" : "scale-100"
          }`}
          onClick={handlePrimaryButtonClick}
        >
          {getPrimaryButtonContent()}
        </Button>

        {shouldShowCompleteButton() ? (
          <Button
            variant="ghost"
            size="icon"
            className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-full h-12 w-12 shadow-lg shadow-green-500/20"
            onClick={onComplete}
            title="Complete Session"
          >
            <Check className="h-5 w-5" />
          </Button>
        ) : (
          <Button
            variant="ghost"
            size="icon"
            className="text-muted-foreground dark:text-white/60 hover:text-foreground dark:hover:text-white hover:bg-accent/50 dark:hover:bg-transparent rounded-full h-12 w-12"
            onClick={toggleFullscreen}
            title={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
          >
            {isFullscreen ? <Minimize className="h-5 w-5" /> : <Maximize className="h-5 w-5" />}
          </Button>
        )}
      </div>
      
      {/* Show fullscreen button when complete button is visible */}
      {shouldShowCompleteButton() && (
        <div className="mt-2">
          <Button
            variant="ghost"
            size="sm"
            className="text-muted-foreground dark:text-white/60 hover:text-foreground dark:hover:text-white hover:bg-accent/50 dark:hover:bg-transparent"
            onClick={toggleFullscreen}
          >
            {isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
          </Button>
        </div>
      )}
    </div>
  )
}
