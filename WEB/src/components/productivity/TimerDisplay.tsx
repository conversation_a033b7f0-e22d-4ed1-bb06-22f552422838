interface TimerDisplayProps {
  mode: "pomodoro" | "stopwatch"
  timeRemaining: number
  elapsedTime: number
}

export function TimerDisplay({ 
  mode, 
  timeRemaining, 
  elapsedTime
}: TimerDisplayProps) {
  // Validate time values to prevent unrealistic displays
  const validateTime = (seconds: number): number => {
    // Ensure time is non-negative and a valid number
    if (typeof seconds !== 'number' || isNaN(seconds) || seconds < 0) return 0;
    
    // Cap maximum displayable time to 24 hours (86400 seconds)
    const MAX_DISPLAYABLE_TIME = 24 * 60 * 60;
    if (seconds > MAX_DISPLAYABLE_TIME) return MAX_DISPLAYABLE_TIME;
    
    return seconds;
  };

  const formatTime = (seconds: number) => {
    // Validate the time before formatting
    const validatedSeconds = validateTime(seconds);
    
    // Use Math.floor to ensure consistent second counting
    const hours = Math.floor(validatedSeconds / 3600);
    const minutes = Math.floor((validatedSeconds % 3600) / 60);
    const remainingSeconds = Math.floor(validatedSeconds % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Determine which time value to display based on mode
  const timeToDisplay = mode === "pomodoro" ? timeRemaining : elapsedTime;

  return (
    <div className="flex flex-col items-center justify-center">
      {/* Use theme-aware text color */}
      <div className="text-[100px] sm:text-[140px] md:text-[180px] font-bold leading-none tracking-tighter font-mono text-foreground">
        {formatTime(timeToDisplay)}
      </div>
    </div>
  );
}
