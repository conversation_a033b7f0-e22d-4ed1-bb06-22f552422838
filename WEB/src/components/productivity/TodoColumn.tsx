import { useState, useEffect } from 'react';
import { Droppable } from '@hello-pangea/dnd';
import { TodoColumn as TodoColumnType } from '@/types/todo';
import { TodoTask } from './TodoTask';
import { useSupabaseTodoStore } from '@/stores/supabaseTodoStore';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Edit2, MoreVertical, Trash2, AlertCircle } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { startOfDay } from 'date-fns';
import { Badge } from '@/components/ui/badge';

interface TodoColumnProps {
  column: TodoColumnType;
  tasks: any[];
  isDefaultColumn: boolean;
  isActiveDragSource?: boolean;
  activeDragId?: string | null;
}

export function TodoColumn({
  column,
  tasks,
  isDefaultColumn,
  isActiveDragSource = false,
  activeDragId = null
}: TodoColumnProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [title, setTitle] = useState(column.title);
  const [sortedTasks, setSortedTasks] = useState(tasks);

  const { updateColumn, deleteColumn } = useSupabaseTodoStore();

  // Debug logging
  useEffect(() => {
    console.log(`Column ${column.id} (${column.title}) has ${tasks.length} tasks:`, tasks);
  }, [column, tasks]);

  // Check if a task is overdue
  const isTaskOverdue = (task: any): boolean => {
    if (!task.dueDate) return false;

    const today = startOfDay(new Date());
    const taskDueDate = startOfDay(new Date(task.dueDate));

    // Task is overdue if due date is before today (not including today)
    return taskDueDate < today;
  };

  // Count overdue tasks in this column
  const overdueTasksCount = tasks.filter(task => isTaskOverdue(task)).length;

  // Sort tasks whenever they change
  useEffect(() => {
    // Sort tasks by overdue status, then priority, then due date
    const sorted = [...tasks].sort((a, b) => {
      // First sort by overdue status (overdue tasks first)
      const aIsOverdue = isTaskOverdue(a);
      const bIsOverdue = isTaskOverdue(b);

      if (aIsOverdue && !bIsOverdue) return -1;
      if (!aIsOverdue && bIsOverdue) return 1;

      // If both tasks have the same overdue status, sort by priority
      const priorityOrder = { high: 0, medium: 1, low: 2 };
      const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];

      if (priorityDiff !== 0) {
        return priorityDiff; // If priorities are different, sort by priority
      }

      // Then sort by due date (earliest first)
      if (a.dueDate && b.dueDate) {
        return a.dueDate - b.dueDate;
      } else if (a.dueDate) {
        return -1; // a has due date, b doesn't
      } else if (b.dueDate) {
        return 1; // b has due date, a doesn't
      }

      // If neither has a due date and priorities are the same, keep original order
      return 0;
    });

    setSortedTasks(sorted);
  }, [tasks]);

  const handleSave = async () => {
    await updateColumn(column.id, title);
    setIsEditing(false);
  };

  const handleDelete = async () => {
    await deleteColumn(column.id);
    setIsEditing(false);
  };

  return (
    <>
      {/* Theme-aware Card */}
      <Card className={`bg-card dark:bg-background/60 backdrop-blur-sm border-border dark:border-muted h-full flex flex-col ${
        isActiveDragSource ? 'ring-2 ring-primary/50' : ''
      }`}>
        <CardHeader className="p-3 pb-0 flex flex-row justify-between items-center">
          <div className="flex items-center gap-2">
            {/* Theme-aware CardTitle */}
            <CardTitle className="text-sm font-medium text-card-foreground">{column.title}</CardTitle>
            {overdueTasksCount > 0 && column.title !== 'Done' && (
              <Badge variant="destructive" className="text-xs px-1.5 py-0 h-5 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                {overdueTasksCount}
              </Badge>
            )}
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              {/* Theme-aware Button */}
              <Button variant="ghost" size="icon" className="h-8 w-8 text-muted-foreground">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            {/* Theme-aware Dropdown Content */}
            <DropdownMenuContent align="end" className="bg-popover border-border text-popover-foreground">
              <DropdownMenuItem onClick={() => setIsEditing(true)} className="hover:bg-accent">
                <Edit2 className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              {!isDefaultColumn && (
                <DropdownMenuItem
                  onClick={handleDelete}
                  className="text-destructive focus:text-destructive hover:bg-destructive/10" // Added hover style
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </CardHeader>

        <CardContent className="p-3 flex-grow overflow-auto">
          <Droppable droppableId={column.id}>
            {(provided, snapshot) => (
              <div
                {...provided.droppableProps}
                ref={provided.innerRef}
                className={`min-h-[50px] transition-colors rounded-md ${
                  snapshot.isDraggingOver
                    ? 'bg-primary/10 border border-primary/20'
                    : ''
                }`}
                style={{
                  minHeight: sortedTasks.length ? '100px' : '200px',
                  padding: snapshot.isDraggingOver ? '8px' : '0',
                  display: 'flex',
                  flexDirection: 'column',
                  // Add a subtle background when empty to indicate it's a drop area
                  backgroundColor: sortedTasks.length === 0 ? 'rgba(var(--primary-rgb), 0.02)' : '',
                }}
              >
                {sortedTasks.map((task, index) => (
                  <TodoTask
                    key={task.id}
                    task={task}
                    index={index}
                    isBeingDragged={activeDragId === task.id}
                  />
                ))}
                {provided.placeholder}
                {/* Add an empty space at the bottom to allow dropping when column is empty or at the end */}
                <div className="flex-grow min-h-[50px]" />
              </div>
            )}
          </Droppable>
        </CardContent>
      </Card>

      <Dialog open={isEditing} onOpenChange={setIsEditing}>
        {/* Theme-aware Dialog Content */}
        <DialogContent className="bg-background border-border text-foreground sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Column</DialogTitle>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="title" className="text-sm font-medium">
                Title
              </label>
              {/* Theme-aware Input */}
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Column title"
                className="bg-muted border-border"
              />
            </div>
          </div>

          <DialogFooter className="flex justify-between">
            {!isDefaultColumn && (
              <Button variant="destructive" onClick={handleDelete}>
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </Button>
            )}
            {/* Primary button (styles likely ok) */}
            <Button onClick={handleSave}>Save changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
