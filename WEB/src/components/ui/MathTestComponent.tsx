import React from 'react';
import { But<PERSON> } from './button';
import { ChatMessage } from '../ChatMessage';

const mathSamples = [
  {
    title: "Inline Math",
    content: "The Pythagorean theorem states that $a^2 + b^2 = c^2$ for a right triangle."
  },
  {
    title: "Display Math",
    content: "The quadratic formula is:\n$$x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$$"
  },
  {
    title: "Math with Alignment",
    content: "Solving the equation step by step:\n$$\\begin{align} x + 3 &= 5 \\\\ x &= 5 - 3 \\\\ x &= 2 \\end{align}$$"
  },
  {
    title: "System of Equations",
    content: "$$\\begin{cases} 3x + 2y = 5 \\\\ x - y = 1 \\end{cases}$$"
  },
  {
    title: "Matrix",
    content: "$$\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}$$"
  },
  {
    title: "Fractions and Integrals",
    content: "$$\\int_{0}^{\\infty} \\frac{x^2}{e^x - 1} dx = \\frac{\\pi^4}{15}$$"
  },
  {
    title: "Alternative Notation - Parentheses",
    content: "Using TeX parentheses: \\(E = mc^2\\)"
  },
  {
    title: "Alternative Notation - Brackets",
    content: "Using TeX brackets: \\[F = G\\frac{m_1 m_2}{r^2}\\]"
  },
  {
    title: "Alternative Notation - Math Tags",
    content: "Using math tags: <math>\\sum_{i=1}^{n} i = \\frac{n(n+1)}{2}</math>"
  },
  {
    title: "Chemistry Equations",
    content: "Balanced chemical equation: $$\\ce{2H2 + O2 -> 2H2O}$$"
  }
];

const MathTestComponent = () => {
  const headingSamples = [
    {
      title: "Basic Headings",
      content: `## Physics
Here's a physics concept.
### Newton's Laws
1. First Law
2. Second Law
3. Third Law`
    },
    {
      title: "Headings with Math",
      content: `## Quantum Mechanics
The Schrödinger equation: $$i\\hbar\\frac{\\partial}{\\partial t}\\Psi = \\hat{H}\\Psi$$
### Wave Function
The wave function $\\Psi$ represents the quantum state.`
    },
    {
      title: "Complex Layout",
      content: `## Chemistry
### Organic Chemistry
#### Alkanes
Basic hydrocarbons with single bonds.
### Physical Chemistry
#### Thermodynamics
The study of heat and energy.`
    }
  ];

  return (
    <div className="space-y-8 p-4">
      <h2 className="text-2xl font-bold mb-4">Heading Rendering Test</h2>

      <div className="space-y-8">
        {headingSamples.map((sample, index) => (
          <div key={index} className="border rounded-lg p-4 bg-card">
            <h3 className="font-medium mb-2">{sample.title}</h3>
            <div className="text-sm bg-muted p-2 rounded mb-2 font-mono">
              {sample.content}
            </div>
            <p className="text-sm text-muted-foreground mb-4">Rendered Output:</p>
            <ChatMessage
              content={sample.content}
              isUser={false}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default MathTestComponent;
