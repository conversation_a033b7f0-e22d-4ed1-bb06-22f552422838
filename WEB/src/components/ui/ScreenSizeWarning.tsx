import { useState, useEffect } from 'react';
import { AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

export function ScreenSizeWarning() {
  const [showWarning, setShowWarning] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setShowWarning(window.innerWidth < 1024); // Show warning for screens smaller than 1024px (lg breakpoint)
    };

    // Check on mount
    checkScreenSize();

    // Add resize listener
    window.addEventListener('resize', checkScreenSize);

    // Cleanup
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  if (!showWarning) return null;

  return (
    <Alert variant="destructive" className="fixed top-20 right-4 z-50 max-w-md bg-red-500/10 border-red-500/20">
      <AlertCircle className="h-4 w-4" />
      <AlertDescription>
        This page is optimized for larger screens. For the best experience, please view on a desktop or tablet device.
      </AlertDescription>
    </Alert>
  );
} 