"use client"

import * as React from "react"
import { useState, useRef, useCallback } from "react"
import { uploadImageToCloudinary } from "../../utils/imageUtils"
import { Button } from "./button"
import { UploadCloud, X } from "lucide-react"
import { cn } from "../../lib/utils"

export interface ImageUploadProps {
  onImageUploaded?: (imageUrl: string, publicId: string) => void
  onError?: (error: Error) => void
  maxFileSize?: number // in bytes
  acceptedFileTypes?: string[] // e.g. ['image/jpeg', 'image/png']
  folder?: string
  className?: string
  buttonText?: string
  multiple?: boolean
  showPreview?: boolean
  previewClassName?: string
  disabled?: boolean
  isLoading?: boolean
}

type ImageUploadComponentProps = ImageUploadProps & Omit<React.HTMLAttributes<HTMLDivElement>, 'onError'>;

export function ImageUpload({
  onImageUploaded,
  onError,
  maxFileSize = 5 * 1024 * 1024, // Default 5MB
  acceptedFileTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  folder,
  className,
  buttonText = "Upload Image",
  multiple = false,
  showPreview = true,
  previewClassName,
  disabled = false,
  isLoading = false,
  ...props
}: ImageUploadComponentProps) {
  const [preview, setPreview] = useState<string | null>(null)
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    const file = files[0]
    
    // Debug file info
    console.log('Selected file:', file.name, 'type:', file.type, 'size:', file.size);
    
    // Validate file type
    if (!acceptedFileTypes.includes(file.type)) {
      const error = new Error(`Invalid file type. Accepted types: ${acceptedFileTypes.join(', ')}`)
      setError(error.message)
      onError?.(error)
      return
    }

    // Validate file size
    if (file.size > maxFileSize) {
      const error = new Error(`File too large. Maximum size: ${maxFileSize / (1024 * 1024)}MB`)
      setError(error.message)
      onError?.(error)
      return
    }

    // Show preview
    if (showPreview) {
      const reader = new FileReader()
      reader.onload = (e) => {
        setPreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }

    // Upload to Cloudinary
    try {
      setUploading(true)
      setError(null)
      
      // Debug Cloudinary config
      console.log('Cloudinary config:', { 
        cloudName: import.meta.env.VITE_CLOUDINARY_CLOUD_NAME,
        uploadPreset: import.meta.env.VITE_CLOUDINARY_UPLOAD_PRESET,
        folder
      });
      
      const response = await uploadImageToCloudinary(file, { 
        folder, 
        maxFileSize,
      })
      
      // Debug successful response
      console.log('Cloudinary upload successful:', response);
      
      // Call the callback with the image URL
      onImageUploaded?.(response.secure_url, response.public_id)
      
      setUploading(false)
    } catch (err) {
      setUploading(false)
      console.error('Detailed upload error:', err);
      const error = err instanceof Error ? err : new Error('Failed to upload image')
      setError(error.message)
      onError?.(error)
    }
  }

  const handleButtonClick = useCallback(() => {
    fileInputRef.current?.click()
  }, [])

  const clearPreview = useCallback(() => {
    setPreview(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [])

  return (
    <div className={cn("flex flex-col gap-2", className)} {...props}>
      <input
        type="file"
        accept={acceptedFileTypes.join(',')}
        onChange={handleFileChange}
        ref={fileInputRef}
        style={{ display: 'none' }}
        multiple={multiple}
        disabled={disabled || uploading || isLoading}
      />
      
      <Button
        type="button"
        onClick={handleButtonClick}
        disabled={disabled || uploading || isLoading}
        variant="outline"
        className={cn("flex items-center gap-2", {
          "opacity-50 cursor-not-allowed": disabled || uploading || isLoading,
        })}
      >
        {uploading || isLoading ? (
          <div className="animate-spin mr-2">
            <UploadCloud className="h-4 w-4" />
          </div>
        ) : (
          <UploadCloud className="h-4 w-4" />
        )}
        {uploading ? "Uploading..." : buttonText}
      </Button>
      
      {error && (
        <p className="text-sm text-red-500 mt-1">{error}</p>
      )}
      
      {showPreview && preview && (
        <div className={cn("relative mt-2 rounded-md overflow-hidden", previewClassName)}>
          <Button
            type="button"
            variant="destructive"
            size="icon"
            className="absolute top-2 right-2 h-6 w-6 rounded-full"
            onClick={clearPreview}
          >
            <X className="h-3 w-3" />
          </Button>
          <img
            src={preview}
            alt="Upload preview"
            className="w-full h-auto object-contain"
          />
        </div>
      )}
    </div>
  )
} 