// Shared color palette for subjects across all components
// This ensures consistent colors between StudyTimer, Analytics, and other components
export const SUBJECT_COLORS = [
  "#4f46e5", // Indigo
  "#ec4899", // Pink
  "#06b6d4", // <PERSON><PERSON>
  "#f97316", // Orange
  "#10b981", // Emerald
  "#8b5cf6", // Violet
  "#f43f5e", // <PERSON>
  "#0ea5e9", // Sky
  "#84cc16", // Lime
  "#14b8a6", // Teal
  "#d946ef", // Fuchsia
  "#f59e0b"  // Amber
];

// Helper function to get a color by index (with wraparound)
export const getSubjectColor = (index: number): string => {
  return SUBJECT_COLORS[index % SUBJECT_COLORS.length];
};

// Helper function to get a random color from the palette
export const getRandomSubjectColor = (): string => {
  return SUBJECT_COLORS[Math.floor(Math.random() * SUBJECT_COLORS.length)];
};
