import { useEffect } from 'react';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { updateUserLastActive } from '@/utils/supabaseDb';
import { throttle } from 'lodash';

/**
 * Hook to track user activity and update lastActive timestamp
 * This helps with the inactive user cleanup feature by keeping
 * track of when users are actively using the application
 */
export function useActivityTracker() {
  const { user } = useSupabaseAuth();

  useEffect(() => {
    if (!user) return;

    // Update last active timestamp on component mount
    updateUserLastActive(user.id).catch(err => 
      console.error('Error updating lastActive on mount:', err)
    );

    // Throttled function to prevent too many database writes
    const throttledUpdateLastActive = throttle(() => {
      if (user) {
        updateUserLastActive(user.id).catch(err => 
          console.error('Error updating lastActive:', err)
        );
      }
    }, 5 * 60 * 1000); // Update at most every 5 minutes

    // Create handlers for various user interactions
    const handleActivity = () => throttledUpdateLastActive();
    
    // Attach event listeners
    window.addEventListener('mousemove', handleActivity);
    window.addEventListener('keydown', handleActivity);
    window.addEventListener('click', handleActivity);
    window.addEventListener('touchstart', handleActivity);
    window.addEventListener('scroll', handleActivity);

    // Clean up event listeners on unmount
    return () => {
      throttledUpdateLastActive.cancel();
      window.removeEventListener('mousemove', handleActivity);
      window.removeEventListener('keydown', handleActivity);
      window.removeEventListener('click', handleActivity);
      window.removeEventListener('touchstart', handleActivity);
      window.removeEventListener('scroll', handleActivity);
    };
  }, [user]);
}

export default useActivityTracker;
