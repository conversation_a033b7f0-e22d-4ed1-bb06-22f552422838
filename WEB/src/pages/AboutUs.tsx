import { Link } from 'react-router-dom';
import { ArrowLeft, Users, Lightbulb, Target, Award } from 'lucide-react';
import { Helmet } from 'react-helmet';
import { Head<PERSON>, Footer } from '@/components/shared';
import { motion } from 'framer-motion';
import { useEffect } from 'react';

const AboutUs = () => {
  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="min-h-screen bg-[#030303] text-white/90 font-onest">
      <Helmet>
        <title>About Us - IsotopeAI</title>
        <meta
          name="description"
          content="Learn about IsotopeAI - Our mission, team, and vision for revolutionizing education with AI."
        />
      </Helmet>

      {/* Header */}
      <Header />

      <div className="container mx-auto px-4 py-32 max-w-4xl">
        <div className="mb-8">
          <Link to="/" className="inline-flex items-center text-violet-400 hover:text-violet-300 transition-colors">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Home
          </Link>
        </div>

        <div className="space-y-8">
          <div>
            <h1 className="text-3xl md:text-4xl font-bold mb-4">About IsotopeAI</h1>
            <p className="text-white/60 text-lg">Revolutionizing education through AI-powered learning</p>
          </div>

          <div className="prose prose-invert max-w-none">
            <p className="text-lg">
              IsotopeAI is an educational technology platform designed to help students master Physics, Chemistry, and Mathematics through personalized AI assistance, collaborative learning, and effective study tools.
            </p>

            <motion.div
              className="grid grid-cols-1 md:grid-cols-2 gap-8 my-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="bg-white/5 p-6 rounded-xl border border-white/10">
                <div className="flex items-center mb-4">
                  <div className="p-3 bg-violet-500/20 rounded-lg mr-4">
                    <Lightbulb className="h-6 w-6 text-violet-400" />
                  </div>
                  <h3 className="text-xl font-semibold">Our Mission</h3>
                </div>
                <p className="text-white/80">
                  To make high-quality education accessible to all students by leveraging the power of artificial intelligence to provide personalized learning experiences.
                </p>
              </div>

              <div className="bg-white/5 p-6 rounded-xl border border-white/10">
                <div className="flex items-center mb-4">
                  <div className="p-3 bg-violet-500/20 rounded-lg mr-4">
                    <Target className="h-6 w-6 text-violet-400" />
                  </div>
                  <h3 className="text-xl font-semibold">Our Vision</h3>
                </div>
                <p className="text-white/80">
                  To create a world where every student has access to the tools and support they need to excel in their studies, regardless of their background or resources.
                </p>
              </div>
            </motion.div>

            <h2 className="text-2xl font-semibold mt-12 mb-6">What Makes Us Different</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
              <motion.div
                className="bg-white/5 p-6 rounded-xl border border-white/10"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <div className="flex items-center mb-3">
                  <div className="p-2 bg-violet-500/20 rounded-lg mr-3">
                    <Award className="h-5 w-5 text-violet-400" />
                  </div>
                  <h3 className="text-lg font-semibold">AI-Powered Learning</h3>
                </div>
                <p className="text-white/80">
                  Our advanced AI assistant is specifically trained on Physics, Chemistry, and Mathematics to provide accurate and helpful responses to your academic questions.
                </p>
              </motion.div>

              <motion.div
                className="bg-white/5 p-6 rounded-xl border border-white/10"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <div className="flex items-center mb-3">
                  <div className="p-2 bg-violet-500/20 rounded-lg mr-3">
                    <Award className="h-5 w-5 text-violet-400" />
                  </div>
                  <h3 className="text-lg font-semibold">Collaborative Study Groups</h3>
                </div>
                <p className="text-white/80">
                  Connect with peers, share resources, and learn together in our collaborative study groups feature.
                </p>
              </motion.div>

              <motion.div
                className="bg-white/5 p-6 rounded-xl border border-white/10"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <div className="flex items-center mb-3">
                  <div className="p-2 bg-violet-500/20 rounded-lg mr-3">
                    <Award className="h-5 w-5 text-violet-400" />
                  </div>
                  <h3 className="text-lg font-semibold">Productivity Tools</h3>
                </div>
                <p className="text-white/80">
                  Our integrated productivity features help you manage your study time effectively and track your progress.
                </p>
              </motion.div>

              <motion.div
                className="bg-white/5 p-6 rounded-xl border border-white/10"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <div className="flex items-center mb-3">
                  <div className="p-2 bg-violet-500/20 rounded-lg mr-3">
                    <Award className="h-5 w-5 text-violet-400" />
                  </div>
                  <h3 className="text-lg font-semibold">Task Management</h3>
                </div>
                <p className="text-white/80">
                  Stay organized with our task management system designed specifically for students.
                </p>
              </motion.div>
            </div>

            <h2 className="text-2xl font-semibold mt-12 mb-6">Our Team</h2>
            <p className="text-white/80">
              IsotopeAI was founded by a team of passionate educators, developers, and students who believe in the power of technology to transform education. Our diverse team brings together expertise in artificial intelligence, educational psychology, and subject matter knowledge to create a platform that truly meets the needs of students.
            </p>

            <div className="mt-12 p-6 bg-white/5 rounded-xl border border-white/10">
              <h3 className="text-xl font-semibold mb-4">Join Our Journey</h3>
              <p className="text-white/80">
                We're constantly evolving and improving IsotopeAI based on user feedback and the latest advancements in AI and education. We invite you to be part of our journey by using our platform, providing feedback, and sharing your success stories.
              </p>
              <div className="mt-6 flex flex-wrap gap-4">
                <Link to="/contact-us" className="inline-flex items-center px-4 py-2 bg-violet-600 hover:bg-violet-700 rounded-md transition-colors">
                  Contact Us
                </Link>
                <a href="https://www.instagram.com/isotope.ai/" target="_blank" rel="noopener noreferrer" className="inline-flex items-center px-4 py-2 bg-white/10 hover:bg-white/20 rounded-md transition-colors">
                  Follow Us on Instagram
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default AboutUs;
