import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Helmet } from 'react-helmet';
import { Head<PERSON>, Footer } from '@/components/shared';
import { AdPlaceholder } from '@/components/ads';

const Ads = () => {
  return (
    <div className="min-h-screen bg-[#030303] text-white/90 font-onest">
      <Helmet>
        <title>Advertising Policy - IsotopeAI</title>
        <meta
          name="description"
          content="IsotopeAI's Advertising Policy - Learn about how we display advertisements on our platform."
        />
        <meta name="robots" content="index, follow" />
        <link rel="canonical" href="https://isotopeai.com/ads" />
        
        {/* Ad-related meta tags */}
        <meta name="format-detection" content="telephone=no" />
        <meta name="ad-client" content="ca-pub-9602732057654649" />
        <meta name="ad-format" content="auto" />
      </Helmet>

      {/* Header */}
      <Header />

      <div className="container mx-auto px-4 py-32 max-w-4xl">
        <div className="mb-8">
          <Link to="/" className="inline-flex items-center text-violet-400 hover:text-violet-300 transition-colors">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Home
          </Link>
        </div>

        <div className="space-y-8">
          <div>
            <h1 className="text-3xl md:text-4xl font-bold mb-4">Advertising Policy</h1>
            <p className="text-white/60">Last Updated: {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
          </div>

          <div className="prose prose-invert max-w-none">
            <p>
              At IsotopeAI, we display advertisements to help support our platform and continue providing high-quality educational services. This policy explains how we handle advertisements on our platform.
            </p>

            <h2 className="text-xl font-semibold mt-6 mb-3">Types of Advertisements</h2>
            <p>We display various types of advertisements on our platform, including:</p>
            <ul className="list-disc pl-6 space-y-2 text-white/80">
              <li>Display ads (banners, rectangles, etc.)</li>
              <li>Text ads</li>
              <li>Responsive ads that adjust to different screen sizes</li>
            </ul>

            <p className="mt-4">
              We primarily use Google AdSense to serve advertisements on our platform. These advertisements are selected based on various factors, including:
            </p>
            <ul className="list-disc pl-6 space-y-2 text-white/80">
              <li>The content of the page you are viewing</li>
              <li>Your geographic location</li>
              <li>Your browsing history and interests (if you have not opted out of personalized advertising)</li>
            </ul>

            <h2 className="text-xl font-semibold mt-6 mb-3">Ad Placement</h2>
            <p>
              We strive to place advertisements in a way that minimizes disruption to your experience while using our platform. Advertisements may appear:
            </p>
            <ul className="list-disc pl-6 space-y-2 text-white/80">
              <li>At the top, bottom, or sides of pages</li>
              <li>Between sections of content</li>
              <li>In designated ad spaces throughout the platform</li>
            </ul>

            <div className="my-8">
              <h3 className="text-lg font-semibold mb-3">Example Ad Placement:</h3>
              <AdPlaceholder format="rectangle" className="mx-auto" />
            </div>

            <h2 className="text-xl font-semibold mt-6 mb-3">Ad Content Standards</h2>
            <p>
              While we do not directly control the content of advertisements displayed through Google AdSense, we have set certain category restrictions to ensure that advertisements are appropriate for our educational platform. We strive to prevent advertisements that:
            </p>
            <ul className="list-disc pl-6 space-y-2 text-white/80">
              <li>Contain adult or inappropriate content</li>
              <li>Promote harmful products or services</li>
              <li>Spread misinformation</li>
              <li>Are deceptive or misleading</li>
            </ul>

            <h2 className="text-xl font-semibold mt-6 mb-3">Your Ad Choices</h2>
            <p>
              You have control over the personalized advertisements you see. You can opt out of personalized advertising by:
            </p>
            <ul className="list-disc pl-6 space-y-2 text-white/80">
              <li>Visiting <a href="https://www.google.com/settings/ads" target="_blank" rel="noopener noreferrer" className="text-violet-400 hover:text-violet-300">Google Ads Settings</a></li>
              <li>Using the AdChoices option that appears on some advertisements</li>
              <li>Adjusting your browser settings to block or limit cookies</li>
            </ul>

            <h2 className="text-xl font-semibold mt-6 mb-3">Feedback</h2>
            <p>
              If you encounter an advertisement that you believe is inappropriate or disruptive, please contact us at <a href="mailto:<EMAIL>" className="text-violet-400 hover:text-violet-300"><EMAIL></a> with details about the advertisement.
            </p>

            <h2 className="text-xl font-semibold mt-6 mb-3">Changes to This Policy</h2>
            <p>
              We may update this Advertising Policy periodically. We will notify you of any changes by posting the new policy on this page and updating the "Last Updated" date.
            </p>

            <h2 className="text-xl font-semibold mt-6 mb-3">Contact Us</h2>
            <p>
              If you have any questions about our Advertising Policy, please contact us at <a href="mailto:<EMAIL>" className="text-violet-400 hover:text-violet-300"><EMAIL></a>.
            </p>
          </div>
        </div>
      </div>

      {/* Bottom Ad Banner */}
      <div className="container mx-auto px-4 my-8">
        <AdPlaceholder format="horizontal" className="mx-auto" />
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default Ads;
