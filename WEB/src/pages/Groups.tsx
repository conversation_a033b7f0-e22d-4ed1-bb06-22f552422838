import { GroupDetails } from "@/components/chat/GroupDetails"
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext"
import { useEffect, useState, useRef } from "react"
import { useNavigate } from "react-router-dom"
import { useChatStore, ChatGroup } from "@/stores/chatStore"
import { useBackgroundTheme } from "@/contexts/BackgroundThemeContext"
import { useDocumentTitle } from "@/hooks/useDocumentTitle"
import { Users, UserPlus, MessageSquare, ArrowLeft, Globe, Lock, PlusCircle, Clock, CalendarClock, Search, Filter, TagIcon, X, Settings2 } from "lucide-react"
import { Button } from "@/components/ui/button"

import { SmallDeviceWarning } from '@/components/ui/SmallDeviceWarning'
import { FeedbackWidget } from '@/components/FeedbackWidget'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
// Removed Firebase imports - now using Supabase
import { CreateGroup } from "@/components/chat/CreateGroup"
import { JoinGroup } from "@/components/chat/JoinGroup"
import Header from "@/components/shared/Header"
import { getGroups, getPublicGroups, getGroupStats, joinGroup, GroupStats as SupabaseGroupStats } from "@/utils/supabase"

// Extended group interface with statistics
interface GroupWithStats extends ChatGroup {
  stats: SupabaseGroupStats;
}
import { TasksDropdown } from "@/components/productivity/TasksDropdown"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

// Add animation keyframes and utility classes for the animated gradient orbs
const animationStyles = `
  @keyframes blob {
    0% {
      transform: translate(0px, 0px) scale(1);
    }
    33% {
      transform: translate(30px, -50px) scale(1.1);
    }
    66% {
      transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
      transform: translate(0px, 0px) scale(1);
    }
  }
  .animate-blob {
    animation: blob 7s infinite;
  }
  .animation-delay-2000 {
    animation-delay: 2s;
  }
  .animation-delay-4000 {
    animation-delay: 4s;
  }
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  .animate-fadeIn {
    animation: fadeIn 0.8s ease-out forwards;
  }
`;



// Note: GroupWithStats is already defined above with SupabaseGroupStats

// Define a type for public groups with activity level
interface PublicGroup {
  id: string;
  name: string;
  description: string;
  memberCount: number;
  ownerId: string;
  createdAt: Date;
  tags: string[];
  isUserMember: boolean;
  totalStudyTime: number;
  activityLevel: 'high' | 'medium' | 'low';
}

export default function Groups() {
  useDocumentTitle("Study Groups - IsotopeAI");
  const { user, loading: authLoading } = useSupabaseAuth()
  const navigate = useNavigate()
  const { groups, setGroups } = useChatStore()
  const { getBackgroundStyle } = useBackgroundTheme()
  const [activeTab, setActiveTab] = useState("myGroups")
  const contentRef = useRef<HTMLDivElement>(null)
  const [isCreateOpen, setIsCreateOpen] = useState(false)
  const [isJoinOpen, setIsJoinOpen] = useState(false)
  const [publicGroups, setPublicGroups] = useState<PublicGroup[]>([])
  const [filteredPublicGroups, setFilteredPublicGroups] = useState<PublicGroup[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [availableTags, setAvailableTags] = useState<string[]>([])
  const [joiningGroupId, setJoiningGroupId] = useState<string | null>(null)
  const { toast } = useToast()
  const [selectedGroupId, setSelectedGroupId] = useState<string | null>(null)
  const [isLoadingGroups, setIsLoadingGroups] = useState(true)
  const [isLoadingPublicGroups, setIsLoadingPublicGroups] = useState(false)
  // Task state is now handled in the TasksDropdown component

  useEffect(() => {
    if (!authLoading && !user) {
      navigate('/login')
    }
  }, [user, authLoading, navigate])

  // Fetch user's groups from Supabase with statistics
  useEffect(() => {
    if (!user) return

    const loadGroupsWithStats = async () => {
      setIsLoadingGroups(true)
      try {
        console.log("Fetching user's groups...")
        const userGroups = await getGroups(user.id)

        // Load statistics for each group
        const groupsWithStats = await Promise.all(
          userGroups.map(async (group) => {
            const stats = await getGroupStats(group.id)
            // Convert totalStudyTime from seconds to minutes for display
            const convertedStats = {
              ...stats,
              totalStudyTime: stats.totalStudyTime / 60
            };
            return {
              id: group.id,
              name: group.name,
              description: group.description || '',
              members: group.members || [],
              createdAt: group.createdAt,
              createdBy: group.createdBy,
              owner_id: group.owner_id,
              inviteCode: group.inviteCode || '',
              isPublic: group.isPublic || false,
              lastMessage: group.last_message ? {
                id: (group.last_message as any)?.id || '',
                content: (group.last_message as any)?.content || '',
                senderId: (group.last_message as any)?.senderId || '',
                timestamp: (group.last_message as any)?.timestamp || Date.now(),
                groupId: group.id
              } : undefined,
              stats: convertedStats
            } as GroupWithStats
          })
        )

        setGroups(groupsWithStats)
      } catch (error) {
        console.error('Error loading groups:', error)
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load groups"
        })
      } finally {
        setIsLoadingGroups(false)
      }
    }

    loadGroupsWithStats()
  }, [user, setGroups, toast]);

  // Fetch public groups when the Discover tab is active
  useEffect(() => {
    if (activeTab === "discover" && user) {
      fetchPublicGroups()
    }
  }, [activeTab, user])

  // Debug log to check groups
  useEffect(() => {
    console.log("Current groups in state:", groups)
  }, [groups])

  // TODO: Implement Supabase group stats fetching

  // Helper function to format time duration (input is in minutes)
  const formatStudyTime = (minutes: number): string => {
    if (!minutes) return '0h';

    const hours = Math.floor(minutes / 60);
    const remainingMinutes = Math.floor(minutes % 60);

    if (hours > 0) {
      return `${hours}h${remainingMinutes > 0 ? ` ${remainingMinutes}m` : ''}`;
    }
    return `${remainingMinutes}m`;
  };

  // Helper function to format relative time
  const getRelativeTimeString = (date: Date | null): string => {
    if (!date) return 'Never';
    
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    return `${Math.floor(diffInDays / 30)} months ago`;
  };

  const fetchPublicGroups = async () => {
    if (!user) return

    setIsLoadingPublicGroups(true)
    try {
      const publicGroupsData = await getPublicGroups(user.id)

      // Transform to PublicGroup format with stats
      const publicGroupsWithStats = await Promise.all(
        publicGroupsData.map(async (group) => {
          const stats = await getGroupStats(group.id)

          // Determine activity level based on last active date
          const activityLevel: 'high' | 'medium' | 'low' = (() => {
            if (!stats.lastActiveDate) return 'low'
            const daysSinceActive = Math.floor((Date.now() - stats.lastActiveDate.getTime()) / (1000 * 60 * 60 * 24))
            if (daysSinceActive <= 1) return 'high'
            if (daysSinceActive <= 7) return 'medium'
            return 'low'
          })()

          return {
            id: group.id,
            name: group.name,
            description: group.description || '',
            memberCount: (group.members || []).length,
            ownerId: group.owner_id || group.createdBy,
            createdAt: new Date(group.createdAt || Date.now()),
            tags: [], // TODO: Add tags support to groups table
            isUserMember: false, // Already filtered out in getPublicGroups
            totalStudyTime: stats.totalStudyTime / 60, // Convert to minutes for display
            activityLevel
          } as PublicGroup
        })
      )

      setPublicGroups(publicGroupsWithStats)
      setFilteredPublicGroups(publicGroupsWithStats)

      // Extract unique tags (when tags are implemented)
      const allTags = publicGroupsWithStats.flatMap(group => group.tags)
      setAvailableTags([...new Set(allTags)])
    } catch (error) {
      console.error('Error fetching public groups:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load public groups"
      })
    } finally {
      setIsLoadingPublicGroups(false)
    }
  }

  // Helper function to format study time for group display (input is in minutes)
  const formatGroupStudyTime = (minutes: number): string => {
    if (!minutes) return '0h';

    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours} ${hours === 1 ? 'hour' : 'hours'}`;
    }
    return 'Less than 1 hour';
  };

  // Apply filters when search query or tags change
  useEffect(() => {
    if (!publicGroups.length) return;
    
    const filtered = publicGroups.filter((group: PublicGroup) => {
      // Apply search query filter
      const matchesSearch = searchQuery === "" || 
        group.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        group.description.toLowerCase().includes(searchQuery.toLowerCase());
      
      // Apply tags filter
      const matchesTags = selectedTags.length === 0 || 
        selectedTags.some(tag => group.tags.includes(tag));
      
      return matchesSearch && matchesTags;
    });
    
    setFilteredPublicGroups(filtered);
  }, [searchQuery, selectedTags, publicGroups]);

  // Toggle tag selection
  const toggleTag = (tag: string) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter(t => t !== tag));
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchQuery("");
    setSelectedTags([]);
  };

  const handleJoinPublicGroup = async (groupId: string) => {
    if (!user) return

    setJoiningGroupId(groupId)
    try {
      // Join the group using Supabase
      await joinGroup(groupId, user.id)

      // Remove from public groups list
      setPublicGroups(publicGroups.filter(g => g.id !== groupId))
      setFilteredPublicGroups(filteredPublicGroups.filter(g => g.id !== groupId))

      toast({
        title: "Success",
        description: "You have joined the group"
      })

      // Switch to My Groups tab and refresh groups
      setActiveTab("myGroups")

      // Refresh user's groups to include the newly joined group
      const userGroups = await getGroups(user.id)
      const groupsWithStats = await Promise.all(
        userGroups.map(async (group) => {
          const stats = await getGroupStats(group.id)
          return {
            id: group.id,
            name: group.name,
            description: group.description || '',
            members: group.members || [],
            createdAt: group.createdAt,
            createdBy: group.createdBy,
            owner_id: group.owner_id,
            inviteCode: group.inviteCode || '',
            isPublic: group.isPublic || false,
            lastMessage: group.last_message ? {
              id: (group.last_message as any)?.id || '',
              content: (group.last_message as any)?.content || '',
              senderId: (group.last_message as any)?.senderId || '',
              timestamp: (group.last_message as any)?.timestamp || Date.now(),
              groupId: group.id
            } : undefined,
            stats
          } as GroupWithStats
        })
      )
      setGroups(groupsWithStats)

    } catch (error) {
      console.error('Error joining public group:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to join group. Please try again."
      })
    } finally {
      setJoiningGroupId(null)
    }
  }



  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
      </div>
    )
  }

  return (
    // Apply theme-aware background and text color
    <div className="relative min-h-screen w-full bg-background dark:bg-gradient-to-b dark:from-[#0f172a] dark:to-[#1e293b] text-foreground overflow-hidden">
      <SmallDeviceWarning />

      {/* Inject animation styles */}
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />

      {/* Background pattern overlay (Dark mode only) */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiMyMDIwMjAiIGZpbGwtb3BhY2l0eT0iMC4wNSI+PHBhdGggZD0iTTM2IDM0djZoNnYtNmgtNnptNiA2djZoNnYtNmgtNnptLTEyIDBoNnY2aC02di02em0xMiAwaDZ2NnYtNnoiLz48cGF0aCBkPSJNMTIgMzZ2Nmg2di02aC02em0wIDZoNnY2aC02di02em0wLTEyaDZ2NnYtNnoiLz48L2c+PC9nPjwvc3ZnPg==')] dark:block hidden"></div>

      {/* Gradient overlay (Dark mode only) */}
      <div className="absolute inset-0 bg-gradient-to-tr from-indigo-900/30 via-transparent to-pink-900/20 dark:block hidden"></div>

      {/* Background image/gradient overlay (Dark mode only) */}
      <div className={`absolute inset-0 ${getBackgroundStyle()} opacity-30 dark:block hidden`} />

      {/* Animated gradient orbs (Dark mode only) */}
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-indigo-600/20 rounded-full filter blur-[100px] animate-blob dark:block hidden"></div>
      <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-pink-600/20 rounded-full filter blur-[100px] animate-blob animation-delay-2000 dark:block hidden"></div>
      <div className="absolute bottom-1/4 left-1/2 w-96 h-96 bg-cyan-600/20 rounded-full filter blur-[100px] animate-blob animation-delay-4000 dark:block hidden"></div>

      {/* Import shared Header component */}
      <Header />

      {/* Draggable Tasks Dropdown */}
      <TasksDropdown
        groupId={selectedGroupId || undefined}
      />

      {/* Main content */}
      <div className="relative z-10 min-h-screen">
        <div className="max-w-[1400px] mx-auto px-4 sm:px-8 pt-32 pb-24" ref={contentRef}>
          {/* Tasks are now shown in a hover menu via TasksDropdown */}
          {selectedGroupId ? (
            <div className="flex items-center mb-8">
              <Button
                variant="ghost"
                size="icon"
                className="h-9 w-9 rounded-lg bg-secondary/50 dark:bg-white/5 hover:bg-secondary dark:hover:bg-white/10 border border-border dark:border-white/10 text-muted-foreground dark:text-white mr-4"
                onClick={() => setSelectedGroupId(null)}
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
                Group Details
              </h1>
            </div>
          ) : (
            <div className="flex justify-between items-center mb-8">
              {/* Theme-aware title */}
              <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
                Your Groups
              </h1>
            </div>
          )}

          <Tabs
            value={activeTab}
            defaultValue="myGroups"
            className="w-full"
            onValueChange={(value) => {
              console.log('Tab value changed to:', value);
              setActiveTab(value);
            }}
          >
            <div className="flex justify-center mb-8 overflow-x-auto pb-2 relative">
              {/* Theme-aware TabsList */}
              <TabsList className="bg-muted dark:bg-gradient-to-r dark:from-slate-800/60 dark:to-slate-900/60 p-1.5 gap-1 inline-flex flex-wrap justify-center w-full max-w-[600px] mx-auto sm:flex-nowrap rounded-xl border border-border dark:border-white/5 shadow-xl backdrop-blur-xl">
                {/* Theme-aware TabsTrigger (inactive state) */}
                <TabsTrigger
                  value="myGroups"
                  className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-600/90 data-[state=active]:to-indigo-700/90 data-[state=active]:text-white text-muted-foreground dark:text-white/80 text-xs sm:text-sm px-3 py-2 sm:px-4 sm:py-2.5 flex-1 rounded-lg transition-all duration-200"
                >
                  My Groups
                </TabsTrigger>
                {/* Theme-aware TabsTrigger (inactive state) */}
                <TabsTrigger
                  value="discover"
                  className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-pink-600/90 data-[state=active]:to-purple-700/90 data-[state=active]:text-white text-muted-foreground dark:text-white/80 text-xs sm:text-sm px-3 py-2 sm:px-4 sm:py-2.5 flex-1 rounded-lg transition-all duration-200"
                >
                  Discover
                </TabsTrigger>
                {/* Theme-aware TabsTrigger (inactive state) */}
                <TabsTrigger
                  value="joinCreate"
                  className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-600/90 data-[state=active]:to-indigo-700/90 data-[state=active]:text-white text-muted-foreground dark:text-white/80 text-xs sm:text-sm px-3 py-2 sm:px-4 sm:py-2.5 flex-1 rounded-lg transition-all duration-200"
                >
                  Join/Create
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="myGroups" data-tab="myGroups">
              {selectedGroupId ? (
                /* Show group details when a group is selected */
                <Card className="bg-card dark:bg-gradient-to-br dark:from-slate-800/60 dark:to-slate-900/60 border border-border dark:border-white/5 backdrop-blur-xl rounded-xl shadow-xl overflow-hidden">
                  <CardHeader className="border-b border-border dark:border-white/5 pb-4">
                    <CardTitle className="text-lg font-medium flex items-center gap-2 text-card-foreground">
                      <Users className="h-5 w-5 text-indigo-400" />
                      <span>Group Details</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-6">
                    <GroupDetails groupId={selectedGroupId} />
                  </CardContent>
                </Card>
              ) : (
                /* Show grid of group cards */
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {/* Loading state */}
                  {isLoadingGroups ? (
                    // Show skeleton loaders when loading
                    <>
                      {[1, 2, 3, 4].map((i) => (
                        <div 
                          key={i} 
                          className="flex flex-col p-6 bg-card/50 dark:bg-slate-800/40 rounded-xl border border-border/50 dark:border-white/5 min-h-[200px] animate-pulse"
                        >
                          <div className="flex items-center justify-between mb-3">
                            <div className="w-24 h-5 bg-muted dark:bg-white/10 rounded"></div>
                            <div className="w-16 h-4 bg-indigo-500/20 rounded-full"></div>
                          </div>
                          <div className="w-full h-12 bg-muted dark:bg-white/5 rounded mb-3"></div>
                          <div className="mt-auto pt-3 border-t border-border/50 dark:border-white/5">
                            <div className="w-24 h-4 bg-muted dark:bg-white/10 rounded"></div>
                          </div>
                        </div>
                      ))}
                    </>
                  ) : groups && groups.length > 0 ? (
                    <>
                      {/* Group cards */}
                      {groups.map((group, index) => (
                        <div
                          key={group.id}
                          onClick={() => setSelectedGroupId(group.id)}
                          className={`flex flex-col p-6 bg-card dark:bg-gradient-to-br dark:from-slate-800/80 dark:to-slate-900/80 hover:bg-accent dark:hover:from-indigo-900/40 dark:hover:to-purple-900/40 rounded-xl border border-border dark:border-white/10 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-[1.02] cursor-pointer group animate-fadeIn overflow-hidden relative`}
                          style={{ animationDelay: `${index * 50}ms` }}
                        >
                          {/* Decorative accent line */}
                          <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-indigo-500 to-purple-500"></div>
                          
                          <div className="flex items-center justify-between mb-3">
                            <h3 className="text-lg font-medium text-card-foreground dark:group-hover:text-white transition-colors">{group.name}</h3>
                            <div className="text-xs bg-indigo-500/20 text-indigo-400 px-2 py-0.5 rounded-full flex items-center gap-1">
                              {group.isPublic ? (
                                <>
                                  <Globe className="h-3 w-3" />
                                  <span>Public</span>
                                </>
                              ) : (
                                <>
                                  <Lock className="h-3 w-3" />
                                  <span>Private</span>
                                </>
                              )}
                            </div>
                          </div>

                          <p className="text-sm text-muted-foreground dark:text-white/70 mb-4 line-clamp-2">{group.description || 'No description'}</p>

                          {/* Stats section */}
                          <div className="grid grid-cols-2 gap-3 mb-4">
                            <div className="bg-background/50 dark:bg-white/5 p-2 rounded-lg flex flex-col items-center">
                              <Clock className="h-4 w-4 text-indigo-400 mb-1" />
                              <span className="text-lg font-semibold text-foreground">{formatStudyTime((group as GroupWithStats).stats?.totalStudyTime || 0)}</span>
                              <span className="text-xs text-muted-foreground">Total Studied</span>
                            </div>
                            
                            <div className="bg-background/50 dark:bg-white/5 p-2 rounded-lg flex flex-col items-center">
                              <Users className="h-4 w-4 text-pink-400 mb-1" />
                              <span className="text-lg font-semibold text-foreground">{group.members.length}</span>
                              <span className="text-xs text-muted-foreground">Members</span>
                            </div>
                          </div>

                          <div className="flex items-center justify-between text-sm text-muted-foreground dark:text-white/60 mt-auto pt-3 border-t border-border dark:border-white/10">
                            <div className="flex items-center gap-1">
                              <CalendarClock className="h-4 w-4 text-indigo-400" />
                              <span>Active {getRelativeTimeString((group as GroupWithStats).stats?.lastActiveDate)}</span>
                            </div>
                            
                            <div className="flex items-center gap-1">
                              <MessageSquare className="h-4 w-4 text-emerald-400" />
                              <span>{(group as GroupWithStats).stats?.totalSessions || 0} sessions</span>
                            </div>
                          </div>
                        </div>
                      ))}

                      {/* Create New Group Card */}
                      <div
                        onClick={() => setIsCreateOpen(true)}
                        className="flex flex-col items-center justify-center p-6 bg-card dark:bg-gradient-to-br dark:from-slate-800/40 dark:to-slate-900/40 hover:bg-accent dark:hover:from-indigo-900/20 dark:hover:to-purple-900/20 rounded-xl border border-border dark:border-white/10 border-dashed transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-[1.02] cursor-pointer group min-h-[180px] animate-fadeIn"
                        style={{ animationDelay: `${groups.length * 50}ms` }}
                      >
                        <div className="w-16 h-16 rounded-full bg-secondary dark:bg-white/5 group-hover:bg-primary/20 dark:group-hover:bg-indigo-500/20 flex items-center justify-center mb-4 transition-colors">
                          <PlusCircle className="h-8 w-8 text-muted-foreground dark:text-white/40 group-hover:text-primary dark:group-hover:text-indigo-400 transition-colors" />
                        </div>
                        <h3 className="text-lg font-medium text-muted-foreground dark:text-white/70 group-hover:text-foreground dark:group-hover:text-white transition-colors">Create New Group</h3>
                      </div>

                      {/* Join Group Card */}
                      <div
                        onClick={() => setIsJoinOpen(true)}
                        className="flex flex-col items-center justify-center p-6 bg-card dark:bg-gradient-to-br dark:from-slate-800/40 dark:to-slate-900/40 hover:bg-accent dark:hover:from-pink-900/20 dark:hover:to-purple-900/20 rounded-xl border border-border dark:border-white/10 border-dashed transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-[1.02] cursor-pointer group min-h-[180px] animate-fadeIn"
                        style={{ animationDelay: `${(groups.length + 1) * 50}ms` }}
                      >
                        <div className="w-16 h-16 rounded-full bg-secondary dark:bg-white/5 group-hover:bg-primary/20 dark:group-hover:bg-pink-500/20 flex items-center justify-center mb-4 transition-colors">
                          <UserPlus className="h-8 w-8 text-muted-foreground dark:text-white/40 group-hover:text-primary dark:group-hover:text-pink-400 transition-colors" />
                        </div>
                        <h3 className="text-lg font-medium text-muted-foreground dark:text-white/70 group-hover:text-foreground dark:group-hover:text-white transition-colors">Join a Group</h3>
                      </div>
                    </>
                  ) : (
                    // No groups state - animated entry
                    <div className="col-span-full flex flex-col items-center justify-center py-12 text-center animate-fadeIn">
                      <div className="w-20 h-20 rounded-full bg-gradient-to-br from-indigo-500/20 to-pink-500/20 flex items-center justify-center mb-6">
                        <Users className="w-10 h-10 text-indigo-400" />
                      </div>
                      <h3 className="text-2xl font-medium text-foreground dark:text-white mb-3">No Groups Yet</h3>
                      <p className="text-muted-foreground dark:text-white/60 max-w-md mb-8">
                        You haven't joined any groups yet. Create your first group or join an existing one to get started.
                      </p>
                      <div className="flex gap-4">
                        <Button
                          variant="outline"
                          onClick={() => setIsJoinOpen(true)}
                          className="bg-secondary/50 dark:bg-white/5 hover:bg-secondary dark:hover:bg-white/10 border-border dark:border-white/10 text-muted-foreground dark:text-white"
                        >
                          <UserPlus className="h-4 w-4 mr-2" />
                          Join Group
                        </Button>
                        <Button
                          onClick={() => setIsCreateOpen(true)}
                          className="bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-500 hover:to-indigo-600"
                        >
                          <PlusCircle className="h-4 w-4 mr-2" />
                          Create Group
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </TabsContent>

            <TabsContent value="discover" data-tab="discover">
              {/* Theme-aware Card */}
              <Card className="bg-card dark:bg-gradient-to-br dark:from-slate-800/60 dark:to-slate-900/60 border border-border dark:border-white/5 backdrop-blur-xl rounded-xl shadow-xl overflow-hidden">
                {/* Theme-aware CardHeader border */}
                <CardHeader className="border-b border-border dark:border-white/5 pb-4">
                  <div className="flex items-center justify-between">
                    {/* Theme-aware CardTitle */}
                    <CardTitle className="text-lg font-medium flex items-center gap-2 text-card-foreground">
                      <Globe className="h-5 w-5 text-pink-400" />
                      <span>Discover Public Groups</span>
                    </CardTitle>
                    {/* Theme-aware Refresh Button */}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 rounded-full bg-secondary/50 dark:bg-white/5 hover:bg-secondary dark:hover:bg-white/10 text-muted-foreground dark:text-white"
                      onClick={fetchPublicGroups}
                      disabled={isLoadingPublicGroups}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className={`${isLoadingPublicGroups ? 'animate-spin' : ''}`}
                      >
                        <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
                        <path d="M3 3v5h5" />
                        <path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16" />
                        <path d="M16 21h5v-5" />
                      </svg>
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="pt-6">
                  {isLoadingPublicGroups ? (
                    <div className="flex justify-center py-8">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary" />
                    </div>
                  ) : publicGroups.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      {/* Theme-aware icon background */}
                      <div className="w-16 h-16 rounded-full bg-gradient-to-br from-indigo-500/20 to-pink-500/20 flex items-center justify-center mb-4">
                        <Globe className="w-8 h-8 text-indigo-400" />
                      </div>
                      {/* Theme-aware text */}
                      <h3 className="text-xl font-medium text-foreground dark:text-white mb-2">No Public Groups Available</h3>
                      {/* Theme-aware text */}
                      <p className="text-muted-foreground dark:text-white/60 max-w-md mb-6">
                        There are no public groups available to join at the moment. You can create your own group or join a private group with an invite code.
                      </p>
                      <div className="flex gap-4">
                        {/* Theme-aware button */}
                        <Button
                          variant="outline"
                          onClick={() => setIsJoinOpen(true)}
                          className="bg-secondary/50 dark:bg-white/5 hover:bg-secondary dark:hover:bg-white/10 border-border dark:border-white/10 text-muted-foreground dark:text-white"
                        >
                          <Lock className="h-4 w-4 mr-2" />
                          Join Private Group
                        </Button>
                        <Button
                          onClick={() => setActiveTab("joinCreate")}
                          className="bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-500 hover:to-indigo-600"
                        >
                          <PlusCircle className="h-4 w-4 mr-2" />
                          Create Group
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      {/* Search and filter section */}
                      <div className="space-y-4">
                        {/* Search bar */}
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input 
                            placeholder="Search groups by name or description..." 
                            className="pl-10 bg-background/50 dark:bg-white/5 border-border dark:border-white/10"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                          />
                        </div>
                        
                        {/* Filter section */}
                        <div className="flex flex-col space-y-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground dark:text-white/70">
                              <TagIcon className="h-4 w-4" />
                              <span>Filter by subjects & tags</span>
                            </div>
                            
                            {(searchQuery || selectedTags.length > 0) && (
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                onClick={clearFilters}
                                className="h-7 px-2 text-xs text-muted-foreground hover:text-foreground flex items-center gap-1"
                              >
                                <X className="h-3 w-3" />
                                Clear filters
                              </Button>
                            )}
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button 
                                  variant="outline" 
                                  size="sm" 
                                  className="bg-background/50 dark:bg-white/5 border-border dark:border-white/10 text-muted-foreground dark:text-white/70 flex items-center gap-1"
                                >
                                  <Filter className="h-3.5 w-3.5" />
                                  <span>Select Tags</span>
                                  {selectedTags.length > 0 && (
                                    <Badge 
                                      variant="secondary" 
                                      className="ml-1 text-xs bg-indigo-500/20 text-indigo-500 dark:bg-indigo-500/30 dark:text-indigo-300 border-none"
                                    >
                                      {selectedTags.length}
                                    </Badge>
                                  )}
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent 
                                className="w-64 max-h-64 overflow-y-auto bg-card dark:bg-slate-800/90 border-border dark:border-white/10"
                                align="start"
                              >
                                <div className="p-1">
                                  <div className="flex items-center justify-between mb-2">
                                    <span className="text-sm font-medium">Available Tags</span>
                                    {selectedTags.length > 0 && (
                                      <Button 
                                        variant="ghost" 
                                        size="sm" 
                                        onClick={() => setSelectedTags([])}
                                        className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground flex items-center gap-1"
                                      >
                                        <X className="h-3 w-3" />
                                        Clear
                                      </Button>
                                    )}
                                  </div>
                                  <div className="grid grid-cols-2 gap-1">
                                    {availableTags.map(tag => (
                                      <Badge 
                                        key={tag}
                                        variant={selectedTags.includes(tag) ? "default" : "outline"} 
                                        className={`cursor-pointer truncate ${
                                          selectedTags.includes(tag) 
                                            ? "bg-indigo-500 hover:bg-indigo-600" 
                                            : "bg-background/50 dark:bg-white/5 hover:bg-accent"
                                        }`}
                                        onClick={() => toggleTag(tag)}
                                      >
                                        {tag}
                                      </Badge>
                                    ))}
                                  </div>
                                </div>
                              </PopoverContent>
                            </Popover>
                            
                            {selectedTags.length > 0 && (
                              <div className="flex flex-wrap gap-1 flex-1 overflow-x-auto pb-1" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
                                {selectedTags.map(tag => (
                                  <Badge 
                                    key={tag}
                                    variant="default"
                                    className="bg-indigo-500 hover:bg-indigo-600 gap-1 truncate max-w-[150px]"
                                  >
                                    {tag}
                                    <X 
                                      className="h-3 w-3 cursor-pointer" 
                                      onClick={() => toggleTag(tag)}
                                    />
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                        
                        {/* Results summary */}
                        <div className="text-sm text-muted-foreground dark:text-white/60 flex items-center justify-between">
                          <span>Showing {filteredPublicGroups.length} of {publicGroups.length} groups</span>
                          <span className="text-xs">Sorted by study hours</span>
                        </div>
                      </div>
                      
                      {/* Groups grid */}
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {filteredPublicGroups.length > 0 ? (
                          filteredPublicGroups.map((group: PublicGroup) => (
                            // Theme-aware group card
                            <div
                              key={group.id}
                              className={`flex flex-col p-6 bg-card dark:bg-gradient-to-br dark:from-slate-800/80 dark:to-slate-900/80 hover:bg-accent dark:hover:from-slate-700/80 dark:hover:to-slate-800/80 rounded-xl border border-border dark:border-white/10 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-[1.02] group overflow-hidden relative`}
                            >
                              {/* Activity indicator */}
                              <div className={`absolute top-0 left-0 right-0 h-1 ${
                                group.activityLevel === 'high' 
                                  ? 'bg-gradient-to-r from-green-500 to-emerald-500' 
                                  : group.activityLevel === 'medium'
                                    ? 'bg-gradient-to-r from-amber-500 to-orange-500'
                                    : 'bg-gradient-to-r from-slate-400 to-slate-500'
                              }`}></div>
                              
                              <div className="flex items-center justify-between mb-3">
                                {/* Theme-aware text */}
                                <h3 className="text-lg font-medium text-card-foreground dark:group-hover:text-white transition-colors">{group.name}</h3>
                                {/* Keep badge style */}
                                <div className="text-xs bg-indigo-500/20 text-indigo-400 px-2 py-0.5 rounded-full flex items-center gap-1">
                                  <Globe className="h-3 w-3" />
                                  <span>Public</span>
                                </div>
                              </div>

                              {/* Theme-aware text */}
                              <p className="text-sm text-muted-foreground dark:text-white/70 mb-3 line-clamp-2">{group.description}</p>

                              {/* Study time highlight */}
                              <div className="flex items-center text-sm mb-3 bg-background/50 dark:bg-white/5 p-2 rounded-lg border border-border/50 dark:border-white/10">
                                <Clock className={`h-4 w-4 mr-2 ${
                                  group.activityLevel === 'high' 
                                    ? 'text-emerald-500' 
                                    : group.activityLevel === 'medium'
                                      ? 'text-amber-500'
                                      : 'text-slate-400'
                                }`} />
                                <span className="text-foreground dark:text-white/90">{formatGroupStudyTime(group.totalStudyTime)} total study time</span>
                              </div>

                              <div className="flex flex-wrap gap-2 mb-3">
                                {/* Theme-aware tags */}
                                {group.tags && group.tags.slice(0, 3).map((tag: string, index: number) => (
                                  <span 
                                    key={index} 
                                    className="text-xs bg-secondary dark:bg-white/10 text-secondary-foreground dark:text-white/80 px-2 py-0.5 rounded-full cursor-pointer hover:bg-secondary/70 dark:hover:bg-white/20"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      if (!selectedTags.includes(tag)) {
                                        toggleTag(tag);
                                      }
                                    }}
                                  >
                                    {tag}
                                  </span>
                                ))}
                                {group.tags && group.tags.length > 3 && (
                                  <span className="text-xs bg-secondary/50 dark:bg-white/5 text-secondary-foreground dark:text-white/60 px-2 py-0.5 rounded-full">
                                    +{group.tags.length - 3} more
                                  </span>
                                )}
                              </div>

                              {/* Theme-aware text */}
                              <div className="flex items-center justify-between text-sm text-muted-foreground dark:text-white/60 mb-4">
                                <div className="flex items-center gap-1">
                                  <Users className="h-4 w-4" />
                                  <span>{group.memberCount} members</span>
                                </div>
                                {/* Theme-aware text */}
                                <div className="text-xs text-muted-foreground/80 dark:text-white/50">
                                  Created {group.createdAt ? new Date(group.createdAt).toLocaleDateString() : 'recently'}
                                </div>
                              </div>

                              <div className="mt-auto pt-4">
                                {group.isUserMember ? (
                                  <Button
                                    className="w-full bg-emerald-600/50 hover:bg-emerald-600/70 text-white cursor-default"
                                    disabled
                                  >
                                    <Users className="h-4 w-4 mr-2" />
                                    Already a Member
                                  </Button>
                                ) : (
                                  <Button
                                    className="w-full bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-500 hover:to-indigo-600 transition-all duration-300"
                                    onClick={() => handleJoinPublicGroup(group.id)}
                                    disabled={joiningGroupId === group.id}
                                  >
                                    {joiningGroupId === group.id ? (
                                      <div className="flex items-center gap-2">
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
                                        <span>Joining...</span>
                                      </div>
                                    ) : (
                                      <>Join Group</>
                                    )}
                                  </Button>
                                )}
                              </div>
                            </div>
                          ))
                        ) : (
                          <div className="col-span-full flex flex-col items-center justify-center py-12 text-center animate-fadeIn">
                            <div className="w-16 h-16 rounded-full bg-secondary/50 dark:bg-white/5 flex items-center justify-center mb-4">
                              <Search className="w-8 h-8 text-muted-foreground dark:text-white/40" />
                            </div>
                            <h3 className="text-xl font-medium text-foreground dark:text-white mb-2">No matching groups found</h3>
                            <p className="text-muted-foreground dark:text-white/60 max-w-md mb-6">
                              Try adjusting your search terms or filters to find more groups.
                            </p>
                            <Button
                              variant="outline"
                              onClick={clearFilters}
                              className="bg-secondary/50 dark:bg-white/5 hover:bg-secondary dark:hover:bg-white/10 border-border dark:border-white/10"
                            >
                              <X className="h-4 w-4 mr-2" />
                              Clear All Filters
                            </Button>
                          </div>
                        )}
                      </div>

                      <div className="flex justify-center mt-8">
                        {/* Theme-aware text */}
                        <p className="text-muted-foreground dark:text-white/60">
                          Looking for a private group?
                          <Button
                            variant="link"
                            className="text-indigo-400 hover:text-indigo-300 p-0 h-auto ml-1"
                            onClick={() => setIsJoinOpen(true)}
                          >
                            Join with invite code or link
                          </Button>
                        </p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="joinCreate" data-tab="joinCreate">
              {/* Theme-aware Card */}
              <Card className="bg-card dark:bg-gradient-to-br dark:from-slate-800/60 dark:to-slate-900/60 border border-border dark:border-white/5 backdrop-blur-xl rounded-xl shadow-xl overflow-hidden">
                {/* Theme-aware CardHeader border */}
                <CardHeader className="border-b border-border dark:border-white/5 pb-4">
                  {/* Theme-aware CardTitle */}
                  <CardTitle className="text-lg font-medium flex items-center gap-2 text-card-foreground">
                    <Settings2 className="h-5 w-5 text-emerald-400" />
                    <span>Join or Create a New Group</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {/* Join Group Section */}
                    {/* Theme-aware inner card */}
                    <div className="bg-muted/50 dark:bg-white/5 rounded-xl p-6 border border-border dark:border-white/10">
                      {/* Theme-aware text */}
                      <h3 className="text-xl font-medium text-foreground dark:text-white mb-4 flex items-center gap-2">
                        <UserPlus className="h-5 w-5 text-indigo-400" />
                        Join a Group
                      </h3>
                      {/* Theme-aware text */}
                      <p className="text-muted-foreground dark:text-white/60 mb-6">
                        Join an existing study group using an invite code or browse public groups.
                      </p>

                      <div className="space-y-4">
                        {/* Theme-aware button */}
                        <Button
                          variant="outline"
                          className="w-full bg-secondary/50 dark:bg-white/5 hover:bg-secondary dark:hover:bg-white/10 border-border dark:border-white/10 text-muted-foreground dark:text-white"
                          onClick={() => setIsJoinOpen(true)}
                        >
                          <Lock className="h-4 w-4 mr-2" />
                          Join with Invite Code or Link
                        </Button>

                        {/* Primary button (styles likely ok) */}
                        <Button
                          className="w-full"
                          onClick={() => {
                            console.log('Setting activeTab to discover');
                            setActiveTab("discover");
                          }}
                        >
                          <Globe className="h-4 w-4 mr-2" />
                          Browse Public Groups
                        </Button>
                      </div>
                    </div>

                    {/* Create Group Section */}
                    {/* Theme-aware inner card */}
                    <div className="bg-muted/50 dark:bg-white/5 rounded-xl p-6 border border-border dark:border-white/10">
                      {/* Theme-aware text */}
                      <h3 className="text-xl font-medium text-foreground dark:text-white mb-4 flex items-center gap-2">
                        <PlusCircle className="h-5 w-5 text-pink-400" />
                        Create a Group
                      </h3>
                      {/* Theme-aware text */}
                      <p className="text-muted-foreground dark:text-white/60 mb-6">
                        Start your own study group and invite friends to collaborate and learn together.
                      </p>

                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4 mb-6">
                          {/* Theme-aware info box */}
                          <div className="bg-background dark:bg-white/5 rounded-lg p-4 border border-border dark:border-white/10 flex flex-col items-center text-center">
                            <Globe className="h-6 w-6 text-indigo-400 mb-2" />
                            {/* Theme-aware text */}
                            <h4 className="text-sm font-medium text-foreground dark:text-white mb-1">Public Group</h4>
                            {/* Theme-aware text */}
                            <p className="text-muted-foreground dark:text-white/60 text-xs">
                              Anyone can discover and join
                            </p>
                          </div>

                          {/* Theme-aware info box */}
                          <div className="bg-background dark:bg-white/5 rounded-lg p-4 border border-border dark:border-white/10 flex flex-col items-center text-center">
                            <Lock className="h-6 w-6 text-muted-foreground dark:text-slate-400 mb-2" />
                            {/* Theme-aware text */}
                            <h4 className="text-sm font-medium text-foreground dark:text-white mb-1">Private Group</h4>
                            {/* Theme-aware text */}
                            <p className="text-muted-foreground dark:text-white/60 text-xs">
                              Invite-only access
                            </p>
                          </div>
                        </div>

                        {/* Primary button (styles likely ok) */}
                        <Button
                          className="w-full bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-500 hover:to-indigo-600"
                          onClick={() => setIsCreateOpen(true)}
                        >
                          Create New Group
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Footer */}
      {/* Theme-aware footer */}
      <footer className="relative z-10 mt-4 border-t border-border dark:border-indigo-500/20 bg-muted/50 dark:bg-gradient-to-b dark:from-slate-900/60 dark:to-slate-950/60 backdrop-blur-xl">
        <div className="container mx-auto px-4 md:px-6 py-12">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div className="flex flex-col space-y-4">
              <div className="flex items-center space-x-3">
                {/* Keep logo */}
                <img src="/icon-192x192.png" alt="IsotopeAI Logo" className="w-10 h-10 rounded-full shadow-lg shadow-indigo-500/20" />
                {/* Keep gradient text */}
                <span className="font-semibold bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-pink-400">
                  IsotopeAI
                </span>
              </div>
              {/* Theme-aware text */}
              <p className="text-muted-foreground dark:text-white/40 max-w-md">
                Your all-in-one platform for AI-powered learning, productivity tools, and collaborative study.
              </p>
            </div>

            <div className="flex flex-col md:items-end space-y-4">
              <div className="flex items-center space-x-4">
                {/* Theme-aware links and separators */}
                <FeedbackWidget className="text-muted-foreground dark:text-white/60 hover:text-primary dark:hover:text-indigo-400 transition-colors" />
                <span className="text-border dark:text-white/20">|</span>
                <a href="https://isotopeai.featurebase.app/changelog" target="_blank" rel="noopener noreferrer" className="text-muted-foreground dark:text-white/60 hover:text-primary dark:hover:text-indigo-400 transition-colors">
                  Changelog
                </a>
                <span className="text-border dark:text-white/20">|</span>
                <a
                  href="mailto:<EMAIL>"
                  className="text-muted-foreground dark:text-white/60 hover:text-primary dark:hover:text-indigo-400 transition-colors"
                >
                  Contact
                </a>
                <span className="text-border dark:text-white/20">|</span>
                <a
                  href="https://www.instagram.com/isotope.ai/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-muted-foreground dark:text-white/60 hover:text-primary dark:hover:text-indigo-400 transition-colors flex items-center gap-1"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="lucide lucide-instagram"
                  >
                    <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                    <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                    <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                  </svg>
                </a>
                <span className="text-border dark:text-white/20">|</span>
                <a
                  href="https://www.reddit.com/r/Isotope/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-muted-foreground dark:text-white/60 hover:text-primary dark:hover:text-indigo-400 transition-colors flex items-center gap-1"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <circle cx="12" cy="12" r="10" />
                    <circle cx="12" cy="9" r="1" />
                    <circle cx="12" cy="15" r="1" />
                    <path d="M8.5 9a2 2 0 0 0-2 2v0c0 1.1.9 2 2 2" />
                    <path d="M15.5 9a2 2 0 0 1 2 2v0c0 1.1-.9 2-2 2" />
                    <path d="M7.5 13h9" />
                    <path d="M10 16v-3" />
                    <path d="M14 16v-3" />
                  </svg>
                </a>
              </div>
              {/* Theme-aware text */}
              <p className="text-muted-foreground dark:text-white/40 text-sm">
                Built with <span className="text-pink-500">❤️</span> by a fellow JEEtard
              </p>
            </div>
          </div>
        </div>

        {/* Dark mode only overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent pointer-events-none dark:block hidden" />
      </footer>

      <CreateGroup 
        open={isCreateOpen} 
        onOpenChange={setIsCreateOpen} 
        onCreateSuccess={(groupId) => {
          // After group is created, we'll select it to show details
          setSelectedGroupId(groupId);
          // Switch to My Groups tab
          setActiveTab("myGroups");
          // Show success toast
          toast({
            title: "Group Created",
            description: "Your new group has been created successfully.",
            duration: 3000
          });
        }}
      />
      <JoinGroup 
        open={isJoinOpen} 
        onOpenChange={setIsJoinOpen}
        onJoinSuccess={(groupId) => {
          // After joining group, we'll select it to show details
          setSelectedGroupId(groupId);
          // Switch to My Groups tab
          setActiveTab("myGroups");
          // Show success toast
          toast({
            title: "Group Joined",
            description: "You have successfully joined the group.",
            duration: 3000
          });
        }}
      />
    </div>
  )
}
