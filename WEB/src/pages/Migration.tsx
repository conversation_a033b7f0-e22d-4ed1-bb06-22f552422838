import React, { useState, useRef } from 'react';
import { useSupabaseAuth } from '../contexts/SupabaseAuthContext';
import { supabase } from '../integrations/supabase/client';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Progress } from '../components/ui/progress';
import { Alert, AlertDescription } from '../components/ui/alert';
import { Upload, Download, CheckCircle, AlertCircle, Info } from 'lucide-react';
import { toast } from '../hooks/use-toast';

interface ImportStatus {
  collection: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  message: string;
  recordsProcessed: number;
  totalRecords: number;
}

const Migration: React.FC = () => {
  const { user } = useSupabaseAuth();
  const [importStatuses, setImportStatuses] = useState<ImportStatus[]>([]);
  const [isImporting, setIsImporting] = useState(false);
  const fileInputRefs = useRef<{ [key: string]: HTMLInputElement | null }>({});

  const collections = [
    {
      key: 'aiChats',
      title: 'AI Chats',
      description: 'Your AI conversation history and shared chats',
      filename: 'isotope-ai-chats.csv'
    },
    {
      key: 'groups',
      title: 'Groups & Messages',
      description: 'Your group chats and messages',
      filename: 'isotope-groups-messages.csv'
    },
    {
      key: 'todos',
      title: 'Todos & Tasks',
      description: 'Your todo lists and task management data',
      filename: 'isotope-todos.csv'
    },
    {
      key: 'subjects',
      title: 'Subjects',
      description: 'Your custom subjects and study categories',
      filename: 'isotope-subjects.csv'
    },
    {
      key: 'exams',
      title: 'Exams',
      description: 'Your exam countdown and scheduling data',
      filename: 'isotope-exams.csv'
    },
    {
      key: 'userData',
      title: 'User Profile & Analytics',
      description: 'Your profile, study sessions, and mock test data',
      filename: 'isotope-user-data.csv'
    }
  ];

  const updateImportStatus = (collection: string, updates: Partial<ImportStatus>) => {
    setImportStatuses(prev => {
      const existing = prev.find(s => s.collection === collection);
      if (existing) {
        return prev.map(s => s.collection === collection ? { ...s, ...updates } : s);
      } else {
        return [...prev, {
          collection,
          status: 'pending',
          progress: 0,
          message: '',
          recordsProcessed: 0,
          totalRecords: 0,
          ...updates
        }];
      }
    });
  };

  const parseCSV = (csvText: string): any[] => {
    const lines = csvText.split('\n').filter(line => line.trim());
    if (lines.length === 0) return [];

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const data = [];

    for (let i = 1; i < lines.length; i++) {
      const values = [];
      let current = '';
      let inQuotes = false;

      for (let j = 0; j < lines[i].length; j++) {
        const char = lines[i][j];
        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
          values.push(current.trim());
          current = '';
        } else {
          current += char;
        }
      }
      values.push(current.trim());

      if (values.length === headers.length) {
        const row: any = {};
        headers.forEach((header, index) => {
          let value = values[index]?.replace(/^"|"$/g, '').replace(/""/g, '"') || '';
          
          // Parse JSON fields
          if (header.includes('messages') || header.includes('tags') || header.includes('members') || 
              header.includes('comments') || header.includes('subject_marks') || header.includes('stats') || 
              header.includes('progress')) {
            try {
              value = value ? JSON.parse(value) : null;
            } catch (e) {
              console.warn(`Failed to parse JSON for ${header}:`, value);
              value = null;
            }
          }
          
          // Parse boolean fields
          if (header.includes('is_') || header === 'completed') {
            value = value === 'true' || value === '1';
          }
          
          // Parse numeric fields
          if (header.includes('count') || header.includes('marks') || header === 'duration' || 
              header === 'progress' || header === 'priority') {
            value = value ? parseInt(value) || 0 : 0;
          }

          row[header] = value;
        });
        data.push(row);
      }
    }

    return data;
  };

  const importAIChats = async (data: any[]) => {
    const validData = data.filter(row => row.id && row.user_id === user?.uid);
    
    for (let i = 0; i < validData.length; i++) {
      const row = validData[i];
      
      const chatData = {
        id: row.id,
        user_id: row.user_id,
        title: row.title || null,
        messages: row.messages || [],
        created_at: row.created_at || new Date().toISOString(),
        updated_at: row.updated_at || new Date().toISOString(),
        is_public: row.is_public || false,
        view_count: row.view_count || 0,
        created_by: row.user_id,
        slug: row.slug || null,
        preview: row.preview || null,
        status: row.status || 'approved',
        tags: row.tags || [],
        is_pinned: row.is_pinned || false,
        is_starred: row.is_starred || false
      };

      const { error } = await supabase
        .from('ai_chats')
        .upsert(chatData, { onConflict: 'id' });

      if (error) {
        console.error('Error importing AI chat:', error);
        throw error;
      }

      // Import comments if they exist
      if (row.comments && Array.isArray(row.comments)) {
        for (const comment of row.comments) {
          const commentData = {
            id: comment.id,
            chat_id: row.id,
            author_id: comment.authorId,
            author_name: comment.author,
            author_username: comment.authorUsername,
            author_photo_url: comment.authorPhotoURL,
            content: comment.content,
            parent_id: comment.parentId || null,
            created_at: new Date(comment.timestamp).toISOString(),
            updated_at: new Date(comment.timestamp).toISOString()
          };

          await supabase
            .from('chat_comments')
            .upsert(commentData, { onConflict: 'id' });
        }
      }

      updateImportStatus('aiChats', {
        progress: Math.round(((i + 1) / validData.length) * 100),
        recordsProcessed: i + 1,
        totalRecords: validData.length,
        message: `Imported ${i + 1} of ${validData.length} AI chats`
      });
    }
  };

  const importGroups = async (data: any[]) => {
    const validData = data.filter(row => row.id && row.members?.includes(user?.uid));
    
    for (let i = 0; i < validData.length; i++) {
      const row = validData[i];
      
      const groupData = {
        id: row.id,
        name: row.name,
        description: row.description || null,
        members: row.members || [],
        created_by: row.created_by,
        created_at: row.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_public: row.is_public || false,
        invite_code: row.invite_code || null,
        owner_id: row.created_by
      };

      const { error: groupError } = await supabase
        .from('groups')
        .upsert(groupData, { onConflict: 'id' });

      if (groupError) {
        console.error('Error importing group:', groupError);
        throw groupError;
      }

      // Import messages if they exist
      if (row.messages && Array.isArray(row.messages)) {
        for (const message of row.messages) {
          const messageData = {
            id: message.id,
            group_id: row.id,
            sender_id: message.senderId,
            content: message.content,
            created_at: message.timestamp || new Date().toISOString(),
            updated_at: message.timestamp || new Date().toISOString()
          };

          await supabase
            .from('messages')
            .upsert(messageData, { onConflict: 'id' });
        }
      }

      updateImportStatus('groups', {
        progress: Math.round(((i + 1) / validData.length) * 100),
        recordsProcessed: i + 1,
        totalRecords: validData.length,
        message: `Imported ${i + 1} of ${validData.length} groups`
      });
    }
  };

  const importTodos = async (data: any[]) => {
    const validData = data.filter(row => row.id && row.created_by === user?.uid);
    
    for (let i = 0; i < validData.length; i++) {
      const row = validData[i];
      
      const todoData = {
        id: row.id,
        title: row.title,
        description: row.description || null,
        priority: row.priority || 'medium',
        created_at: row.created_at,
        updated_at: row.updated_at,
        due_date: row.due_date || null,
        assigned_to: row.assigned_to || null,
        assigned_to_name: row.assigned_to_name || null,
        assigned_to_photo_url: row.assigned_to_photo_url || null,
        created_by: row.created_by,
        group_id: row.group_id || null,
        column_id: row.column_id || 'column-1'
      };

      const { error } = await supabase
        .from('todos')
        .upsert(todoData, { onConflict: 'id' });

      if (error) {
        console.error('Error importing todo:', error);
        throw error;
      }

      updateImportStatus('todos', {
        progress: Math.round(((i + 1) / validData.length) * 100),
        recordsProcessed: i + 1,
        totalRecords: validData.length,
        message: `Imported ${i + 1} of ${validData.length} todos`
      });
    }
  };

  const importSubjects = async (data: any[]) => {
    const validData = data.filter(row => row.id && row.user_id === user?.uid);

    for (let i = 0; i < validData.length; i++) {
      const row = validData[i];

      const subjectData = {
        id: row.id,
        user_id: row.user_id,
        name: row.name,
        color: row.color || '#000000',
        created_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('user_subjects')
        .upsert(subjectData, { onConflict: 'id' });

      if (error) {
        console.error('Error importing subject:', error);
        throw error;
      }

      updateImportStatus('subjects', {
        progress: Math.round(((i + 1) / validData.length) * 100),
        recordsProcessed: i + 1,
        totalRecords: validData.length,
        message: `Imported ${i + 1} of ${validData.length} subjects`
      });
    }
  };

  const importExams = async (data: any[]) => {
    const validData = data.filter(row => row.id && row.user_id === user?.uid);

    for (let i = 0; i < validData.length; i++) {
      const row = validData[i];

      const examData = {
        id: row.id,
        user_id: row.user_id,
        name: row.name,
        date: row.date,
        total_marks: 0, // Default values for required fields
        total_marks_obtained: 0,
        subject_marks: [],
        created_at: row.created_at || new Date().toISOString()
      };

      const { error } = await supabase
        .from('exams')
        .upsert(examData, { onConflict: 'id' });

      if (error) {
        console.error('Error importing exam:', error);
        throw error;
      }

      updateImportStatus('exams', {
        progress: Math.round(((i + 1) / validData.length) * 100),
        recordsProcessed: i + 1,
        totalRecords: validData.length,
        message: `Imported ${i + 1} of ${validData.length} exams`
      });
    }
  };

  const importUserData = async (data: any[]) => {
    const userProfileData = data.find(row => row.type === 'user_profile' && row.user_id === user?.uid);
    const studySessions = data.filter(row => row.type === 'study_session' && row.user_id === user?.uid);
    const mockTests = data.filter(row => row.type === 'mock_test' && row.user_id === user?.uid);

    let processed = 0;
    const total = 1 + studySessions.length + mockTests.length;

    // Import user profile
    if (userProfileData) {
      const profileData = {
        id: user?.uid,
        email: userProfileData.email,
        display_name: userProfileData.display_name,
        photo_url: userProfileData.photo_url,
        username: userProfileData.username,
        created_at: userProfileData.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
        last_login: userProfileData.last_login || new Date().toISOString(),
        stats: userProfileData.stats || {},
        progress: userProfileData.progress || {}
      };

      const { error } = await supabase
        .from('users')
        .upsert(profileData, { onConflict: 'id' });

      if (error) {
        console.error('Error importing user profile:', error);
        throw error;
      }
    }
    processed++;

    updateImportStatus('userData', {
      progress: Math.round((processed / total) * 100),
      recordsProcessed: processed,
      totalRecords: total,
      message: `Imported user profile`
    });

    // Import study sessions
    for (let i = 0; i < studySessions.length; i++) {
      const session = studySessions[i];

      const sessionData = {
        id: session.id,
        user_id: session.user_id,
        subject: session.subject,
        duration: session.duration,
        mode: session.mode,
        phase: session.phase,
        completed: session.completed,
        start_time: session.start_time,
        end_time: session.end_time || null,
        notes: session.notes,
        created_at: session.start_time
      };

      const { error } = await supabase
        .from('study_sessions')
        .upsert(sessionData, { onConflict: 'id' });

      if (error) {
        console.error('Error importing study session:', error);
        throw error;
      }

      processed++;
      updateImportStatus('userData', {
        progress: Math.round((processed / total) * 100),
        recordsProcessed: processed,
        totalRecords: total,
        message: `Imported ${i + 1} of ${studySessions.length} study sessions`
      });
    }

    // Import mock tests
    for (let i = 0; i < mockTests.length; i++) {
      const test = mockTests[i];

      const testData = {
        id: test.id,
        user_id: test.user_id,
        name: test.name,
        test_date: test.test_date,
        subject_marks: test.subject_marks || [],
        total_marks_obtained: test.total_marks_obtained,
        total_marks: test.total_marks,
        notes: test.notes,
        created_at: test.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('mock_tests')
        .upsert(testData, { onConflict: 'id' });

      if (error) {
        console.error('Error importing mock test:', error);
        throw error;
      }

      processed++;
      updateImportStatus('userData', {
        progress: Math.round((processed / total) * 100),
        recordsProcessed: processed,
        totalRecords: total,
        message: `Imported ${i + 1} of ${mockTests.length} mock tests`
      });
    }
  };

  const handleFileUpload = async (collectionKey: string, file: File) => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to import data",
        variant: "destructive"
      });
      return;
    }

    updateImportStatus(collectionKey, {
      status: 'processing',
      progress: 0,
      message: 'Reading file...'
    });

    try {
      const text = await file.text();
      const data = parseCSV(text);

      if (data.length === 0) {
        throw new Error('No valid data found in CSV file');
      }

      updateImportStatus(collectionKey, {
        message: `Found ${data.length} records, starting import...`,
        totalRecords: data.length
      });

      switch (collectionKey) {
        case 'aiChats':
          await importAIChats(data);
          break;
        case 'groups':
          await importGroups(data);
          break;
        case 'todos':
          await importTodos(data);
          break;
        case 'subjects':
          await importSubjects(data);
          break;
        case 'exams':
          await importExams(data);
          break;
        case 'userData':
          await importUserData(data);
          break;
        default:
          throw new Error(`Unknown collection: ${collectionKey}`);
      }

      updateImportStatus(collectionKey, {
        status: 'completed',
        progress: 100,
        message: `Successfully imported ${data.length} records`
      });

      toast({
        title: "Import Successful",
        description: `${collectionKey} data has been imported successfully`
      });

    } catch (error: any) {
      console.error(`Import error for ${collectionKey}:`, error);
      updateImportStatus(collectionKey, {
        status: 'error',
        message: `Import failed: ${error.message}`
      });

      toast({
        title: "Import Failed",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const triggerFileInput = (collectionKey: string) => {
    fileInputRefs.current[collectionKey]?.click();
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      case 'processing':
        return <div className="h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
      default:
        return <Upload className="h-5 w-5 text-gray-400" />;
    }
  };

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>
              Please sign in to access the migration tool
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4">Data Migration</h1>
          <p className="text-gray-600 mb-6">
            Import your data from the legacy Firebase system to the new Supabase backend
          </p>

          <Alert className="mb-6">
            <Info className="h-4 w-4" />
            <AlertDescription>
              <strong>Before you start:</strong> Make sure you have exported your data from the legacy system at{' '}
              <a href="https://legacy.isotopeai.in" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                legacy.isotopeai.in
              </a>
            </AlertDescription>
          </Alert>
        </div>

        <div className="grid gap-6">
          {collections.map((collection) => {
            const status = importStatuses.find(s => s.collection === collection.key);

            return (
              <Card key={collection.key}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        {getStatusIcon(status?.status || 'pending')}
                        {collection.title}
                      </CardTitle>
                      <CardDescription>{collection.description}</CardDescription>
                    </div>
                    <Button
                      onClick={() => triggerFileInput(collection.key)}
                      disabled={isImporting || status?.status === 'processing'}
                      variant={status?.status === 'completed' ? 'outline' : 'default'}
                    >
                      {status?.status === 'completed' ? 'Re-import' : 'Import'}
                    </Button>
                  </div>
                </CardHeader>

                {status && (
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>{status.message}</span>
                        <span>{status.recordsProcessed}/{status.totalRecords}</span>
                      </div>
                      <Progress value={status.progress} className="w-full" />
                    </div>
                  </CardContent>
                )}

                <input
                  ref={el => fileInputRefs.current[collection.key] = el}
                  type="file"
                  accept=".csv"
                  style={{ display: 'none' }}
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleFileUpload(collection.key, file);
                    }
                  }}
                />
              </Card>
            );
          })}
        </div>

        <div className="mt-8 text-center">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Important:</strong> This migration process will merge your legacy data with any existing data in the new system.
              Existing records with the same ID will be updated. Make sure to backup your current data before proceeding.
            </AlertDescription>
          </Alert>
        </div>
      </div>
    </div>
  );
};

export default Migration;
