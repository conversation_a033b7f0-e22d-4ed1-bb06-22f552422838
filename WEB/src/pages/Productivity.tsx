import { useEffect, useState } from "react"
import { useNavigate, useLocation } from "react-router-dom" // Import useLocation
import { ProductivityLink } from "@/components/productivity/ProductivityLink" // Import our custom Link component
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext"
import { StudyTimer } from "@/components/productivity/StudyTimer"
import { AddStudySessionButton } from "@/components/productivity/AddStudySessionButton"

import { TasksDropdown } from "@/components/productivity/TasksDropdown"
import { ExamCountdown } from "@/components/productivity/ExamCountdown"
import { SpotifyBar } from "@/components/productivity/SpotifyBar"
import { LeaderboardButton } from "@/components/productivity/LeaderboardButton"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"
import { useBackgroundTheme } from "@/contexts/BackgroundThemeContext"
import { useDocumentTitle } from "@/hooks/useDocumentTitle"
import { useLocalStorage } from "@/hooks/useLocalStorage"
import { Settings } from "lucide-react"

import { SmallDeviceWarning } from '@/components/ui/SmallDeviceWarning';

import Header from "@/components/shared/Header"
// Removed Firebase imports - now using Supabase
import { Button } from "@/components/ui/button" // Import Button component

// Collection of motivational quotes for students
const motivationalQuotes = [
  "Every hour of focused study brings you one step closer to your dream college 🎓",
  "Consistency + Hard Work + Smart Study = Results 📊",
  "Master one concept at a time. Small victories lead to exam triumph 🏆",
  "Your rank is determined by what you do today, not tomorrow 🚀",
  "The best way to predict your future is to create it through disciplined study 📚",
  "The questions you solve today are the answers you'll know in the exam 💡",
  "Study smarter: 25 minutes of deep focus beats hours of distracted effort 🧠",
  "Your competition is studying right now. Are you? 💪",
  "Difficult formulas often lead to beautiful solutions ✨",
  "Track your progress, celebrate small wins, achieve big goals 📈",
  "When you feel like giving up, remember why you started 🌟",
  "Turn study stress into study success through planned breaks 🌿",
  "Today's productive study session is tomorrow's exam confidence 🎯",
  "The Pomodoro that seems hardest often teaches you the most 🍅",
  "Your notes today become your knowledge tomorrow 📝",
  "Study with purpose: every minute counts on the path to success ⏳",
  "The best students aren't always the smartest, but they're always the most consistent 🔄",
  "Chemistry, like success, is all about the right reactions at the right time ⚗️"
];

// Interface for productivity visibility settings
interface ProductivityVisibilitySettings {
  showQuotes: boolean;
  showTasks: boolean;
  showExamCountdown: boolean;
  showSpotifyBar: boolean;
  spotifyCollapsed: boolean;
}

// Default visibility settings
const DEFAULT_VISIBILITY_SETTINGS: ProductivityVisibilitySettings = {
  showQuotes: true,
  showTasks: true,
  showExamCountdown: true,
  showSpotifyBar: true,
  spotifyCollapsed: false
};

export default function Productivity() {
  useDocumentTitle("Productivity - IsotopeAI");
  const { user, loading } = useSupabaseAuth()
  const navigate = useNavigate()
  const location = useLocation(); // Get location object
  const [mode, setMode] = useState<"pomodoro" | "stopwatch">("stopwatch")
  // State to hold the initial selected subject from navigation state
  const [initialSubject, setInitialSubject] = useState<string | null>(null);
  const { getBackgroundStyle } = useBackgroundTheme() // Removed unused backgroundTheme
  const [quote, setQuote] = useState<string>("")

  // Visibility settings state
  const [visibilitySettings, setVisibilitySettings] = useState<ProductivityVisibilitySettings>(DEFAULT_VISIBILITY_SETTINGS);
  const [isLoadingSettings, setIsLoadingSettings] = useState(true);

  // Set Spotify states based on visibility settings
  const [spotifyVisible, setSpotifyVisible] = useLocalStorage<boolean>('spotify-is-visible', DEFAULT_VISIBILITY_SETTINGS.showSpotifyBar);
  const [spotifyCollapsed, setSpotifyCollapsed] = useLocalStorage<boolean>('spotify-is-collapsed', DEFAULT_VISIBILITY_SETTINGS.spotifyCollapsed);

  // Track timer status for beforeunload warning
  const [timerStatus] = useState<"idle" | "running" | "paused">("idle");

  // Load productivity settings from Supabase (simplified for now)
  useEffect(() => {
    const fetchProductivitySettings = async () => {
      if (!user) return;

      try {
        // For now, just use default settings
        // TODO: Implement Supabase settings loading
        setVisibilitySettings(DEFAULT_VISIBILITY_SETTINGS);
        setSpotifyVisible(DEFAULT_VISIBILITY_SETTINGS.showSpotifyBar);
        setSpotifyCollapsed(DEFAULT_VISIBILITY_SETTINGS.spotifyCollapsed);
      } catch (error) {
        console.error('Error fetching productivity settings:', error);
      } finally {
        setIsLoadingSettings(false);
      }
    };

    fetchProductivitySettings();
  }, [user, setSpotifyVisible, setSpotifyCollapsed]);

  // Select a random quote when the component mounts
  useEffect(() => {
    if (visibilitySettings.showQuotes) {
      const randomIndex = Math.floor(Math.random() * motivationalQuotes.length);
      setQuote(motivationalQuotes[randomIndex]);
    }
  }, [visibilitySettings.showQuotes]);

  // Effect to check for selectedSubject in navigation state on mount
  useEffect(() => {
    if (location.state && (location.state as any).selectedSubject) {
      setInitialSubject((location.state as any).selectedSubject);
      // Optionally clear the state after reading it to prevent re-applying on refresh
      // navigate(location.pathname, { replace: true });
    }
  }, [location.state]); // Depend on location.state

  // Add beforeunload event listener to prevent accidental tab closing
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      // Only show warning if timer is active (running or paused)
      if (timerStatus === "running" || timerStatus === "paused") {
        // Standard way to show a confirmation dialog before closing the tab
        const confirmationMessage = "You have an active study session. Are you sure you want to leave? Your progress might be lost.";
        e.preventDefault();
        e.returnValue = confirmationMessage; // Required for Chrome
        return confirmationMessage; // For older browsers
      }
    };

    // Add the event listener
    window.addEventListener('beforeunload', handleBeforeUnload);

    // Clean up the event listener when component unmounts
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [timerStatus]); // Add timerStatus as a dependency

  useEffect(() => {
    if (!loading && !user) {
      navigate('/login')
    }
  }, [user, loading, navigate])

  if (loading || isLoadingSettings) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
      </div>
    )
  }

  return (
    <div className={cn("relative min-h-screen w-full text-foreground overflow-hidden flex flex-col", getBackgroundStyle())} style={{ position: 'relative' }}>
      <SmallDeviceWarning />

      {/* Animated gradient orbs (Dark mode only) */}
      <div className="absolute top-20 right-[10%] w-64 h-64 rounded-full bg-purple-600/10 blur-[100px] animate-float-slow dark:block hidden"></div>
      <div className="absolute bottom-20 left-[5%] w-80 h-80 rounded-full bg-blue-600/10 blur-[120px] animate-float-slower dark:block hidden"></div>
      <div className="absolute top-[40%] left-[20%] w-40 h-40 rounded-full bg-pink-600/5 blur-[80px] animate-pulse-slow dark:block hidden"></div>

      {/* Subtle particle effect (Dark mode only) */}
      <div className="absolute inset-0 bg-[url('/stars.png')] bg-repeat opacity-[0.07] dark:block hidden"></div>

      {/* Top gradient overlay (Dark mode only) */}
      <div className="absolute top-0 left-0 right-0 h-72 bg-gradient-to-b from-purple-900/30 via-indigo-900/10 to-transparent dark:block hidden"></div>

      {/* Header section - fixed height */}
      <div className="flex-none">
        <Header />
      </div>

      {/* Main content section - scrollable, now with flex and centering */}
      <div className="flex-1 flex flex-col justify-center items-center relative pt-16 sm:pt-20">
        {/* Exam Countdown - Only visible on larger screens if enabled */}
        {visibilitySettings.showExamCountdown && (
          <div className="hidden sm:block absolute top-4 right-4 z-40">
            <ExamCountdown />
          </div>
        )}

        {/* Draggable Tasks Dropdown - Only if enabled */}
        {visibilitySettings.showTasks && (
          <div className="relative z-30 w-full mt-4 sm:mt-20">
            {/* Pass the initialSubject state as a prop */}
            <TasksDropdown initialSelectedSubject={initialSubject} />
          </div>
        )}

        {/* Main content with subtle animations - now centered */}
        <div className="flex-1 flex items-center justify-center px-4 animate-fadeIn w-full mt-4 sm:mt-0">
          <div className="w-full max-w-xl">
            {/* Enhanced tabs with beautiful box and subtle animation */}
            <div className="w-full mb-12 flex justify-center">
              {/* Use theme-aware background, border, and shadow */}
              <div className="bg-background/50 dark:bg-white/5 backdrop-blur-sm border border-border dark:border-white/10 rounded-full p-1.5 shadow-lg shadow-primary/10 dark:shadow-purple-500/10 inline-block hover:shadow-primary/20 dark:hover:shadow-purple-500/20 transition-all duration-500">
                <Tabs
                  value={mode}
                  onValueChange={(value) => setMode(value as "pomodoro" | "stopwatch")}
                  className="w-full"
                >
                  <TabsList className="grid grid-cols-2 bg-transparent gap-1">
                    <TabsTrigger
                      value="pomodoro"
                      className={cn(
                        "data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md",
                        "text-muted-foreground rounded-full py-2 px-6 flex items-center justify-center transition-all duration-300"
                      )}
                    >
                      Pomodoro
                    </TabsTrigger>
                    <TabsTrigger
                      value="stopwatch"
                      className={cn(
                        "data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md",
                        "text-muted-foreground rounded-full py-2 px-6 flex items-center justify-center transition-all duration-300"
                      )}
                    >
                      Stopwatch
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
            </div>

            <StudyTimer mode={mode} />

            {/* Exam Countdown - Only visible on small screens if enabled */}
            {visibilitySettings.showExamCountdown && (
              <div className="sm:hidden mt-8 flex justify-center">
                <ExamCountdown />
              </div>
            )}
          </div>
        </div>

        {/* Enhanced quote section with animation - now better positioned - Only if enabled */}
        {visibilitySettings.showQuotes && quote && (
          <div className="w-full pb-8 px-4">
            {/* Styled quote with decorative elements */}
            <div className="relative max-w-xl mx-auto px-6 animate-fadeIn">
              {/* Large quotation marks with improved visibility in dark mode */}
              <div className="absolute top-0 left-0 text-primary/30 dark:text-primary/40 text-6xl font-serif leading-none">&ldquo;</div>
              <div className="absolute bottom-0 right-0 text-primary/30 dark:text-primary/40 text-6xl font-serif leading-none">&rdquo;</div>

              {/* Quote text with improved typography */}
              <p className="font-serif italic text-xl sm:text-2xl text-center mx-auto text-muted-foreground py-6 px-4">
                {quote}
              </p>

              {/* Decorative line with improved visibility in dark mode */}
              <div className="w-24 h-1 bg-primary/30 dark:bg-primary/50 mx-auto mt-2 rounded-full"></div>
            </div>
          </div>
        )}

        {/* Spotify Bar - Only if enabled */}
        {visibilitySettings.showSpotifyBar && spotifyVisible && (
          <div className="w-full max-w-xl mb-12 px-4 flex justify-center animate-fadeIn">
            <SpotifyBar
              className={cn("w-full", spotifyCollapsed && "mx-auto w-auto")}
              isCollapsed={spotifyCollapsed}
              setIsCollapsed={setSpotifyCollapsed}
              onToggleCollapse={(collapsed) => setSpotifyCollapsed(collapsed)}
            />
          </div>
        )}
      </div>

      {/* Bottom buttons row - aligned in a fixed container */}
      <div className="fixed bottom-8 right-8 z-50 flex items-center gap-3">
        {/* Settings Button */}
        <ProductivityLink to="/settings">
          <Button
            variant="ghost"
            size="icon"
            className="h-12 w-12 rounded-full shadow-lg border dark:border-slate-800 bg-background/80 backdrop-blur-md hover:bg-primary/20 dark:hover:bg-white/10"
          >
            <Settings className="h-5 w-5 text-primary" />
          </Button>
        </ProductivityLink>

        {/* Study Sessions Button - Using the component but with custom styling */}
        <AddStudySessionButton
          className="!static h-12 w-12 rounded-full shadow-lg border dark:border-slate-800 bg-background/80 backdrop-blur-md hover:bg-primary/20 dark:hover:bg-white/10"
        />

        {/* Leaderboard Button - Using custom styling to match others */}
        <Button
          variant="ghost"
          size="icon"
          className="h-12 w-12 rounded-full shadow-lg border dark:border-slate-800 bg-background/80 backdrop-blur-md hover:bg-primary/20 dark:hover:bg-white/10"
          onClick={() => document.querySelector<HTMLButtonElement>('.leaderboard-trigger-button')?.click()}
        >
          <div className="h-5 w-5 text-primary">🏆</div>
        </Button>
      </div>

      {/* Keep the original LeaderboardButton for its functionality, but hide it */}
      <div className="hidden">
        <LeaderboardButton />
      </div>
    </div>
  )
}
