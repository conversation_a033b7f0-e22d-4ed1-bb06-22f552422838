import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { PublicQnA } from '../types/qa';
import { Button } from '../components/ui/button';
import { Separator } from '../components/ui/separator';
import { useDocumentTitle } from '../hooks/useDocumentTitle';
import { useSupabaseAuth } from '../contexts/SupabaseAuthContext';

const QAPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const [qna, setQna] = useState<PublicQnA | null>(null);
  const [relatedQnAs, setRelatedQnAs] = useState<PublicQnA[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useSupabaseAuth();
  const navigate = useNavigate();
  
  // Set document title once we have the question
  useDocumentTitle(qna ? `${qna.questionText} | IsotopeAI` : 'Q&A - IsotopeAI');

  useEffect(() => {
    const loadQnA = async () => {
      if (!slug) return;
      
      setIsLoading(true);
      try {
        // TODO: Implement Supabase Q&A fetching
        console.error('Q&A not found');
      } catch (error) {
        console.error('Error loading Q&A:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadQnA();
  }, [slug]);

  // Format timestamp for display
  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  // Handle "Continue the conversation" button click
  const handleContinueConversation = () => {
    if (qna?.originalChatId) {
      navigate(`/shared/${qna.originalChatId}`);
    }
  };

  // If still loading or Q&A not found
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-12 text-center">
        <p>Loading question and answer...</p>
      </div>
    );
  }

  if (!qna) {
    return (
      <div className="container mx-auto px-4 py-12 text-center">
        <h1 className="text-2xl font-bold mb-4">Question Not Found</h1>
        <p className="mb-6">The question you're looking for doesn't exist or has been removed.</p>
        <Link to="/qa">
          <Button>Browse All Questions</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-4">
        <Link to="/qa" className="text-blue-600 hover:underline">
          ← Back to Q&A Directory
        </Link>
      </div>
      
      <article className="bg-white rounded-lg shadow-md p-6 mb-8">
        <header className="mb-6">
          <h1 className="text-2xl font-bold mb-3">{qna.questionText}</h1>
          <div className="text-sm text-gray-500">
            Asked on {formatDate(qna.createdAt)} • {qna.viewCount} views
          </div>
        </header>
        
        {qna.image && (
          <div className="mb-6">
            <img 
              src={qna.image.url} 
              alt={`Image for: ${qna.questionText}`}
              className="max-w-full h-auto rounded-md"
              loading="lazy"
            />
          </div>
        )}
        
        <div className="prose max-w-none">
          <h2 className="text-xl font-semibold mb-3">Answer</h2>
          <div className="whitespace-pre-line">
            {qna.answerText}
          </div>
        </div>
        
        <Separator className="my-8" />
        
        <div className="bg-blue-50 rounded-lg p-6 text-center">
          <h3 className="text-lg font-semibold mb-2">Have follow-up questions?</h3>
          <p className="mb-4">
            Continue this conversation with our AI assistant to get more specific answers to your questions.
          </p>
          {user ? (
            <Button onClick={handleContinueConversation}>
              Continue the Conversation
            </Button>
          ) : (
            <div>
              <p className="mb-2 text-sm">You need to be logged in to continue this conversation.</p>
              <Link to={`/login?redirectTo=/shared/${qna.originalChatId}`}>
                <Button>Log in to Continue</Button>
              </Link>
            </div>
          )}
        </div>
      </article>
      
      {relatedQnAs.length > 0 && (
        <aside className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Related Questions</h2>
          <ul className="space-y-3">
            {relatedQnAs.map(related => (
              <li key={related.id}>
                <Link 
                  to={`/qa/${related.slug}`} 
                  className="text-blue-600 hover:underline block"
                >
                  {related.questionText}
                </Link>
              </li>
            ))}
          </ul>
          <div className="mt-6 text-center">
            <Link to="/qa">
              <Button variant="outline">View All Questions</Button>
            </Link>
          </div>
        </aside>
      )}
    </div>
  );
};

export default QAPage; 