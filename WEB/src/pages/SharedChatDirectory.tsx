import React, { useState, useEffect, useMemo } from 'react';
import { Link } from 'react-router-dom';
// TODO: Implement Supabase chat functions
import { SharedChatSEO, ChatMessage } from '../types/qa';
import { Helmet } from 'react-helmet';
import { useDocumentTitle } from '../hooks/useDocumentTitle';
import { useSupabaseAuth } from '../contexts/SupabaseAuthContext';
import { motion, AnimatePresence } from 'framer-motion';
import { Calendar, Eye, Search, Clock, SortAsc, SortDesc, BarChart, MessageSquare, Sparkles, ArrowRight } from 'lucide-react';
import { Header, Footer } from '@/components/shared';

const SharedChatDirectory: React.FC = () => {
  // Set document title
  useDocumentTitle('Shared AI Conversations - IsotopeAI');
  
  // Get authentication context
  const { user } = useSupabaseAuth();

  // State for chats and loading status
  const [allChats, setAllChats] = useState<SharedChatSEO[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [sortBy, setSortBy] = useState<'recent' | 'popular' | 'oldest'>('recent');
  const [totalCount, setTotalCount] = useState<number>(0);
  const [lastDoc, setLastDoc] = useState<QueryDocumentSnapshot<DocumentData> | null>(null);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [currentPage, setCurrentPage] = useState<number>(1);

  // Constants
  const CHATS_PER_PAGE = 30;
  const PREVIEW_LENGTH = 120;

  // Animation variants
  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.05,
        duration: 0.5,
        ease: [0.25, 0.4, 0.25, 1]
      }
    })
  };

  // Fetch chats on component mount or sort change
  useEffect(() => {
    let isMounted = true; // Flag to track if component is mounted
    console.log('SharedChatDirectory: Starting chat fetch, auth state:', user ? 'logged in' : 'not logged in');
    
    const fetchChats = async () => {
      try {
        setLoading(true);
        setAllChats([]);
        setLastDoc(null);
        setCurrentPage(1);
        setHasMore(true);
        
        // Get count first to show total
        console.log('SharedChatDirectory: Fetching shared chat count');
        const count = await getSharedChatCount();
        console.log('SharedChatDirectory: Got count:', count);
        
        if (!isMounted) return; // Don't update state if component unmounted
        setTotalCount(count);
        
        // Fetch chats based on sort order
        console.log(`SharedChatDirectory: Fetching ${sortBy} chats`);
        
        // TODO: Implement Supabase chat loading
        let result = { chats: [], lastDoc: null, hasMore: false };
        
        console.log(`SharedChatDirectory: Fetched ${result.chats.length} chats`);
        
        if (!isMounted) return; // Don't update state if component unmounted
        
        if (result.chats.length === 0) {
          setHasMore(false);
          setError("No public chats found. This could be due to a filtering issue or no public chats exist.");
        } else {
          setAllChats(result.chats);
          setLastDoc(result.lastDoc);
          setHasMore(result.chats.length >= CHATS_PER_PAGE);
          setError(null);
        }
      } catch (err) {
        console.error('Error fetching shared chats:', err);
        if (isMounted) {
          setError('Failed to load shared conversations. Please try again later.');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchChats();
    
    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, [sortBy, user, CHATS_PER_PAGE]);

  // Sort chats with 0 views to the end, and sort those by date
  const sortChatsWithZeroViewsLast = (chats: SharedChatSEO[]): SharedChatSEO[] => {
    return [...chats].sort((a, b) => {
      // If both have 0 views, sort by date (newest first)
      if (a.viewCount === 0 && b.viewCount === 0) {
        return b.updatedAt - a.updatedAt;
      }
      
      // If only a has 0 views, it goes after b
      if (a.viewCount === 0) return 1;
      
      // If only b has 0 views, it goes after a
      if (b.viewCount === 0) return -1;
      
      // Otherwise sort by viewCount (largest first)
      return b.viewCount - a.viewCount;
    });
  };

  // Function to load more chats
  const loadMoreChats = async () => {
    if (!hasMore || loadingMore) return;
    
    console.log('SharedChatDirectory: Loading more chats');
    try {
      setLoadingMore(true);
      
      // Calculate how many items we need to skip based on what we already have
      const batchSize = CHATS_PER_PAGE; // Keep this consistent
      
      console.log(`SharedChatDirectory: Fetching more ${sortBy} chats, already have ${allChats.length}, lastDoc:`, lastDoc ? 'exists' : 'null');
      
      // Fetch next batch of chats based on sort type
      let result;
      if (sortBy === 'recent') {
        result = await getRecentSharedChats(batchSize, lastDoc);
      } else if (sortBy === 'oldest') {
        result = await getOldestSharedChats(batchSize, lastDoc);
      } else { // popular
        result = await getPopularSharedChats(batchSize, lastDoc);
        
        // Sort chats with 0 views to the end by date
        result.chats = sortChatsWithZeroViewsLast(result.chats);
      }
      
      console.log(`SharedChatDirectory: Fetched ${result.chats.length} more chats`);
      
      if (result.chats.length === 0) {
        // No more chats to load
        setHasMore(false);
      } else {
        setAllChats(prev => [...prev, ...result.chats]);
        setLastDoc(result.lastDoc);
        setHasMore(result.chats.length >= batchSize);
        setCurrentPage(prev => prev + 1);
      }
    } catch (err) {
      console.error('Error loading more chats:', err);
      setError('Failed to load more conversations. Please try again.');
    } finally {
      setLoadingMore(false);
    }
  };

  // Filter chats based on search query
  const filteredChats = useMemo(() => {
    if (!searchQuery.trim()) {
      return allChats;
    }

    const query = searchQuery.toLowerCase();
    
    return allChats.filter(chat => {
      // Check title
      if (chat.title?.toLowerCase().includes(query)) {
        return true;
      }

      // Check tags
      if (chat.tags?.some(tag => tag.toLowerCase().includes(query))) {
        return true;
      }

      // Search within chat messages (this is the key feature)
      if (chat.chatMessages && chat.chatMessages.length > 0) {
        return chat.chatMessages.some(message => 
          message.content?.toLowerCase().includes(query)
        );
      }

      return false;
    });
  }, [allChats, searchQuery]);

  // Generate a preview of the first message that matches the search query
  const getPreviewWithHighlight = (chat: SharedChatSEO): string => {
    if (!chat.chatMessages || chat.chatMessages.length === 0) {
      return 'No messages in this conversation.';
    }

    // If there's no search query, just return the first message
    if (!searchQuery.trim()) {
      const firstMessage = chat.chatMessages[0];
      return firstMessage.content.substring(0, PREVIEW_LENGTH) + 
        (firstMessage.content.length > PREVIEW_LENGTH ? '...' : '');
    }

    const query = searchQuery.toLowerCase();
    
    // Find the first message containing the search query
    const matchingMessage = chat.chatMessages.find(msg => 
      msg.content.toLowerCase().includes(query)
    );

    if (!matchingMessage) {
      // If no match found (should not happen due to our filter), return the first message
      return chat.chatMessages[0].content.substring(0, PREVIEW_LENGTH) + '...';
    }

    // Find the position of the query in the message
    const content = matchingMessage.content;
    const matchIndex = content.toLowerCase().indexOf(query);
    
    // Create a window around the match
    let start = Math.max(0, matchIndex - 40);
    let end = Math.min(content.length, matchIndex + query.length + 80);
    
    // Adjust to avoid cutting words
    if (start > 0) {
      // Find the next space before the start
      const prevSpace = content.lastIndexOf(' ', start);
      if (prevSpace !== -1) start = prevSpace + 1;
    }
    
    if (end < content.length) {
      // Find the next space after the end
      const nextSpace = content.indexOf(' ', end);
      if (nextSpace !== -1) end = nextSpace;
    }
    
    let preview = content.substring(start, end);
    
    // Add ellipsis if needed
    if (start > 0) preview = '...' + preview;
    if (end < content.length) preview = preview + '...';
    
    return preview;
  };

  // Calculate pagination info
  const paginationInfo = useMemo(() => {
    const totalPages = Math.ceil(totalCount / CHATS_PER_PAGE) || 1;
    const displayedResults = searchQuery ? filteredChats.length : Math.min(allChats.length, totalCount);
    const start = 1;
    const end = displayedResults;
    
    return {
      totalPages,
      displayedResults,
      start,
      end
    };
  }, [totalCount, CHATS_PER_PAGE, filteredChats.length, searchQuery, allChats.length]);

  // Function to fetch a specific page
  const fetchPage = async (pageNumber: number) => {
    if (pageNumber === currentPage || pageNumber < 1 || pageNumber > paginationInfo.totalPages || loading || loadingMore) return;
    
    console.log(`SharedChatDirectory: Fetching page ${pageNumber} of ${paginationInfo.totalPages}`);
    try {
      setLoading(true);
      setAllChats([]);
      
      // For first page, fetch from beginning
      if (pageNumber === 1) {
        let result;
        if (sortBy === 'recent') {
          result = await getRecentSharedChats(CHATS_PER_PAGE);
        } else if (sortBy === 'oldest') {
          result = await getOldestSharedChats(CHATS_PER_PAGE);
        } else { // popular
          result = await getPopularSharedChats(CHATS_PER_PAGE);
          result.chats = sortChatsWithZeroViewsLast(result.chats);
        }
        
        setAllChats(result.chats);
        setLastDoc(result.lastDoc);
        setHasMore(result.chats.length >= CHATS_PER_PAGE);
        setCurrentPage(1);
      } 
      // For other pages, we need to implement a different approach
      else {
        const itemsToSkip = (pageNumber - 1) * CHATS_PER_PAGE;
        const chatsToFetch = CHATS_PER_PAGE;
        
        console.log(`SharedChatDirectory: Need to skip ${itemsToSkip} items to reach page ${pageNumber}`);
        
        // Option 1: Use a single query with a large limit
        // This works for medium-sized collections but may time out for very large ones
        let result;
        if (sortBy === 'recent') {
          result = await getRecentSharedChats(itemsToSkip + chatsToFetch);
        } else if (sortBy === 'oldest') {
          result = await getOldestSharedChats(itemsToSkip + chatsToFetch);
        } else { // popular
          result = await getPopularSharedChats(itemsToSkip + chatsToFetch);
          result.chats = sortChatsWithZeroViewsLast(result.chats);
        }
          
        if (result.chats.length > itemsToSkip) {
          // Extract just the items for this page
          const pagedChats = result.chats.slice(itemsToSkip);
          setAllChats(pagedChats);
          // The last document should be the last one we're displaying on this page
          const lastIndex = Math.min(itemsToSkip + chatsToFetch - 1, result.chats.length - 1);
          setLastDoc(lastIndex >= 0 && lastIndex < result.chats.length ? 
            result.lastDoc : null);
          setHasMore(result.chats.length >= itemsToSkip + chatsToFetch);
          setCurrentPage(pageNumber);
        } else {
          // Not enough items in the result
          console.log('SharedChatDirectory: Not enough items to display this page');
          setError('Unable to load this page. Please try a different page or refresh.');
          setHasMore(false);
        }
      }
    } catch (err) {
      console.error('Error fetching page:', err);
      setError('Failed to load page. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Determine the correct destination for the "Start a new conversation" button
  const startConversationLink = user ? "/ai" : "/ai-landing";

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-background/95 relative overflow-hidden shared-page-container custom-scrollbar">
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl opacity-60 pointer-events-none" />
      <div className="absolute bottom-0 left-0 w-96 h-96 bg-secondary/5 rounded-full blur-3xl opacity-60 pointer-events-none" />
      
      {/* Blur circles */}
      <div className="blur-circle blur-circle-1"></div>
      <div className="blur-circle blur-circle-2"></div>
      
      <Helmet>
        <title>Shared Chats - IsotopeAI</title>
        <meta name="description" content="Browse through public AI conversations from IsotopeAI. Search our knowledge base for answers to your questions and join the discussion." />
        <meta name="keywords" content="AI, artificial intelligence, conversations, chat, knowledge base, Q&A" />
        <link rel="canonical" href="https://isotopeai.com/shared" />
        <meta property="og:title" content="Shared Chats - IsotopeAI" />
        <meta property="og:description" content="Browse through public AI conversations from IsotopeAI. Search our knowledge base for answers to your questions and join the discussion." />
        <meta property="og:url" content="https://isotopeai.com/shared" />
        <meta property="og:type" content="website" />
      </Helmet>

      {/* Header */}
      <Header />

      <div className="max-w-6xl mx-auto px-4 py-12 md:py-16 pt-32 relative z-10 shared-page-typography">
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-12 text-center"
        >
          <div className="inline-flex items-center mb-4 bg-primary/10 px-3 py-1 rounded-full text-primary text-sm border border-primary/20 enhanced-badge">
            <Sparkles className="h-4 w-4 mr-2" /> Collaborative Knowledge
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-4 gradient-heading font-spaceGrotesk">
            Shared AI Conversations
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Browse through {totalCount} shared conversations or search for specific topics
          </p>
        </motion.div>

        {/* Search and filter controls */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-10 p-4 md:p-6 bg-background/50 backdrop-blur-sm rounded-xl border border-border/40 shadow-sm"
        >
          <div className="flex flex-col md:flex-row gap-4 items-center">
            <div className="flex-grow relative">
              <div className="absolute inset-y-0 left-4 flex items-center pointer-events-none">
                <Search className="w-5 h-5 text-muted-foreground" />
              </div>
              <input
                type="text"
                className="w-full pl-12 pr-4 py-3 border border-border/60 rounded-lg focus:ring-2 focus:ring-primary/30 focus:border-primary bg-background/80 transition-all duration-200"
                placeholder="Search within conversations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div className="flex-shrink-0 w-full md:w-auto">
              <div className="relative">
                <select
                  className="appearance-none w-full md:w-auto px-10 py-3 border border-border/60 rounded-lg focus:ring-2 focus:ring-primary/30 focus:border-primary bg-background/80 transition-all duration-200 pr-10"
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as 'recent' | 'popular' | 'oldest')}
                >
                  <option value="recent">Newest First</option>
                  <option value="oldest">Oldest First</option>
                  <option value="popular">Most Popular</option>
                </select>
                <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                  {sortBy === 'recent' && <Clock className="w-4 h-4 text-muted-foreground" />}
                  {sortBy === 'oldest' && <SortAsc className="w-4 h-4 text-muted-foreground" />}
                  {sortBy === 'popular' && <BarChart className="w-4 h-4 text-muted-foreground" />}
                </div>
                <div className="absolute inset-y-0 right-3 flex items-center pointer-events-none">
                  <SortDesc className="w-4 h-4 text-muted-foreground" />
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Results count */}
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mb-6 text-muted-foreground flex justify-between items-center py-2"
        >
          <div>
            {!loading && (
              filteredChats.length === 0 && searchQuery
                ? 'No conversations found matching your search.'
                : searchQuery 
                  ? `Found ${filteredChats.length} conversation${filteredChats.length !== 1 ? 's' : ''} matching your search`
                  : `Showing ${paginationInfo.start}-${paginationInfo.end} of ${totalCount} conversation${totalCount !== 1 ? 's' : ''}`
            )}
          </div>
          <div>
            {!searchQuery && (
              <span className="inline-flex items-center bg-background/50 px-3 py-1 rounded-full text-sm border border-border/30">
                Page {currentPage} of {paginationInfo.totalPages}
              </span>
            )}
          </div>
        </motion.div>

        {/* Chat list */}
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="relative shared-loading">
              <div className="absolute -inset-1 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-full blur-sm animate-pulse"></div>
              <div className="relative animate-spin rounded-full h-12 w-12 border-2 border-t-primary border-r-transparent border-b-secondary border-l-transparent"></div>
            </div>
          </div>
        ) : error ? (
          <motion.div 
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.4 }}
            className="bg-destructive/10 p-6 rounded-lg text-destructive border border-destructive/20 backdrop-blur-sm"
          >
            {error}
          </motion.div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <AnimatePresence mode="popLayout">
                {filteredChats.map((chat, index) => (
                  <motion.div
                    key={chat.id}
                    variants={fadeInUp}
                    initial="hidden"
                    animate="visible"
                    custom={index}
                    className={index % 2 === 0 ? "floating-animation" : "floating-animation-delayed"}
                  >
                    <Link
                      to={`/shared/${chat.id}`}
                      className="bg-background/70 backdrop-blur-sm p-5 rounded-xl border border-border/40 shadow-sm hover:shadow-md hover:border-primary/30 hover:bg-background/80 transition-all duration-300 block h-full card-hover-effect"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <h2 className="text-xl font-semibold text-foreground line-clamp-2 font-spaceGrotesk">
                          {chat.title || 'Untitled Conversation'}
                        </h2>
                        <div className="flex items-center gap-1 text-sm text-muted-foreground whitespace-nowrap ml-2 bg-background/50 px-2 py-1 rounded-full">
                          <Eye className="h-3.5 w-3.5" />
                          <span>{chat.viewCount || 0}</span>
                        </div>
                      </div>

                      <p className="text-muted-foreground mb-4 line-clamp-3">
                        {getPreviewWithHighlight(chat)}
                      </p>

                      <div className="flex justify-between items-center mt-auto">
                        {chat.tags && chat.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1.5 mt-2">
                            {chat.tags.slice(0, 3).map(tag => (
                              <span 
                                key={tag} 
                                className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full enhanced-badge"
                              >
                                {tag}
                              </span>
                            ))}
                            {chat.tags.length > 3 && (
                              <span className="text-xs bg-secondary/10 text-secondary px-2 py-1 rounded-full enhanced-badge">
                                +{chat.tags.length - 3}
                              </span>
                            )}
                          </div>
                        )}
                        
                        <div className="flex items-center text-primary text-sm font-medium mt-2">
                          Read more
                          <ArrowRight className="h-3.5 w-3.5 ml-1" />
                        </div>
                      </div>
                    </Link>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>

            {/* Load more button */}
            {!searchQuery && hasMore && (
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.5 }}
                className="flex justify-center mt-10"
              >
                <button
                  onClick={loadMoreChats}
                  disabled={loadingMore}
                  className="group px-8 py-3 bg-primary/10 text-primary border border-primary/20 rounded-full hover:bg-primary/20 focus:outline-none focus:ring-2 focus:ring-primary/40 focus:ring-offset-2 disabled:opacity-50 transition-all duration-300 button-glow"
                >
                  {loadingMore ? (
                    <span className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Loading...
                    </span>
                  ) : (
                    <span className="flex items-center">
                      <span>Load More</span>
                      <span className="ml-2 group-hover:translate-y-1 transition-transform duration-300">↓</span>
                    </span>
                  )}
                </button>
              </motion.div>
            )}
            
            {/* Pagination controls */}
            {!searchQuery && paginationInfo.totalPages > 1 && (
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.5 }}
                className="flex justify-center items-center mt-10 bg-background/50 backdrop-blur-sm p-3 rounded-full border border-border/30 inline-block mx-auto"
              >
                <button
                  onClick={() => fetchPage(1)}
                  disabled={currentPage === 1 || loading}
                  className="p-2 rounded-md border border-border/40 disabled:opacity-50 hover:bg-background/80 hover:border-primary/30 transition-colors"
                  aria-label="First page"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                    <path fillRule="evenodd" d="M7.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L3.414 10l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                  </svg>
                </button>
                <button
                  onClick={() => fetchPage(currentPage - 1)}
                  disabled={currentPage === 1 || loading}
                  className="p-2 rounded-md border border-border/40 disabled:opacity-50 hover:bg-background/80 hover:border-primary/30 transition-colors mx-1"
                  aria-label="Previous page"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </button>
                
                <div className="flex items-center gap-1 mx-1">
                  {Array.from({ length: Math.min(5, paginationInfo.totalPages) }, (_, i) => {
                    // Show current page and 2 pages before and after it
                    let pageNum = currentPage - 2 + i;
                    
                    // Adjust if we're at the beginning
                    if (currentPage < 3) {
                      pageNum = i + 1;
                    }
                    
                    // Adjust if we're at the end
                    if (currentPage > paginationInfo.totalPages - 2) {
                      pageNum = paginationInfo.totalPages - 4 + i;
                      if (pageNum <= 0) pageNum = i + 1;
                    }
                    
                    // Skip if page is out of bounds
                    if (pageNum < 1 || pageNum > paginationInfo.totalPages) {
                      return null;
                    }
                    
                    return (
                      <button
                        key={pageNum}
                        onClick={() => fetchPage(pageNum)}
                        disabled={currentPage === pageNum || loading}
                        className={`w-10 h-10 rounded-md flex items-center justify-center text-sm font-medium transition-all duration-200
                          ${currentPage === pageNum 
                            ? 'bg-primary text-primary-foreground shadow-md' 
                            : 'border border-border/40 hover:bg-background/80 hover:border-primary/30'}`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>
                
                <button
                  onClick={() => fetchPage(currentPage + 1)}
                  disabled={currentPage === paginationInfo.totalPages || loading}
                  className="p-2 rounded-md border border-border/40 disabled:opacity-50 hover:bg-background/80 hover:border-primary/30 transition-colors mx-1"
                  aria-label="Next page"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                </button>
                <button
                  onClick={() => fetchPage(paginationInfo.totalPages)}
                  disabled={currentPage === paginationInfo.totalPages || loading}
                  className="p-2 rounded-md border border-border/40 disabled:opacity-50 hover:bg-background/80 hover:border-primary/30 transition-colors"
                  aria-label="Last page"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10l-4.293-3.293a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    <path fillRule="evenodd" d="M12.293 15.707a1 1 0 010-1.414L16.586 10l-4.293-3.293a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                </button>
              </motion.div>
            )}
            
            {/* No more results message */}
            {!searchQuery && !hasMore && allChats.length > 0 && (
              <motion.div 
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.4, delay: 0.2 }}
                className="text-center mt-8 text-muted-foreground"
              >
                You've reached the end of the results.
              </motion.div>
            )}
          </>
        )}

        {/* Start a new conversation button */}
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="mt-20 bg-gradient-to-r from-primary/10 to-primary/5 p-8 rounded-2xl border border-primary/20 backdrop-blur-sm shadow-lg relative overflow-hidden"
        >
          <div className="absolute inset-0 pointer-events-none opacity-30">
            <div className="absolute -top-24 -right-24 w-48 h-48 bg-primary/20 rounded-full blur-3xl"/>
            <div className="absolute -bottom-24 -left-24 w-48 h-48 bg-primary/10 rounded-full blur-3xl"/>
          </div>
          
          <div className="relative z-10">
            <h2 className="text-2xl md:text-3xl font-bold text-foreground mb-4 font-spaceGrotesk gradient-heading">
              Want to create your own conversation?
            </h2>
            <p className="text-lg text-muted-foreground mb-6">
              Start a new chat with our AI and share your insights with others.
            </p>
            <Link
              to="/ai-landing"
              className="inline-flex items-center px-8 py-3 bg-primary text-primary-foreground rounded-full hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary/40 focus:ring-offset-2 transition-all duration-300 shadow-md shadow-primary/20 group button-glow"
            >
              <span>Start a new conversation</span>
              <ArrowRight className="ml-2 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
          </div>
        </motion.div>
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default SharedChatDirectory; 