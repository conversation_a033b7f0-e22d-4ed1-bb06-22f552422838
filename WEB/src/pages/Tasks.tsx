import { useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext"
import { TodoBoard } from "@/components/productivity/TodoBoard"
import { useBackgroundTheme } from "@/contexts/BackgroundThemeContext"
import { SmallDeviceWarning } from '@/components/ui/SmallDeviceWarning'
import { useDocumentTitle } from "@/hooks/useDocumentTitle"
import Header from "@/components/shared/Header"
import { motion } from "framer-motion"
import { Calendar, CheckSquare, Clock, ListTodo } from "lucide-react"

export default function Tasks() {
  useDocumentTitle("Tasks - IsotopeAI");
  const { user, loading } = useSupabaseAuth()
  const navigate = useNavigate()
  const { getBackgroundStyle } = useBackgroundTheme()

  useEffect(() => {
    if (!loading && !user) {
      navigate('/login')
    }
  }, [user, loading, navigate])

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-[#0a0f2c] dark:to-[#1a1f3c]">
        <div className="relative w-16 h-16">
          <div className="absolute top-0 left-0 w-full h-full rounded-full border-4 border-primary/30 animate-pulse"></div>
          <div className="absolute top-0 left-0 w-full h-full rounded-full border-t-4 border-primary animate-spin"></div>
        </div>
        <p className="mt-4 text-muted-foreground animate-pulse">Loading your workspace...</p>
      </div>
    )
  }

  return (
    <div className="relative min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 text-gray-900 dark:bg-gradient-to-b dark:from-[#0a0f2c] dark:to-[#1a1f3c] dark:text-white overflow-hidden">
      <SmallDeviceWarning />
      
      {/* Enhanced background with decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className={`absolute inset-0 ${getBackgroundStyle()} opacity-20 dark:opacity-40`} />
        
        {/* Decorative elements */}
        <div className="absolute top-20 right-10 w-64 h-64 bg-primary/5 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-20 left-10 w-80 h-80 bg-indigo-500/5 rounded-full filter blur-3xl"></div>
        
        {/* Floating icons (visible only on desktop) */}
        <div className="hidden lg:block">
          <motion.div 
            className="absolute top-40 right-32 text-primary/20 dark:text-primary/10"
            animate={{ 
              y: [0, 15, 0],
              rotate: [0, 5, 0],
            }}
            transition={{ 
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <CheckSquare size={48} />
          </motion.div>
          
          <motion.div 
            className="absolute bottom-40 right-1/4 text-violet-500/20 dark:text-violet-500/10"
            animate={{ 
              y: [0, -20, 0],
              rotate: [0, -5, 0],
            }}
            transition={{ 
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1
            }}
          >
            <Calendar size={40} />
          </motion.div>
          
          <motion.div 
            className="absolute top-1/3 left-1/4 text-indigo-500/20 dark:text-indigo-500/10"
            animate={{ 
              y: [0, 10, 0],
              x: [0, 10, 0],
              rotate: [0, 10, 0],
            }}
            transition={{ 
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
          >
            <ListTodo size={56} />
          </motion.div>
          
          <motion.div 
            className="absolute bottom-1/4 left-32 text-emerald-500/20 dark:text-emerald-500/10"
            animate={{ 
              y: [0, -15, 0],
              rotate: [0, -3, 0],
            }}
            transition={{ 
              duration: 9,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 3
            }}
          >
            <Clock size={36} />
          </motion.div>
        </div>
      </div>
      
      {/* Header */}
      <Header />
      
      {/* Main content with animation */}
      <motion.div 
        className="relative z-10 flex flex-col min-h-screen pt-28 px-4 pb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
      >
        <div className="flex-1 w-full max-w-7xl mx-auto">
          {/* Page title with animation */}
          <motion.div
            className="mb-6"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.4 }}
          >
            <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300">
              Your Tasks
            </h1>
            <p className="text-muted-foreground mt-2">
              Organize, prioritize, and track your tasks efficiently
            </p>
          </motion.div>
          
          {/* Card container with animation */}
          <motion.div
            className="bg-white/80 dark:bg-background/30 dark:backdrop-blur-md rounded-2xl p-6 h-[calc(100vh-220px)] shadow-xl dark:shadow-2xl dark:shadow-indigo-500/5 border border-gray-100 dark:border-indigo-500/10 overflow-hidden"
            initial={{ opacity: 0, scale: 0.98 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            <TodoBoard />
          </motion.div>
        </div>
      </motion.div>
    </div>
  )
}
