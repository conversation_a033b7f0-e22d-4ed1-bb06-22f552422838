import { But<PERSON> } from "@/components/ui/button";
import { Link, useNavigate } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { ArrowRight, Users, Share2, ChartLine, BookOpen, MessageCircle, Target, Menu, X, Instagram, Mail, Calendar } from "lucide-react";
import { useState, useEffect } from "react";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { SignIn } from "@/components/SignIn";
import { Header, Footer } from "@/components/shared";
import { Helmet } from "react-helmet";

const GroupsLanding = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { user, signInWithGoogle } = useSupabaseAuth();
  const navigate = useNavigate();

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleGetStarted = () => {
    if (user) {
      navigate('/groups');
    } else {
      signInWithGoogle();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted relative overflow-hidden flex flex-col">
      <Helmet>
        <title>Study Groups & Collaborative Learning | IsotopeAI</title>
        <meta
          name="description"
          content="Join or create study groups to collaborate, share resources, and accelerate your learning. Track progress, set goals, and participate in group discussions."
        />
        <meta
          name="keywords"
          content="study groups, collaborative learning, group study, online study groups, resource sharing, academic collaboration, study sessions, group discussions, progress tracking, goal setting, JEE study groups, NEET study groups"
        />
        <meta property="og:title" content="Study Groups & Collaborative Learning | IsotopeAI" />
        <meta property="og:description" content="Join or create study groups to collaborate, share resources, and accelerate your learning. Track progress, set goals, and participate in group discussions." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://isotopeai.com/groups-landing" />
        <meta name="twitter:card" content="summary_large_image" />
        <link rel="canonical" href="https://isotopeai.com/groups-landing" />
      </Helmet>
      
      {/* Header */}
      <Header />
      
      {/* Floating Elements */}
      <FloatingElements />
      
      {/* Main Content */}
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="container mx-auto px-4 py-16 pt-28 md:pt-40 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left side - Text content */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center lg:text-left"
            >
              <motion.div 
                className="inline-block mb-4"
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                transition={{ 
                  duration: 0.5,
                  type: "spring",
                  stiffness: 200
                }}
              >
                <div className="bg-primary/20 p-3 rounded-full inline-block">
                  <Users className="w-8 h-8 text-primary" />
                </div>
              </motion.div>
              
              {/* 100% Free Badge */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="mb-4"
              >
                <span className="bg-green-500/20 text-green-500 font-semibold px-4 py-2 rounded-full inline-flex items-center gap-1 border border-green-500/30">
                  <img src="/favicon.ico" alt="IsotopeAI Logo" className="w-4 h-4" />
                  100% Free Forever
                </span>
              </motion.div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70">
                Study Better Together
              </h1>
              <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto lg:mx-0">
                Join or create study groups to collaborate, share resources, and accelerate your learning
              </p>
              
              {user ? (
                <Button 
                  size="lg" 
                  className="bg-primary hover:bg-primary/90 rounded-full px-4 sm:px-8 py-4 sm:py-6 h-auto text-base sm:text-lg shadow-lg shadow-primary/20 w-full sm:w-auto"
                  onClick={handleGetStarted}
                >
                  Go to Study Groups <ArrowRight className="ml-2" />
                </Button>
              ) : (
                <Button 
                  size="lg" 
                  className="bg-primary hover:bg-primary/90 rounded-full px-4 sm:px-8 py-4 sm:py-6 h-auto text-base sm:text-lg shadow-lg shadow-primary/20 w-full sm:w-auto"
                  onClick={handleGetStarted}
                >
                  Try now (It's 100% FREE) <ArrowRight className="ml-2" />
                </Button>
              )}
            </motion.div>
            
            {/* Right side - Group Preview */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="lg:mt-0 mt-8"
            >
              <div className="bg-card rounded-xl p-6 border border-white/10 shadow-lg">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="bg-primary/20 p-2 rounded-full">
                      <Users className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold">Physics Study Group</h3>
                      <p className="text-sm text-muted-foreground">8 members • Active now</p>
                    </div>
                  </div>
                  <span className="bg-green-500/20 text-green-500 px-2 py-1 rounded-full text-xs">Live</span>
                </div>
                
                <div className="space-y-4">
                  <div className="bg-background/50 p-4 rounded-lg">
                    <h4 className="font-medium mb-2 flex items-center gap-2">
                      <BookOpen className="w-4 h-4 text-primary" /> Current Topics
                    </h4>
                    <ul className="space-y-2 text-muted-foreground">
                      <li className="flex items-center gap-2">
                        <div className="w-1 h-1 bg-primary rounded-full"></div>
                        Quantum Mechanics
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-1 h-1 bg-primary rounded-full"></div>
                        Thermodynamics
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-1 h-1 bg-primary rounded-full"></div>
                        Wave Optics
                      </li>
                    </ul>
                  </div>
                  
                  <div className="bg-background/50 p-4 rounded-lg">
                    <h4 className="font-medium mb-2 flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-primary" /> Upcoming Session
                    </h4>
                    <p className="text-muted-foreground">Problem Solving Workshop - Tomorrow, 3 PM</p>
                    <div className="mt-3 flex gap-2">
                      <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center text-xs">AK</div>
                      <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center text-xs">JD</div>
                      <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center text-xs">SR</div>
                      <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center text-xs">+5</div>
                    </div>
                  </div>
                  
                  <div className="bg-background/50 p-4 rounded-lg">
                    <h4 className="font-medium mb-2 flex items-center gap-2">
                      <MessageCircle className="w-4 h-4 text-primary" /> Recent Discussion
                    </h4>
                    <div className="text-sm text-muted-foreground">
                      <p>"Can someone explain the double-slit experiment?"</p>
                      <p className="mt-1 text-xs">- Asked by Sarah, 20 minutes ago</p>
                    </div>
                  </div>
                </div>
                
                <div className="mt-4 flex justify-end">
                  <Button variant="outline" size="sm" className="rounded-full">
                    Preview Group
                  </Button>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Features Grid */}
        <section className="container mx-auto px-4 py-16 relative z-10">
          <motion.h2 
            className="text-3xl font-bold mb-12 text-center"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
          >
            Powerful Features for <span className="text-primary">Collaborative Learning</span>
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard
              icon={<Users className="w-8 h-8" />}
              title="Collaborative Learning"
              description="Form groups with peers and learn together effectively"
              delay={0.1}
            />
            <FeatureCard
              icon={<Share2 className="w-8 h-8" />}
              title="Resource Sharing"
              description="Share study materials, notes, and helpful resources"
              delay={0.2}
            />
            <FeatureCard
              icon={<ChartLine className="w-8 h-8" />}
              title="Progress Tracking"
              description="Monitor group and individual progress over time"
              delay={0.3}
            />
            <FeatureCard
              icon={<MessageCircle className="w-8 h-8" />}
              title="Group Discussions"
              description="Engage in topic-focused discussions and problem-solving"
              delay={0.4}
            />
            <FeatureCard
              icon={<Target className="w-8 h-8" />}
              title="Goal Setting"
              description="Set and achieve group study goals together"
              delay={0.5}
            />
            <FeatureCard
              icon={<BookOpen className="w-8 h-8" />}
              title="Study Sessions"
              description="Schedule and join virtual study sessions"
              delay={0.6}
            />
          </div>
        </section>

        {/* CTA Section */}
        <section className="container mx-auto px-4 py-20 text-center relative z-10">
          <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-3xl p-12 backdrop-blur-sm border border-primary/20">
            <motion.h2 
              className="text-4xl font-bold mb-6"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
            >
              Ready to Study Smarter?
            </motion.h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Join our community of successful students
            </p>
            
            {user ? (
              <Button 
                size="lg" 
                className="bg-primary hover:bg-primary/90 rounded-full px-8 py-6 h-auto text-lg shadow-lg shadow-primary/20"
                onClick={handleGetStarted}
              >
                Go to Dashboard <ArrowRight className="ml-2" />
              </Button>
            ) : (
              <div className="flex justify-center">
                <SignIn />
              </div>
            )}
          </div>
        </section>
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
};

const FeatureCard = ({ icon, title, description, delay = 0 }: { 
  icon: React.ReactNode; 
  title: string; 
  description: string;
  delay?: number;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
      whileHover={{ scale: 1.05, boxShadow: "0 10px 30px -15px rgba(0, 0, 0, 0.3)" }}
      className="bg-card p-6 rounded-xl shadow-md border border-white/5 backdrop-blur-sm"
    >
      <div className="mb-4 text-primary bg-primary/10 p-3 rounded-lg inline-block">{icon}</div>
      <h3 className="text-xl font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground">{description}</p>
    </motion.div>
  );
};

const FloatingElements = () => {
  return (
    <>
      <div className="absolute top-20 left-10 w-64 h-64 bg-primary/5 rounded-full blur-3xl" />
      <div className="absolute top-40 right-20 w-80 h-80 bg-primary/10 rounded-full blur-3xl" />
      <div className="absolute bottom-40 left-20 w-72 h-72 bg-primary/5 rounded-full blur-3xl" />
      <div className="absolute bottom-20 right-10 w-60 h-60 bg-primary/10 rounded-full blur-3xl" />
      
      <motion.div 
        className="absolute top-1/4 left-10 w-6 h-6 bg-primary/30 rounded-full"
        animate={{
          y: [0, 20, 0],
          opacity: [0.5, 1, 0.5]
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div 
        className="absolute top-1/3 right-20 w-4 h-4 bg-primary/20 rounded-full"
        animate={{
          y: [0, -15, 0],
          opacity: [0.3, 0.8, 0.3]
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />
      <motion.div 
        className="absolute bottom-1/4 left-1/4 w-5 h-5 bg-primary/20 rounded-full"
        animate={{
          y: [0, 10, 0],
          opacity: [0.4, 0.9, 0.4]
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 0.5
        }}
      />
    </>
  );
};

export default GroupsLanding; 