import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link, useNavigate } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { ArrowRight, BarChart2, Check, FileText, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, <PERSON><PERSON><PERSON>, <PERSON>r, Bo<PERSON>, ChartBar, Activity, TrendingUp, Bar<PERSON><PERSON> } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { SignIn } from "@/components/SignIn";
import { Header, Footer } from "@/components/shared";
import { Helmet } from "react-helmet";
import { useInView } from "framer-motion";
import { cn } from "@/lib/utils";

const MockTestsLanding = () => {
  const { user, signInWithGoogle } = useSupabaseAuth();
  const navigate = useNavigate();
  const [scrolled, setScrolled] = useState(false);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);

    const handleScroll = () => {
      const offset = window.scrollY;
      if (offset > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const handleGetStarted = () => {
    if (user) {
      navigate('/mock-tests');
    } else {
      signInWithGoogle();
    }
  };

  return (
    <div className="relative w-full overflow-x-hidden bg-[#030014] font-onest">
      <Helmet>
        <title>Mock Test Analytics & Performance Tracking | IsotopeAI</title>
        <meta
          name="description"
          content="Track your mock test performance with detailed analytics, subject-wise breakdowns, and trend analysis. Improve your exam preparation with IsotopeAI's mock test tracking tools."
        />
        <meta
          name="keywords"
          content="mock tests, test analytics, performance tracking, exam preparation, JEE mock tests, NEET mock tests, BITSAT mock tests, test score analysis, subject performance, competitive exam preparation, test improvement, study analytics"
        />
        <meta property="og:title" content="Mock Test Analytics & Performance Tracking | IsotopeAI" />
        <meta property="og:description" content="Track your mock test performance with detailed analytics, subject-wise breakdowns, and trend analysis. Improve your exam preparation with IsotopeAI." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://isotopeai.com/mocktest-landing" />
        <meta name="twitter:card" content="summary_large_image" />
        <link rel="canonical" href="https://isotopeai.com/mocktest-landing" />
      </Helmet>

      {/* Modern Background */}
      <BackgroundElements />

      {/* Header */}
      <AnimatedHeader scrolled={scrolled} />

      {/* Main Content */}
      <main className="flex-grow">
        {/* Hero Section */}
        <HeroSection handleGetStarted={handleGetStarted} user={user} />

        {/* Features Section */}
        <FeaturesSection />

        {/* Analytics Showcase */}
        <AnalyticsShowcaseSection />

        {/* How It Works */}
        <HowItWorksSection />

        {/* CTA Section */}
        <CTASection handleGetStarted={handleGetStarted} user={user} />
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
};

// Background elements with modern design
const BackgroundElements = () => {
  return (
    <>
      {/* Dark gradient background with depth */}
      <div className="fixed inset-0 bg-gradient-radial from-[#0A0A1F] via-[#070722] to-[#030014] opacity-80 z-[-2]" />

      {/* Animated grid pattern */}
      <div className="fixed inset-0 bg-[url('/grid-pattern.svg')] bg-repeat opacity-[0.015] z-[-1]" />

      {/* Animated subtle noise texture */}
      <div
        className="fixed inset-0 z-[-1] opacity-20"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noise'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noise)' opacity='0.4'/%3E%3C/svg%3E")`,
          transform: 'translateZ(0)',
        }}
      />

      {/* Main glow elements */}
      <div className="fixed top-[-10%] right-[0%] w-[600px] h-[600px] bg-[#4338ca]/30 rounded-full blur-[120px] opacity-30 z-[-1]" />
      <div className="fixed bottom-[-15%] left-[-5%] w-[500px] h-[500px] bg-[#3b0764]/30 rounded-full blur-[120px] opacity-30 z-[-1]" />
      <div className="fixed top-[30%] left-[15%] w-[300px] h-[300px] bg-[#4f46e5]/20 rounded-full blur-[120px] opacity-20 z-[-1]" />
    </>
  );
};

const AnimatedHeader = ({ scrolled }: { scrolled: boolean }) => {
  return (
    <motion.header
      className={`fixed w-full z-50 transition-all duration-500 ${scrolled ? 'py-2 backdrop-blur-md' : 'py-4 bg-transparent'}`}
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className={`absolute inset-0 ${scrolled ? 'bg-[#030014]/80 backdrop-blur-xl border-b border-white/10' : 'bg-transparent'}`}></div>
      <Header />
    </motion.header>
  );
};

const HeroSection = ({ handleGetStarted, user }: { handleGetStarted: () => void; user: FirebaseUser | null }) => {
  return (
    <section className="container mx-auto px-4 py-16 pt-36 md:pt-40 relative z-10 min-h-screen flex items-center">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
        {/* Left side - Text content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center lg:text-left"
        >
          <motion.div
            className="inline-block mb-6"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{
              duration: 0.7,
              type: "spring",
              stiffness: 200
            }}
          >
            <div className="bg-gradient-to-br from-indigo-500/30 to-purple-600/30 p-4 rounded-2xl inline-block shadow-inner shadow-white/5">
              <BarChart2 className="w-8 h-8 text-indigo-400" />
            </div>
          </motion.div>

          {/* 100% Free Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="mb-6"
          >
            <span className="bg-gradient-to-r from-emerald-500/20 to-emerald-700/20 text-emerald-400 font-medium px-5 py-2.5 rounded-full inline-flex items-center gap-2 border border-emerald-500/20 shadow-sm shadow-emerald-500/10">
              <Check className="w-4 h-4" />
              Track Your Progress
            </span>
          </motion.div>

          <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold mb-8 leading-tight">
            Analyze Your
            <span className="relative inline-block ml-3 whitespace-nowrap">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 via-purple-400 to-indigo-400">Mock Test Results</span>
              <motion.span
                className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-indigo-400/0 via-purple-400 to-indigo-400/0"
                initial={{ width: 0, left: "50%" }}
                animate={{ width: "100%", left: 0 }}
                transition={{ duration: 1, delay: 1 }}
              ></motion.span>
            </span>
          </h1>

          <p className="text-xl text-white/70 mb-10 max-w-xl mx-auto lg:mx-0 leading-relaxed">
            Track your performance, identify strengths and weaknesses, and improve your scores with our comprehensive mock test analytics
          </p>

          {user ? (
            <motion.div
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                size="lg"
                className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 rounded-xl px-8 py-6 h-auto text-base sm:text-lg shadow-lg shadow-indigo-600/20 font-medium transition-all duration-300"
                onClick={handleGetStarted}
              >
                Go to Mock Tests <ArrowRight className="ml-2" />
              </Button>
            </motion.div>
          ) : (
            <motion.div
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                size="lg"
                className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 rounded-xl px-8 py-6 h-auto text-base sm:text-lg shadow-lg shadow-indigo-600/20 font-medium transition-all duration-300"
                onClick={handleGetStarted}
              >
                Start Tracking Now <ArrowRight className="ml-2" />
              </Button>
            </motion.div>
          )}

          {/* Trust indicators */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 1 }}
            className="mt-12 flex flex-wrap justify-center lg:justify-start gap-4"
          >
            <div className="bg-white/5 backdrop-blur-sm px-4 py-2 rounded-full text-xs text-white/50 border border-white/10">
              Track multiple subjects
            </div>
            <div className="bg-white/5 backdrop-blur-sm px-4 py-2 rounded-full text-xs text-white/50 border border-white/10">
              Visualize performance trends
            </div>
            <div className="bg-white/5 backdrop-blur-sm px-4 py-2 rounded-full text-xs text-white/50 border border-white/10">
              Identify weak areas
            </div>
          </motion.div>
        </motion.div>

        {/* Right side - Analytics Demo */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="lg:mt-0 mt-8"
        >
          <div className="bg-white/5 backdrop-blur-md rounded-2xl border border-white/10 shadow-xl overflow-hidden">
            <div className="p-4 border-b border-white/10 flex items-center">
              <BarChart2 className="h-5 w-5 text-indigo-400 mr-2" />
              <h3 className="font-medium">Mock Test Analytics</h3>
            </div>
            <div className="p-6">
              <img
                src="/mocktest-analytics-preview.png"
                alt="Mock Test Analytics Dashboard"
                className="rounded-lg shadow-lg border border-white/10"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = "https://placehold.co/600x400/0a0a2a/white?text=Mock+Test+Analytics";
                }}
              />
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

const FeaturesSection = () => {
  return (
    <section className="container mx-auto px-4 py-28 relative z-10">
      <div className="relative mb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center"
        >
          <div className="inline-flex items-center mb-3 bg-indigo-500/10 px-4 py-1.5 rounded-full text-white/80 text-sm border border-indigo-500/20">
            <Sparkles className="h-4 w-4 mr-2 text-indigo-400" />
            Key Features
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
            <span className="relative">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 via-purple-400 to-indigo-400">Powerful</span>
              <motion.span
                className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-indigo-400/0 via-purple-400 to-indigo-400/0"
                initial={{ width: 0, left: "50%" }}
                whileInView={{ width: "100%", left: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 1 }}
              ></motion.span>
            </span> Analytics{" "}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 via-purple-400 to-indigo-400">
              Tools
            </span>
          </h2>
          <p className="text-white/60 max-w-2xl mx-auto text-lg">
            Track, analyze, and improve your mock test performance with our comprehensive analytics suite
          </p>
        </motion.div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <FeatureCard
          icon={<BarChart className="w-8 h-8" />}
          title="Subject Performance"
          description="Track your performance across different subjects to identify strengths and weaknesses"
          delay={0.1}
          gradient="from-blue-600/20 via-indigo-600/20 to-indigo-600/10"
        />
        <FeatureCard
          icon={<TrendingUp className="w-8 h-8" />}
          title="Performance Trends"
          description="Visualize your progress over time with detailed trend analysis"
          delay={0.2}
          gradient="from-purple-600/20 via-indigo-600/20 to-purple-600/10"
        />
        <FeatureCard
          icon={<Activity className="w-8 h-8" />}
          title="Subject Strengths"
          description="Identify your strongest and weakest subjects with radar charts"
          delay={0.3}
          gradient="from-indigo-600/20 via-violet-600/20 to-indigo-600/10"
        />
        <FeatureCard
          icon={<PieChart className="w-8 h-8" />}
          title="Score Distribution"
          description="Analyze how your scores are distributed across different subjects"
          delay={0.4}
          gradient="from-blue-600/20 via-indigo-600/20 to-blue-600/10"
        />
        <FeatureCard
          icon={<FileText className="w-8 h-8" />}
          title="Test Management"
          description="Easily add and manage your mock test results with our intuitive interface"
          delay={0.5}
          gradient="from-violet-600/20 via-purple-600/20 to-violet-600/10"
        />
        <FeatureCard
          icon={<ChartBar className="w-8 h-8" />}
          title="Performance Metrics"
          description="Track key performance metrics like average scores, improvement rate, and more"
          delay={0.6}
          gradient="from-purple-600/20 via-indigo-600/20 to-purple-600/10"
        />
      </div>
    </section>
  );
};

const FeatureCard = ({
  icon,
  title,
  description,
  delay,
  gradient
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  delay: number;
  gradient: string;
}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.5, delay }}
      className="group"
    >
      <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 h-full transition-all duration-300 hover:shadow-lg hover:shadow-indigo-500/10 hover:border-indigo-500/30 relative overflow-hidden">
        {/* Gradient background that appears on hover */}
        <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}></div>

        {/* Icon with gradient background */}
        <div className="bg-gradient-to-br from-indigo-500/20 to-purple-600/20 p-3 rounded-xl inline-block mb-4 border border-white/5">
          <div className="text-indigo-400">
            {icon}
          </div>
        </div>

        <h3 className="text-xl font-semibold mb-3 text-white/90">{title}</h3>
        <p className="text-white/60">{description}</p>
      </div>
    </motion.div>
  );
};

const AnalyticsShowcaseSection = () => {
  return (
    <section className="container mx-auto px-4 py-28 relative z-10">
      <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/5 via-transparent to-purple-600/5 z-[-1]"></div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="text-center mb-16"
      >
        <div className="inline-flex items-center mb-3 bg-indigo-500/10 px-4 py-1.5 rounded-full text-white/80 text-sm border border-indigo-500/20">
          <Sparkles className="h-4 w-4 mr-2 text-indigo-400" />
          Visualize Your Progress
        </div>
        <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 via-purple-400 to-indigo-400">
            Comprehensive
          </span>{" "}
          Analytics Dashboard
        </h2>
        <p className="text-white/60 max-w-2xl mx-auto text-lg">
          Get detailed insights into your performance with our intuitive analytics dashboard
        </p>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="relative"
        >
          <div className="bg-white/5 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-xl">
            <h3 className="text-xl font-semibold mb-6 flex items-center">
              <BarChart className="w-5 h-5 text-indigo-400 mr-3" />
              Subject Performance
            </h3>
            <div className="aspect-video bg-gradient-to-br from-indigo-900/30 to-purple-900/30 rounded-lg flex items-center justify-center">
              <img
                src="/subject-performance-chart.png"
                alt="Subject Performance Chart"
                className="rounded-lg max-w-full max-h-full"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = "https://placehold.co/600x400/0a0a2a/white?text=Subject+Performance+Chart";
                }}
              />
            </div>
            <p className="mt-4 text-white/60 text-sm">
              Track your performance across different subjects to identify your strengths and areas for improvement
            </p>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: 20 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="relative"
        >
          <div className="bg-white/5 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-xl">
            <h3 className="text-xl font-semibold mb-6 flex items-center">
              <TrendingUp className="w-5 h-5 text-indigo-400 mr-3" />
              Performance Trends
            </h3>
            <div className="aspect-video bg-gradient-to-br from-indigo-900/30 to-purple-900/30 rounded-lg flex items-center justify-center">
              <img
                src="/performance-trend-chart.png"
                alt="Performance Trend Chart"
                className="rounded-lg max-w-full max-h-full"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = "https://placehold.co/600x400/0a0a2a/white?text=Performance+Trend+Chart";
                }}
              />
            </div>
            <p className="mt-4 text-white/60 text-sm">
              Visualize your progress over time and identify patterns in your performance
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

const HowItWorksSection = () => {
  return (
    <section className="container mx-auto px-4 py-28 relative z-10">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="text-center mb-16"
      >
        <div className="inline-flex items-center mb-3 bg-indigo-500/10 px-4 py-1.5 rounded-full text-white/80 text-sm border border-indigo-500/20">
          <Sparkles className="h-4 w-4 mr-2 text-indigo-400" />
          Simple Process
        </div>
        <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 via-purple-400 to-indigo-400">
            How It Works
          </span>
        </h2>
        <p className="text-white/60 max-w-2xl mx-auto text-lg">
          Start tracking your mock test performance in just a few simple steps
        </p>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <StepCard
          number={1}
          title="Add Your Test Results"
          description="Enter your mock test details including subject-wise marks and test date"
          delay={0.1}
        />
        <StepCard
          number={2}
          title="View Analytics"
          description="Get instant access to comprehensive analytics and visualizations"
          delay={0.2}
        />
        <StepCard
          number={3}
          title="Track Progress"
          description="Monitor your improvement over time and identify areas for focus"
          delay={0.3}
        />
      </div>
    </section>
  );
};

const StepCard = ({
  number,
  title,
  description,
  delay
}: {
  number: number;
  title: string;
  description: string;
  delay: number;
}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.5, delay }}
      className="group"
    >
      <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 h-full transition-all duration-300 hover:shadow-lg hover:shadow-indigo-500/10 hover:border-indigo-500/30 relative overflow-hidden">
        {/* Step number */}
        <div className="absolute -top-6 -left-6 w-20 h-20 bg-gradient-to-br from-indigo-500/20 to-purple-600/20 rounded-full flex items-center justify-center">
          <div className="text-3xl font-bold text-indigo-400 mt-6 ml-6">{number}</div>
        </div>

        <div className="pt-4 pl-4">
          <h3 className="text-xl font-semibold mb-3 text-white/90">{title}</h3>
          <p className="text-white/60">{description}</p>
        </div>
      </div>
    </motion.div>
  );
};

const CTASection = ({ handleGetStarted, user }: { handleGetStarted: () => void; user: FirebaseUser | null }) => {
  return (
    <section className="container mx-auto px-4 py-28 relative z-10">
      <div className="relative">
        {/* Decorative gradient background */}
        <div className="absolute -inset-1 bg-gradient-to-r from-indigo-600/20 via-purple-600/10 to-indigo-600/20 blur-xl opacity-70 rounded-3xl"></div>

        <div className="bg-white/5 backdrop-blur-md rounded-3xl p-8 md:p-12 border border-white/10 shadow-xl relative overflow-hidden">
          {/* Glass effect overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-20"></div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <motion.h2
                className="text-3xl md:text-4xl font-bold mb-6 leading-tight"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                Ready to <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 via-purple-400 to-indigo-400">
                  Track Your Progress
                </span> and Improve Your Scores?
              </motion.h2>

              <motion.p
                className="text-white/70 text-lg mb-8"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.1 }}
              >
                Start tracking your mock test performance today and get valuable insights to help you improve your scores.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="flex flex-wrap gap-4"
              >
                <motion.div
                  whileHover={{ scale: 1.03 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    size="lg"
                    className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 rounded-xl px-8 py-6 h-auto text-base sm:text-lg shadow-lg shadow-indigo-600/20 font-medium transition-all duration-300"
                    onClick={handleGetStarted}
                  >
                    {user ? "Go to Mock Tests" : "Get Started for Free"} <ArrowRight className="ml-2" />
                  </Button>
                </motion.div>
              </motion.div>
            </div>

            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="relative"
            >
              <div className="bg-gradient-to-br from-indigo-900/30 to-purple-900/30 rounded-2xl p-6 border border-white/10">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white/5 rounded-xl p-4 border border-white/10">
                    <div className="flex items-center mb-2">
                      <BarChart className="w-4 h-4 text-indigo-400 mr-2" />
                      <h4 className="text-sm font-medium">Physics</h4>
                    </div>
                    <div className="text-2xl font-bold text-indigo-400">87%</div>
                    <div className="text-xs text-white/50 mt-1">+12% from last test</div>
                  </div>

                  <div className="bg-white/5 rounded-xl p-4 border border-white/10">
                    <div className="flex items-center mb-2">
                      <BarChart className="w-4 h-4 text-purple-400 mr-2" />
                      <h4 className="text-sm font-medium">Chemistry</h4>
                    </div>
                    <div className="text-2xl font-bold text-purple-400">78%</div>
                    <div className="text-xs text-white/50 mt-1">+5% from last test</div>
                  </div>

                  <div className="bg-white/5 rounded-xl p-4 border border-white/10">
                    <div className="flex items-center mb-2">
                      <BarChart className="w-4 h-4 text-blue-400 mr-2" />
                      <h4 className="text-sm font-medium">Mathematics</h4>
                    </div>
                    <div className="text-2xl font-bold text-blue-400">92%</div>
                    <div className="text-xs text-white/50 mt-1">+8% from last test</div>
                  </div>

                  <div className="bg-white/5 rounded-xl p-4 border border-white/10">
                    <div className="flex items-center mb-2">
                      <TrendingUp className="w-4 h-4 text-emerald-400 mr-2" />
                      <h4 className="text-sm font-medium">Overall</h4>
                    </div>
                    <div className="text-2xl font-bold text-emerald-400">85%</div>
                    <div className="text-xs text-white/50 mt-1">+9% from last test</div>
                  </div>
                </div>

                <div className="mt-4 p-4 bg-white/5 rounded-xl border border-white/10">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium">Progress Trend</h4>
                    <span className="text-xs text-emerald-400">+8.5% avg</span>
                  </div>
                  <div className="h-12 flex items-end gap-1">
                    {[65, 72, 68, 75, 79, 82, 85].map((value, index) => (
                      <div
                        key={index}
                        className="bg-gradient-to-t from-indigo-600 to-purple-600 rounded-sm"
                        style={{
                          height: `${value}%`,
                          width: '12%'
                        }}
                      ></div>
                    ))}
                  </div>
                  <div className="flex justify-between mt-2 text-xs text-white/50">
                    <span>Test 1</span>
                    <span>Test 7</span>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default MockTestsLanding;