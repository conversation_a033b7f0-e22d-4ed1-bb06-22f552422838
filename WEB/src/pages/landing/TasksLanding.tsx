import { But<PERSON> } from "@/components/ui/button";
import { Link, useNavigate } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { ArrowRight, CheckSquare, ListTodo, Clock, Tags, Share2, BarChart2, Menu, X, Instagram, Mail, GripVertical, CheckCircle2, <PERSON>rkles } from "lucide-react";
import { useState, useEffect } from "react";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { SignIn } from "@/components/SignIn";
import { Header } from "@/components/shared";
import { Footer } from "@/components/shared";
import { Helmet } from "react-helmet";

const TasksLanding = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeView, setActiveView] = useState<'table' | 'kanban'>('table');
  const [draggedTask, setDraggedTask] = useState<string | null>(null);
  const [dragOverColumn, setDragOverColumn] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<{show: boolean, message: string}>({show: false, message: ''});
  const { user, signInWithGoogle } = useSupabaseAuth();
  const navigate = useNavigate();

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Hide success message after 2 seconds
  useEffect(() => {
    if (successMessage.show) {
      const timer = setTimeout(() => {
        setSuccessMessage({show: false, message: ''});
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [successMessage.show]);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleGetStarted = () => {
    if (user) {
      navigate('/tasks');
    } else {
      signInWithGoogle();
    }
  };

  const handleDragStart = (taskId: string) => {
    setDraggedTask(taskId);
  };

  const handleDragEnd = () => {
    setDraggedTask(null);
    setDragOverColumn(null);
  };

  const handleDragOver = (column: string) => {
    if (draggedTask && dragOverColumn !== column) {
      setDragOverColumn(column);
    }
  };

  const handleDrop = (column: string) => {
    if (draggedTask) {
      // Show success message
      let taskName = '';
      if (draggedTask === 'math') taskName = 'Math Assignment';
      else if (draggedTask === 'physics-lab') taskName = 'Physics Lab Report';
      else if (draggedTask === 'physics-notes') taskName = 'Physics Notes';
      else if (draggedTask === 'chapter5') taskName = 'Chapter 5';
      
      let columnName = '';
      if (column === 'todo') columnName = 'To Do';
      else if (column === 'inprogress') columnName = 'In Progress';
      else if (column === 'completed') columnName = 'Completed';
      
      setSuccessMessage({
        show: true,
        message: `Moved "${taskName}" to ${columnName}`
      });
    }
    handleDragEnd();
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted relative overflow-hidden flex flex-col">
      <Helmet>
        <title>Task Management for Students | Kanban & Table Views | IsotopeAI</title>
        <meta
          name="description"
          content="Organize, prioritize, and track your academic tasks with our powerful task management system. Switch between table and kanban views for effective task organization."
        />
        <meta
          name="keywords"
          content="task management, student tasks, kanban board, table view, task organization, priority labels, due date tracking, academic tasks, study tasks, task visualization, JEE task management, NEET task planning"
        />
        <meta property="og:title" content="Task Management for Students | Kanban & Table Views | IsotopeAI" />
        <meta property="og:description" content="Organize, prioritize, and track your academic tasks with our powerful task management system. Switch between table and kanban views for effective task organization." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://isotopeai.com/tasks-landing" />
        <meta name="twitter:card" content="summary_large_image" />
        <link rel="canonical" href="https://isotopeai.com/tasks-landing" />
      </Helmet>
      
      {/* Header */}
      <Header />
      
      {/* Main Content */}
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="container mx-auto px-4 py-16 pt-28 md:pt-40 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left side - Text content */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center lg:text-left"
            >
              <motion.div 
                className="inline-block mb-4"
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                transition={{ 
                  duration: 0.5,
                  type: "spring",
                  stiffness: 200
                }}
              >
                <div className="bg-primary/20 p-3 rounded-full inline-block">
                  <CheckSquare className="w-8 h-8 text-primary" />
                </div>
              </motion.div>
              
              {/* 100% Free Badge */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="mb-4"
              >
                <span className="bg-green-500/20 text-green-500 font-semibold px-4 py-2 rounded-full inline-flex items-center gap-1 border border-green-500/30">
                  <img src="/favicon.ico" alt="IsotopeAI Logo" className="w-4 h-4" />
                  100% Free Forever
                </span>
              </motion.div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70">
                Manage Your Study Tasks Effectively
              </h1>
              <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto lg:mx-0">
                Organize, prioritize, and track your academic tasks with our powerful task management system
              </p>
              
              {user ? (
                <Button 
                  size="lg" 
                  className="bg-primary hover:bg-primary/90 rounded-full px-4 sm:px-8 py-4 sm:py-6 h-auto text-base sm:text-lg shadow-lg shadow-primary/20 w-full sm:w-auto"
                  onClick={handleGetStarted}
                >
                  Go to Task Manager <ArrowRight className="ml-2" />
                </Button>
              ) : (
                <Button 
                  size="lg" 
                  className="bg-primary hover:bg-primary/90 rounded-full px-4 sm:px-8 py-4 sm:py-6 h-auto text-base sm:text-lg shadow-lg shadow-primary/20 w-full sm:w-auto"
                  onClick={handleGetStarted}
                >
                  Try now (It's 100% FREE) <ArrowRight className="ml-2" />
                </Button>
              )}
            </motion.div>
            
            {/* Right side - Task Demo */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="lg:mt-0 mt-8"
            >
              <div className="bg-card rounded-xl p-6 border border-white/10 shadow-lg">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="bg-primary/20 p-2 rounded-full">
                      <ListTodo className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold">Task Manager</h3>
                      <p className="text-sm text-muted-foreground">Table & Kanban Views</p>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <span className={`px-2 py-1 text-xs rounded-full transition-colors ${activeView === 'table' ? 'bg-primary/20 text-primary' : 'bg-muted/30 text-muted-foreground'}`}>Table</span>
                    <span className={`px-2 py-1 text-xs rounded-full transition-colors ${activeView === 'kanban' ? 'bg-green-500/20 text-green-500' : 'bg-muted/30 text-muted-foreground'}`}>Kanban</span>
                  </div>
                </div>
                
                {/* Tabs for Table/Kanban */}
                <div className="flex border-b border-white/10 mb-4">
                  <button 
                    className={`px-4 py-2 text-sm font-medium transition-colors ${activeView === 'table' ? 'text-primary border-b-2 border-primary' : 'text-muted-foreground hover:text-primary'}`}
                    onClick={() => setActiveView('table')}
                  >
                    Table View
                  </button>
                  <button 
                    className={`px-4 py-2 text-sm font-medium transition-colors ${activeView === 'kanban' ? 'text-primary border-b-2 border-primary' : 'text-muted-foreground hover:text-primary'}`}
                    onClick={() => setActiveView('kanban')}
                  >
                    Kanban Board
                  </button>
                </div>
                
                {/* Table View Demo */}
                {activeView === 'table' && (
                  <motion.div 
                    className="space-y-3 mb-4"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="flex items-center justify-between p-3 bg-background/50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <input type="checkbox" className="w-4 h-4 accent-primary" />
                        <div>
                          <h4 className="font-medium">Complete Math Assignment</h4>
                          <p className="text-xs text-muted-foreground">Due in 2 hours</p>
                        </div>
                      </div>
                      <span className="px-2 py-1 text-xs bg-red-500/20 text-red-500 rounded-full">High</span>
                    </div>
                    
                    <div className="flex items-center justify-between p-3 bg-background/50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <input type="checkbox" className="w-4 h-4 accent-primary" />
                        <div>
                          <h4 className="font-medium">Review Physics Notes</h4>
                          <p className="text-xs text-muted-foreground">Due today at 6 PM</p>
                        </div>
                      </div>
                      <span className="px-2 py-1 text-xs bg-yellow-500/20 text-yellow-500 rounded-full">Medium</span>
                    </div>
                    
                    <div className="flex items-center justify-between p-3 bg-background/50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <input type="checkbox" className="w-4 h-4 accent-primary" checked />
                        <div className="line-through opacity-50">
                          <h4 className="font-medium">Read Chapter 5</h4>
                          <p className="text-xs text-muted-foreground">Completed</p>
                        </div>
                      </div>
                      <span className="px-2 py-1 text-xs bg-primary/20 text-primary rounded-full">Low</span>
                    </div>
                  </motion.div>
                )}
                
                {/* Kanban Board View */}
                {activeView === 'kanban' && (
                  <motion.div 
                    className="mb-4"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="flex gap-2 overflow-x-auto pb-2">
                      {/* To Do Column */}
                      <div 
                        className={`flex-shrink-0 w-[32%] bg-background/50 rounded-lg p-2 transition-colors ${dragOverColumn === 'todo' ? 'bg-primary/10 border border-primary/30' : ''}`}
                        onDragOver={(e) => {
                          e.preventDefault();
                          handleDragOver('todo');
                        }}
                        onDrop={(e) => {
                          e.preventDefault();
                          handleDrop('todo');
                        }}
                      >
                        <div className="text-xs font-medium mb-2 px-1 flex items-center justify-between">
                          <span>To Do</span>
                          <span className="bg-primary/20 text-primary text-xs px-1.5 py-0.5 rounded-full">2</span>
                        </div>
                        
                        <div className="space-y-2">
                          <motion.div 
                            className={`bg-card rounded-md p-2 border shadow-sm cursor-move relative ${draggedTask === 'math' ? 'opacity-50 border-dashed border-primary' : 'border-white/5'}`}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            draggable
                            onDragStart={() => handleDragStart('math')}
                            onDragEnd={handleDragEnd}
                          >
                            <div className="absolute -left-1 top-1/2 -translate-y-1/2 -translate-x-1/2 text-primary/50">
                              <GripVertical size={16} />
                            </div>
                            <h4 className="text-xs font-medium">Complete Math Assignment</h4>
                            <div className="flex items-center justify-between mt-2">
                              <p className="text-[10px] text-muted-foreground">Due in 2 hours</p>
                              <span className="px-1.5 py-0.5 text-[10px] bg-red-500/20 text-red-500 rounded-full">High</span>
                            </div>
                          </motion.div>
                          
                          <motion.div 
                            className={`bg-card rounded-md p-2 border shadow-sm cursor-move relative ${draggedTask === 'physics-lab' ? 'opacity-50 border-dashed border-primary' : 'border-white/5'}`}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            draggable
                            onDragStart={() => handleDragStart('physics-lab')}
                            onDragEnd={handleDragEnd}
                          >
                            <div className="absolute -left-1 top-1/2 -translate-y-1/2 -translate-x-1/2 text-primary/50">
                              <GripVertical size={16} />
                            </div>
                            <h4 className="text-xs font-medium">Physics Lab Report</h4>
                            <div className="flex items-center justify-between mt-2">
                              <p className="text-[10px] text-muted-foreground">Due tomorrow</p>
                              <span className="px-1.5 py-0.5 text-[10px] bg-yellow-500/20 text-yellow-500 rounded-full">Medium</span>
                            </div>
                          </motion.div>
                        </div>
                      </div>
                      
                      {/* In Progress Column */}
                      <div 
                        className={`flex-shrink-0 w-[32%] bg-background/50 rounded-lg p-2 transition-colors ${dragOverColumn === 'inprogress' ? 'bg-primary/10 border border-primary/30' : ''}`}
                        onDragOver={(e) => {
                          e.preventDefault();
                          handleDragOver('inprogress');
                        }}
                        onDrop={(e) => {
                          e.preventDefault();
                          handleDrop('inprogress');
                        }}
                      >
                        <div className="text-xs font-medium mb-2 px-1 flex items-center justify-between">
                          <span>In Progress</span>
                          <span className="bg-primary/20 text-primary text-xs px-1.5 py-0.5 rounded-full">1</span>
                        </div>
                        
                        <div className="space-y-2">
                          <motion.div 
                            className={`bg-card rounded-md p-2 border shadow-sm cursor-move relative ${draggedTask === 'physics-notes' ? 'opacity-50 border-dashed border-primary' : 'border-white/5'}`}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            draggable
                            onDragStart={() => handleDragStart('physics-notes')}
                            onDragEnd={handleDragEnd}
                          >
                            <div className="absolute -left-1 top-1/2 -translate-y-1/2 -translate-x-1/2 text-primary/50">
                              <GripVertical size={16} />
                            </div>
                            <h4 className="text-xs font-medium">Review Physics Notes</h4>
                            <div className="flex items-center justify-between mt-2">
                              <p className="text-[10px] text-muted-foreground">Due today at 6 PM</p>
                              <span className="px-1.5 py-0.5 text-[10px] bg-yellow-500/20 text-yellow-500 rounded-full">Medium</span>
                            </div>
                          </motion.div>
                        </div>
                      </div>
                      
                      {/* Completed Column */}
                      <div 
                        className={`flex-shrink-0 w-[32%] bg-background/50 rounded-lg p-2 transition-colors ${dragOverColumn === 'completed' ? 'bg-primary/10 border border-primary/30' : ''}`}
                        onDragOver={(e) => {
                          e.preventDefault();
                          handleDragOver('completed');
                        }}
                        onDrop={(e) => {
                          e.preventDefault();
                          handleDrop('completed');
                        }}
                      >
                        <div className="text-xs font-medium mb-2 px-1 flex items-center justify-between">
                          <span>Completed</span>
                          <span className="bg-green-500/20 text-green-500 text-xs px-1.5 py-0.5 rounded-full">1</span>
                        </div>
                        
                        <div className="space-y-2">
                          <motion.div 
                            className={`bg-card rounded-md p-2 border shadow-sm cursor-move relative opacity-70 ${draggedTask === 'chapter5' ? 'opacity-50 border-dashed border-primary' : 'border-white/5'}`}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            draggable
                            onDragStart={() => handleDragStart('chapter5')}
                            onDragEnd={handleDragEnd}
                          >
                            <div className="absolute -left-1 top-1/2 -translate-y-1/2 -translate-x-1/2 text-primary/50">
                              <GripVertical size={16} />
                            </div>
                            <h4 className="text-xs font-medium">Read Chapter 5</h4>
                            <div className="flex items-center justify-between mt-2">
                              <p className="text-[10px] text-muted-foreground">Completed</p>
                              <span className="px-1.5 py-0.5 text-[10px] bg-primary/20 text-primary rounded-full">Low</span>
                            </div>
                          </motion.div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Drag Instructions */}
                    <div className="mt-3 text-center">
                      <p className="text-xs text-muted-foreground italic">Drag tasks between columns to change their status</p>
                    </div>
                    
                    {/* Success Message */}
                    <AnimatePresence>
                      {successMessage.show && (
                        <motion.div 
                          className="mt-3 bg-green-500/20 border border-green-500/30 text-green-500 rounded-md p-2 flex items-center justify-center gap-2"
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0 }}
                        >
                          <CheckCircle2 size={14} />
                          <span className="text-xs font-medium">{successMessage.message}</span>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                )}
                
                <div className="mt-4 flex justify-between items-center">
                  <div className="text-xs text-muted-foreground">
                    <span className="font-medium text-primary">3/8</span> tasks completed today
                  </div>
                  <Button variant="outline" size="sm" className="rounded-full text-xs px-3 py-1 h-auto">
                    View All
                  </Button>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Features Grid */}
        <section className="container mx-auto px-4 py-16 relative z-10">
          <motion.h2 
            className="text-3xl font-bold mb-12 text-center"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
          >
            Powerful Features for <span className="text-primary">Task Management</span>
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard
              icon={<ListTodo className="w-8 h-8" />}
              title="Table View"
              description="Organize tasks in a clean, sortable table with priority indicators"
              delay={0.1}
            />
            <FeatureCard
              icon={<CheckSquare className="w-8 h-8" />}
              title="Kanban Board"
              description="Visualize your workflow with drag-and-drop task management"
              delay={0.2}
            />
            <FeatureCard
              icon={<Tags className="w-8 h-8" />}
              title="Priority Labels"
              description="Categorize tasks with customizable priority levels"
              delay={0.3}
            />
            <FeatureCard
              icon={<Clock className="w-8 h-8" />}
              title="Due Date Tracking"
              description="Never miss deadlines with clear due date indicators"
              delay={0.4}
            />
            <FeatureCard
              icon={<Share2 className="w-8 h-8" />}
              title="Group Integration"
              description="Share tasks with your study groups for collaborative work"
              delay={0.5}
            />
            <FeatureCard
              icon={<BarChart2 className="w-8 h-8" />}
              title="Progress Analytics"
              description="Track completion rates and productivity patterns over time"
              delay={0.6}
            />
          </div>
        </section>

        {/* Benefits Section */}
        <section className="container mx-auto px-4 py-16 relative z-10">
          <motion.h2 
            className="text-3xl font-bold mb-12 text-center"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
          >
            Key <span className="text-primary">Benefits</span> for Students
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <BenefitCard
              title="Flexible Task Views"
              description="Switch between table and kanban views to manage tasks your way"
              delay={0.1}
            />
            <BenefitCard
              title="Visual Workflow"
              description="See your progress at a glance with intuitive visual organization"
              delay={0.2}
            />
            <BenefitCard
              title="Seamless Integration"
              description="Connect your tasks with other IsotopeAI features for a complete study system"
              delay={0.3}
            />
          </div>
        </section>

        {/* CTA Section */}
        <section className="container mx-auto px-4 py-20 text-center relative z-10">
          <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-3xl p-12 backdrop-blur-sm border border-primary/20">
            <motion.h2 
              className="text-4xl font-bold mb-6"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
            >
              Ready to Visualize Your Tasks?
            </motion.h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Experience the power of dual-view task management with table and kanban boards
            </p>
            
            {user ? (
              <Button 
                size="lg" 
                className="bg-primary hover:bg-primary/90 rounded-full px-8 py-6 h-auto text-lg shadow-lg shadow-primary/20"
                onClick={handleGetStarted}
              >
                Go to Dashboard <ArrowRight className="ml-2" />
              </Button>
            ) : (
              <div className="flex justify-center">
                <SignIn />
              </div>
            )}
          </div>
        </section>
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
};

const FeatureCard = ({ icon, title, description, delay = 0 }: { 
  icon: React.ReactNode; 
  title: string; 
  description: string;
  delay?: number;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
      whileHover={{ scale: 1.05, boxShadow: "0 10px 30px -15px rgba(0, 0, 0, 0.3)" }}
      className="bg-card p-6 rounded-xl shadow-md border border-white/5 backdrop-blur-sm"
    >
      <div className="mb-4 text-primary bg-primary/10 p-3 rounded-lg inline-block">{icon}</div>
      <h3 className="text-xl font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground">{description}</p>
    </motion.div>
  );
};

const BenefitCard = ({ title, description, delay = 0 }: { 
  title: string; 
  description: string;
  delay?: number;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
      whileHover={{ scale: 1.05, boxShadow: "0 10px 30px -15px rgba(0, 0, 0, 0.3)" }}
      className="bg-card p-6 rounded-xl shadow-md border border-white/5 backdrop-blur-sm text-center"
    >
      <h3 className="text-xl font-semibold mb-3">{title}</h3>
      <p className="text-muted-foreground">{description}</p>
    </motion.div>
  );
};

const FloatingElements = () => {
  return (
    <>
      <div className="absolute top-20 left-10 w-64 h-64 bg-primary/5 rounded-full blur-3xl" />
      <div className="absolute top-40 right-20 w-80 h-80 bg-primary/10 rounded-full blur-3xl" />
      <div className="absolute bottom-40 left-20 w-72 h-72 bg-primary/5 rounded-full blur-3xl" />
      <div className="absolute bottom-20 right-10 w-60 h-60 bg-primary/10 rounded-full blur-3xl" />
      
      <motion.div 
        className="absolute top-1/4 left-10 w-6 h-6 bg-primary/30 rounded-full"
        animate={{
          y: [0, 20, 0],
          opacity: [0.5, 1, 0.5]
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div 
        className="absolute top-1/3 right-20 w-4 h-4 bg-primary/20 rounded-full"
        animate={{
          y: [0, -15, 0],
          opacity: [0.3, 0.8, 0.3]
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />
      <motion.div 
        className="absolute bottom-1/4 left-1/4 w-5 h-5 bg-primary/20 rounded-full"
        animate={{
          y: [0, 10, 0],
          opacity: [0.4, 0.9, 0.4]
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 0.5
        }}
      />
    </>
  );
};

export default TasksLanding; 