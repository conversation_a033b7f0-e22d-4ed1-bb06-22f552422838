import { create } from 'zustand'

export interface Message {
  id: string
  content: string
  senderId: string
  timestamp: number
  groupId: string
}

export interface ChatGroup {
  id: string
  name: string
  members: string[] | null
  createdAt: string | null
  createdBy: string
  owner_id?: string | null
  inviteCode?: string | null
  invite_code?: string | null
  isPublic?: boolean | null
  description?: string | null
  lastMessage?: Message
  last_message?: any
  last_activity?: string | null
  updated_at?: string | null
}

interface ChatState {
  activeGroupId: string | null
  groups: ChatGroup[]
  messages: { [groupId: string]: Message[] }
  setActiveGroupId: (groupId: string) => void
  addGroup: (group: ChatGroup) => void
  removeGroup: (groupId: string) => void
  addMessage: (message: Message) => void
  setGroups: (groups: ChatGroup[]) => void
  setMessages: (groupId: string, messages: Message[]) => void
}

export const useChatStore = create<ChatState>((set) => ({
  activeGroupId: null,
  groups: [],
  messages: {},
  setActiveGroupId: (groupId) => set({ activeGroupId: groupId }),
  addGroup: (group) =>
    set((state) => ({ groups: [...state.groups, group] })),
  removeGroup: (groupId) =>
    set((state) => ({
      groups: state.groups.filter((g) => g.id !== groupId),
      messages: Object.fromEntries(
        Object.entries(state.messages).filter(([key]) => key !== groupId)
      ),
    })),
  addMessage: (message) =>
    set((state) => ({
      messages: {
        ...state.messages,
        [message.groupId]: [
          ...(state.messages[message.groupId] || []),
          message,
        ],
      },
    })),
  setGroups: (groups) => set({ groups }),
  setMessages: (groupId, messages) =>
    set((state) => ({
      messages: { ...state.messages, [groupId]: messages },
    })),
})) 