import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface Badge {
  id: string
  name: string
  description: string
  imageUrl: string
  unlockedAt?: number
}

export interface UserProgress {
  totalStudyHours: number
  currentStreak: number
  longestStreak: number
  lastLoginDate: string | null
  badges: Badge[]
  level: number
  experience: number
}

interface GamificationState {
  progress: UserProgress
  updateStudyHours: (hours: number) => void
  updateStreak: () => void
  addBadge: (badge: Badge) => void
  setProgress: (progress: UserProgress) => void
  addExperience: (exp: number) => void
}

const EXPERIENCE_PER_LEVEL = 100

export const useGamificationStore = create<GamificationState>()(
  persist(
    (set) => ({
      progress: {
        totalStudyHours: 0,
        currentStreak: 0,
        longestStreak: 0,
        lastLoginDate: null,
        badges: [],
        level: 1,
        experience: 0,
      },
      updateStudyHours: (hours) =>
        set((state) => ({
          progress: {
            ...state.progress,
            totalStudyHours: state.progress.totalStudyHours + hours,
          },
        })),
      updateStreak: () =>
        set((state) => {
          const today = new Date().toISOString().split('T')[0]
          const lastLogin = state.progress.lastLoginDate
          
          if (!lastLogin) {
            return {
              progress: {
                ...state.progress,
                currentStreak: 1,
                longestStreak: 1,
                lastLoginDate: today,
              },
            }
          }

          const lastLoginDate = new Date(lastLogin)
          const yesterday = new Date()
          yesterday.setDate(yesterday.getDate() - 1)
          
          if (lastLogin === today) {
            return state
          }

          const isConsecutiveDay = lastLoginDate.toISOString().split('T')[0] === yesterday.toISOString().split('T')[0]
          const newStreak = isConsecutiveDay ? state.progress.currentStreak + 1 : 1
          
          return {
            progress: {
              ...state.progress,
              currentStreak: newStreak,
              longestStreak: Math.max(newStreak, state.progress.longestStreak),
              lastLoginDate: today,
            },
          }
        }),
      addBadge: (badge) =>
        set((state) => ({
          progress: {
            ...state.progress,
            badges: [...state.progress.badges, { ...badge, unlockedAt: Date.now() }],
          },
        })),
      setProgress: (progress) => set({ progress }),
      addExperience: (exp) =>
        set((state) => {
          const currentExp = state.progress.experience + exp
          const levelsGained = Math.floor(currentExp / EXPERIENCE_PER_LEVEL)
          const remainingExp = currentExp % EXPERIENCE_PER_LEVEL
          
          return {
            progress: {
              ...state.progress,
              level: state.progress.level + levelsGained,
              experience: remainingExp,
            },
          }
        }),
    }),
    {
      name: 'gamification-storage',
    }
  )
) 