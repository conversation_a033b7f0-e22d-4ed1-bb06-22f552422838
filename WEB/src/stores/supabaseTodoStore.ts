import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  getTodos,
  createTodo,
  updateTodo,
  deleteTodo,
  subscribeToTodos,
} from '../utils/supabase';
import { TodoState, TodoItem, TodoColumn, TodoBoard } from '../types/todo';
import { v4 as uuidv4 } from 'uuid';

// Define enhanced drag source/destination type
interface EnhancedDragItem {
  droppableId: string;
  index: number;
  taskId?: string;
}

// Initial state with default columns
const initialBoard: TodoBoard = {
  tasks: {},
  columns: {
    'column-1': {
      id: 'column-1',
      title: 'Todo',
      taskIds: [],
    },
    'column-2': {
      id: 'column-2',
      title: 'In Progress',
      taskIds: [],
    },
    'column-3': {
      id: 'column-3',
      title: 'Done',
      taskIds: [],
    },
  },
  columnOrder: ['column-1', 'column-2', 'column-3'],
};

const initialState: TodoState = {
  board: initialBoard,
  loading: false,
  error: null,
};

export const useSupabaseTodoStore = create<
  TodoState & {
    // Actions
    fetchTodos: (userId: string, groupId?: string) => void;
    addTask: (task: Omit<TodoItem, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
    updateTask: (taskId: string, updates: Partial<TodoItem>) => Promise<void>;
    deleteTask: (taskId: string) => Promise<void>;
    moveTask: (source: EnhancedDragItem, destination: EnhancedDragItem) => Promise<void>;
    addColumn: (title: string) => Promise<void>;
    updateColumn: (columnId: string, title: string) => Promise<void>;
    deleteColumn: (columnId: string) => Promise<void>;
    reset: () => void;
  }
>(
  persist(
    (set, get) => ({
      ...initialState,

      fetchTodos: async (userId, groupId) => {
        set({ loading: true, error: null });

        try {
          // Fetch todos from Supabase
          const todos = await getTodos(userId);
          
          const tasks: Record<string, TodoItem> = {};
          const columns = JSON.parse(JSON.stringify(get().board.columns));

          // Reset taskIds in all columns
          Object.keys(columns).forEach(columnId => {
            columns[columnId].taskIds = [];
          });

          todos.forEach((todo) => {
            // Convert Supabase todo to TodoItem format
            const task: TodoItem = {
              id: todo.id,
              title: todo.title,
              description: todo.description || '',
              priority: todo.priority as 'low' | 'medium' | 'high' || 'medium',
              createdAt: todo.createdAt,
              updatedAt: todo.updatedAt,
              createdBy: todo.createdBy,
              dueDate: todo.dueDate || undefined,
              assignedTo: todo.assignedTo || undefined,
              assignedToName: todo.assignedToName || undefined,
              assignedToPhotoURL: todo.assignedToPhotoUrl || undefined,
            };

            tasks[task.id] = task;

            // Find which column this task belongs to
            const columnId = todo.column_id || todo.columnId || 'column-1';

            if (columns[columnId]) {
              columns[columnId].taskIds.push(task.id);
            } else {
              // If column doesn't exist, move to Todo
              columns['column-1'].taskIds.push(task.id);
            }
          });

          console.log('Loaded tasks from Supabase:', tasks);
          console.log('Columns state:', columns);

          set({
            board: {
              tasks,
              columns,
              columnOrder: get().board.columnOrder,
            },
            loading: false,
          });

          // Set up real-time subscription
          const subscription = subscribeToTodos(userId, (payload) => {
            console.log('Real-time todo update:', payload);
            // Refetch todos when changes occur
            get().fetchTodos(userId, groupId);
          });

          // Store subscription for cleanup
          (get() as any).subscription = subscription;

        } catch (error: any) {
          console.error('Error fetching todos:', error);
          set({ error: error.message, loading: false });
        }
      },

      addTask: async (taskData) => {
        try {
          const taskId = uuidv4();
          const now = Date.now();

          // Create a clean task object for Supabase
          const newTodoData = {
            id: taskId,
            title: taskData.title,
            description: taskData.description || '',
            priority: taskData.priority || 'medium',
            createdAt: now,
            updatedAt: now,
            createdBy: taskData.createdBy,
            dueDate: taskData.dueDate || null,
            assignedTo: taskData.assignedTo || null,
            assignedToName: taskData.assignedToName || null,
            assignedToPhotoUrl: taskData.assignedToPhotoURL || null,
            column_id: 'column-1', // Default to Todo column
            columnId: 'column-1', // Keep both for compatibility
            groupId: null,
          };

          console.log('Adding task to Supabase:', newTodoData);
          await createTodo(newTodoData);

          // Update local state for immediate UI update
          const board = JSON.parse(JSON.stringify(get().board));
          const newTask: TodoItem = {
            id: taskId,
            title: taskData.title,
            description: taskData.description || '',
            priority: taskData.priority || 'medium',
            createdAt: now,
            updatedAt: now,
            createdBy: taskData.createdBy,
            dueDate: taskData.dueDate,
            assignedTo: taskData.assignedTo,
            assignedToName: taskData.assignedToName,
            assignedToPhotoURL: taskData.assignedToPhotoURL,
          };

          board.tasks[taskId] = newTask;
          board.columns['column-1'].taskIds.push(taskId);
          set({ board });

          return Promise.resolve();
        } catch (error: any) {
          console.error('Error adding task:', error);
          set({ error: error.message });
          return Promise.reject(error);
        }
      },

      updateTask: async (taskId, updates) => {
        try {
          const supabaseUpdates = {
            ...updates,
            updatedAt: Date.now(),
            assignedToPhotoUrl: updates.assignedToPhotoURL, // Map field name
          };

          await updateTodo(taskId, supabaseUpdates);
          return Promise.resolve();
        } catch (error: any) {
          set({ error: error.message });
          return Promise.reject(error);
        }
      },

      deleteTask: async (taskId) => {
        try {
          await deleteTodo(taskId);
          return Promise.resolve();
        } catch (error: any) {
          set({ error: error.message });
          return Promise.reject(error);
        }
      },

      moveTask: async (source, destination) => {
        try {
          const { board } = get();
          const sourceColumn = board.columns[source.droppableId];
          const destColumn = board.columns[destination.droppableId];
          const taskId = sourceColumn.taskIds[source.index];

          // Update the task's column in Supabase
          await updateTodo(taskId, {
            column_id: destination.droppableId,
            columnId: destination.droppableId, // Keep both for compatibility
            updatedAt: Date.now(),
          });

          // Update local state immediately
          const newBoard = JSON.parse(JSON.stringify(board));
          
          // Remove from source
          newBoard.columns[source.droppableId].taskIds.splice(source.index, 1);
          
          // Add to destination
          newBoard.columns[destination.droppableId].taskIds.splice(destination.index, 0, taskId);
          
          set({ board: newBoard });

          return Promise.resolve();
        } catch (error: any) {
          set({ error: error.message });
          return Promise.reject(error);
        }
      },

      addColumn: async (title) => {
        try {
          const columnId = `column-${Date.now()}`;
          const board = JSON.parse(JSON.stringify(get().board));
          
          board.columns[columnId] = {
            id: columnId,
            title,
            taskIds: [],
          };
          board.columnOrder.push(columnId);
          
          set({ board });
          return Promise.resolve();
        } catch (error: any) {
          set({ error: error.message });
          return Promise.reject(error);
        }
      },

      updateColumn: async (columnId, title) => {
        try {
          const board = JSON.parse(JSON.stringify(get().board));
          board.columns[columnId].title = title;
          set({ board });
          return Promise.resolve();
        } catch (error: any) {
          set({ error: error.message });
          return Promise.reject(error);
        }
      },

      deleteColumn: async (columnId) => {
        try {
          const board = JSON.parse(JSON.stringify(get().board));
          
          // Move all tasks from deleted column to Todo column
          const tasksToMove = board.columns[columnId].taskIds;
          for (const taskId of tasksToMove) {
            await updateTodo(taskId, {
              column_id: 'column-1',
              columnId: 'column-1',
              updatedAt: Date.now(),
            });
          }
          
          // Add tasks to Todo column
          board.columns['column-1'].taskIds.push(...tasksToMove);
          
          // Remove column
          delete board.columns[columnId];
          board.columnOrder = board.columnOrder.filter(id => id !== columnId);
          
          set({ board });
          return Promise.resolve();
        } catch (error: any) {
          set({ error: error.message });
          return Promise.reject(error);
        }
      },

      reset: () => {
        // Clean up subscription
        const subscription = (get() as any).subscription;
        if (subscription) {
          subscription.unsubscribe();
        }
        set(initialState);
      },
    }),
    {
      name: 'supabase-todo-board-storage',
      partialize: (state) => ({
        // Only persist the columns and columnOrder, not the tasks
        board: {
          columns: state.board.columns,
          columnOrder: state.board.columnOrder,
          tasks: {} // Don't persist tasks as they come from Supabase
        }
      }),
    }
  )
);
