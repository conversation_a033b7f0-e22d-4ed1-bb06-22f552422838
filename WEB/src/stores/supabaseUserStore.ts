import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User } from '@supabase/supabase-js';
import { Database } from '../integrations/supabase/types';

// Type definitions
type UserRow = Database['public']['Tables']['users']['Row'];

interface UserProfile extends UserRow {
  // Add any additional computed properties if needed
}

interface UserState {
  user: User | null;
  userProfile: UserProfile | null;
  isLoading: boolean;
  lastFetched: number | null;
  setUser: (user: User | null) => void;
  setUserProfile: (profile: UserProfile | null) => void;
  setLoading: (loading: boolean) => void;
  setLastFetched: (timestamp: number | null) => void;
  updateUserProfile: (updates: Partial<UserProfile>) => void;
  reset: () => void;
}

const initialState = {
  user: null,
  userProfile: null,
  isLoading: true,
  lastFetched: null,
};

export const useSupabaseUserStore = create<UserState>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      setUser: (user) => {
        console.log('Setting user in store:', user?.id);
        set({ user });
      },
      
      setUserProfile: (profile) => {
        console.log('Setting user profile in store:', profile?.id);
        set({ 
          userProfile: profile, 
          lastFetched: profile ? Date.now() : null,
          isLoading: false 
        });
      },
      
      setLoading: (loading) => set({ isLoading: loading }),
      
      setLastFetched: (timestamp) => set({ lastFetched: timestamp }),
      
      updateUserProfile: (updates) => {
        const currentProfile = get().userProfile;
        if (currentProfile) {
          const updatedProfile = {
            ...currentProfile,
            ...updates,
            updated_at: new Date().toISOString(),
          };
          set({ 
            userProfile: updatedProfile,
            lastFetched: Date.now()
          });
        }
      },
      
      reset: () => {
        console.log('Resetting user store');
        set(initialState);
      },
    }),
    {
      name: 'supabase-user-storage',
      // Only persist user profile data, not the auth user object
      partialize: (state) => ({
        userProfile: state.userProfile,
        lastFetched: state.lastFetched,
      }),
    }
  )
);
