import { Subject } from "../components/productivity/SubjectManager";

export interface SubjectMarks {
  subject: string;
  subjectColor?: string;
  marksObtained: number;
  totalMarks: number;
}

export interface MockTest {
  id: string;          // Unique ID for the mock test
  name: string;        // Name of the mock test
  date: string;        // Date of the test in YYYY-MM-DD format
  subjectMarks: SubjectMarks[]; // Array of subjects with their marks
  totalMarksObtained: number; // Total marks obtained across all subjects
  totalMarks: number;  // Total marks across all subjects
  notes?: string;      // Optional notes about the test
  createdAt: string;   // Timestamp when the test was created
  userId: string;      // User ID who created the test
}

export interface MockTestAnalytics {
  totalTests: number;
  averageScore: number;
  highestScore: {
    testId: string;
    testName: string;
    score: number;
    percentage: number;
  };
  lowestScore: {
    testId: string;
    testName: string;
    score: number;
    percentage: number;
  };
  subjectPerformance: {
    [subject: string]: {
      totalTests: number;
      averageScore: number;
      averagePercentage: number;
    }
  };
  recentTests: MockTest[];
} 