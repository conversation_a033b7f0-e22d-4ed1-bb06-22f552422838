export interface TodoItem {
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  createdAt: number;
  updatedAt: number;
  dueDate?: number;
  assignedTo?: string;
  assignedToName?: string;
  assignedToPhotoURL?: string;
  createdBy: string;
  groupId?: string;
}

export interface TodoColumn {
  id: string;
  title: string;
  taskIds: string[];
}

export interface TodoBoard {
  tasks: Record<string, TodoItem>;
  columns: Record<string, TodoColumn>;
  columnOrder: string[];
}

export interface TodoState {
  board: TodoBoard;
  loading: boolean;
  error: string | null;
} 