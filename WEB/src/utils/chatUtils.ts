/**
 * Generates a deterministic chat ID based on the question content.
 * This ensures that the same question always gets the same URL.
 * 
 * @param questionText The question text to hash
 * @returns A deterministic ID based on the content hash
 */
export const generateChatId = async (questionText: string): Promise<string> => {
  try {
    // Normalize the question text: trim, lowercase
    const normalizedText = questionText.trim().toLowerCase();
    
    // Create a hash of the text to use as ID
    const encoder = new TextEncoder();
    const data = encoder.encode(normalizedText);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    
    // Use only first 16 bytes (32 hex chars) for a reasonably sized ID
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('').substring(0, 32);
  } catch (error) {
    console.error('Error generating chat ID:', error);
    // Fallback to a timestamp-based ID if crypto API fails
    return `fallback-${Date.now().toString(36)}`;
  }
}; 