/**
 * Pings search engines to notify them about a new share URL
 * This helps get content indexed quickly
 * @param shareUrl The URL of the shared content to submit to search engines
 */
export const pingSearchEngines = async (shareUrl: string): Promise<void> => {
  try {
    // Only run in production environment
    if (process.env.NODE_ENV !== 'production') {
      console.log('Search engine ping skipped in development environment');
      return;
    }
    
    // Encode the URL for submission
    const encodedUrl = encodeURIComponent(shareUrl);
    
    // List of search engine ping endpoints (Google and Bing)
    const pingUrls = [
      `https://www.google.com/ping?sitemap=${encodedUrl}`,
      `https://www.bing.com/ping?sitemap=${encodedUrl}`
    ];
    
    // Send the ping requests
    const pingPromises = pingUrls.map(url => 
      fetch(url, { method: 'GET', mode: 'no-cors' })
        .then(() => console.log(`Pinged search engine: ${url}`))
        .catch(err => console.error(`Failed to ping search engine: ${url}`, err))
    );
    
    // Wait for all pings to complete
    await Promise.all(pingPromises);
    
    console.log('Successfully pinged search engines with new share URL:', shareUrl);
  } catch (error) {
    console.error('Error pinging search engines:', error);
    // Silent fail - don't disrupt user experience if pings fail
  }
};

/**
 * Generates structured data for SEO-friendly content
 * @param data The chat data to create structured data for
 * @returns A JSON-LD string for embedding in the page
 */
export const generateStructuredData = (data: {
  title: string;
  description: string;
  url: string;
  authorName: string;
  datePublished: string;
  content?: string;
}): string => {
  const faqData = data.content ? {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": data.title,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": data.content
        }
      }
    ]
  } : null;
  
  const articleData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": data.title,
    "description": data.description,
    "author": {
      "@type": "Person",
      "name": data.authorName
    },
    "publisher": {
      "@type": "Organization",
      "name": "IsotopeAI",
      "logo": {
        "@type": "ImageObject",
        "url": `${window.location.origin}/icon-192x192.png`
      }
    },
    "datePublished": data.datePublished,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": data.url
    }
  };
  
  return JSON.stringify(faqData ? [articleData, faqData] : articleData);
}; 