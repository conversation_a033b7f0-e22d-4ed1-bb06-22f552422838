// Helper function to validate time and ensure it's non-negative and within a reasonable range
const validateTime = (seconds: number): number => {
  if (typeof seconds !== 'number' || isNaN(seconds) || seconds < 0) return 0;
  const MAX_REASONABLE_TIME = 24 * 60 * 60 * 365 * 10; // 10 years in seconds, a very large but finite number
  return Math.min(seconds, MAX_REASONABLE_TIME);
};

/**
 * Formats a total number of seconds into a human-readable string (e.g., "1h 30m 15s").
 *
 * @param totalSeconds The total duration in seconds.
 * @returns A formatted string representing the duration.
 */
export const formatDuration = (totalSeconds: number): string => {
  const validatedSeconds = validateTime(totalSeconds);

  const hours = Math.floor(validatedSeconds / 3600);
  const minutes = Math.floor((validatedSeconds % 3600) / 60);
  const seconds = Math.floor(validatedSeconds % 60);

  const parts: string[] = [];

  if (hours > 0) {
    parts.push(`${hours}h`);
  }
  // Always show minutes if there are hours, or if minutes exist and no hours
  if (minutes > 0 || (hours > 0 && seconds > 0)) {
    parts.push(`${minutes}m`);
  }
  // Always show seconds if duration is less than a minute, or if it's 0
  if (seconds > 0 || (hours === 0 && minutes === 0 && parts.length === 0)) {
    parts.push(`${seconds}s`);
  }

  if (parts.length === 0) {
    return `0s`; // Fallback for 0 duration if no parts were added
  }

  return parts.join(' ');
};

/**
 * Formats a total number of seconds into a compact time string (e.g., "01:30:15" or "30:15").
 *
 * @param totalSeconds The total duration in seconds.
 * @returns A formatted string representing the time.
 */
export const formatTime = (totalSeconds: number): string => {
  const validatedSeconds = validateTime(totalSeconds);

  const hours = Math.floor(validatedSeconds / 3600);
  const minutes = Math.floor((validatedSeconds % 3600) / 60);
  const seconds = Math.floor(validatedSeconds % 60);

  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

/**
 * Converts seconds to hours as a decimal number.
 *
 * @param totalSeconds The total duration in seconds.
 * @returns The duration in hours as a decimal.
 */
export const secondsToHours = (totalSeconds: number): number => {
  const validatedSeconds = validateTime(totalSeconds);
  return validatedSeconds / 3600;
};

/**
 * Converts seconds to minutes as a decimal number.
 *
 * @param totalSeconds The total duration in seconds.
 * @returns The duration in minutes as a decimal.
 */
export const secondsToMinutes = (totalSeconds: number): number => {
  const validatedSeconds = validateTime(totalSeconds);
  return validatedSeconds / 60;
};

/**
 * Formats seconds as hours with one decimal place (e.g., "2.5h").
 *
 * @param totalSeconds The total duration in seconds.
 * @returns A formatted string representing hours.
 */
export const formatHours = (totalSeconds: number): string => {
  const hours = secondsToHours(totalSeconds);
  return `${hours.toFixed(1)}h`;
};
